<?php
// Script pour ajouter des entrées de test dans la table activity_logs

// Activer l'affichage des erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Inclure le fichier de configuration
require_once __DIR__ . '/app/config/config.php';

// Se connecter à la base de données
try {
    $pdo = getDbConnection();
    echo "Connexion à la base de données réussie.<br>";
} catch (Exception $e) {
    die("Erreur de connexion à la base de données: " . $e->getMessage());
}

// Vérifier si la table activity_logs existe
$tableExists = false;
try {
    $result = $pdo->query("SHOW TABLES LIKE 'activity_logs'");
    $tableExists = $result->rowCount() > 0;
} catch (Exception $e) {
    die("Erreur lors de la vérification de la table: " . $e->getMessage());
}

// Créer la table si elle n'existe pas
if (!$tableExists) {
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS activity_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL,
                action ENUM('create', 'update', 'delete', 'delete_failed', 'reset', 'login', 'logout', 'test') NOT NULL,
                entity_type ENUM('avocat', 'affaire', 'database', 'user') NOT NULL,
                entity_id INT,
                details TEXT,
                ip_address VARCHAR(45),
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB;
        ");
        echo "La table activity_logs a été créée.<br>";
    } catch (Exception $e) {
        die("Erreur lors de la création de la table: " . $e->getMessage());
    }
}

// Ajouter des entrées de test
$testEntries = [
    [
        'username' => 'admin',
        'action' => 'login',
        'entity_type' => 'user',
        'entity_id' => 1,
        'details' => 'Connexion réussie',
        'ip_address' => '127.0.0.1'
    ],
    [
        'username' => 'admin',
        'action' => 'create',
        'entity_type' => 'avocat',
        'entity_id' => 1,
        'details' => 'Création de l\'avocat: Me Dupont',
        'ip_address' => '127.0.0.1'
    ],
    [
        'username' => 'admin',
        'action' => 'update',
        'entity_type' => 'avocat',
        'entity_id' => 1,
        'details' => 'Mise à jour de l\'avocat: Me Dupont',
        'ip_address' => '127.0.0.1'
    ],
    [
        'username' => 'admin',
        'action' => 'create',
        'entity_type' => 'affaire',
        'entity_id' => 1,
        'details' => 'Création de l\'affaire: Dossier 123',
        'ip_address' => '127.0.0.1'
    ],
    [
        'username' => 'admin',
        'action' => 'update',
        'entity_type' => 'affaire',
        'entity_id' => 1,
        'details' => 'Mise à jour de l\'affaire: Dossier 123',
        'ip_address' => '127.0.0.1'
    ],
    [
        'username' => 'user1',
        'action' => 'login',
        'entity_type' => 'user',
        'entity_id' => 2,
        'details' => 'Connexion réussie',
        'ip_address' => '127.0.0.1'
    ],
    [
        'username' => 'user1',
        'action' => 'create',
        'entity_type' => 'affaire',
        'entity_id' => 2,
        'details' => 'Création de l\'affaire: Dossier 456',
        'ip_address' => '127.0.0.1'
    ],
    [
        'username' => 'admin',
        'action' => 'delete',
        'entity_type' => 'affaire',
        'entity_id' => 2,
        'details' => 'Suppression de l\'affaire: Dossier 456',
        'ip_address' => '127.0.0.1'
    ],
    [
        'username' => 'user1',
        'action' => 'logout',
        'entity_type' => 'user',
        'entity_id' => 2,
        'details' => 'Déconnexion',
        'ip_address' => '127.0.0.1'
    ],
    [
        'username' => 'admin',
        'action' => 'logout',
        'entity_type' => 'user',
        'entity_id' => 1,
        'details' => 'Déconnexion',
        'ip_address' => '127.0.0.1'
    ]
];

// Insérer les entrées de test
$stmt = $pdo->prepare("INSERT INTO activity_logs (username, action, entity_type, entity_id, details, ip_address) VALUES (?, ?, ?, ?, ?, ?)");

$insertedCount = 0;
foreach ($testEntries as $entry) {
    try {
        $stmt->execute([
            $entry['username'],
            $entry['action'],
            $entry['entity_type'],
            $entry['entity_id'],
            $entry['details'],
            $entry['ip_address']
        ]);
        $insertedCount++;
    } catch (Exception $e) {
        echo "Erreur lors de l'insertion de l'entrée: " . $e->getMessage() . "<br>";
    }
}

echo "Nombre d'entrées insérées: " . $insertedCount . "<br>";

// Vérifier le nombre total d'entrées
try {
    $count = $pdo->query("SELECT COUNT(*) FROM activity_logs")->fetchColumn();
    echo "Nombre total d'entrées dans la table: " . $count . "<br>";
} catch (Exception $e) {
    echo "Erreur lors du comptage des entrées: " . $e->getMessage() . "<br>";
}

echo "<p>Vous pouvez maintenant <a href='views/admin/admin_logs.html'>consulter le journal d'activité</a>.</p>";

<?php
// Script pour corriger la structure de la table users
require_once 'config.php';

// Fonction pour exécuter le script SQL
function executeSqlScript($sqlFile) {
    try {
        // Lire le contenu du fichier SQL
        $sql = file_get_contents($sqlFile);
        
        if (!$sql) {
            die("Erreur: Impossible de lire le fichier SQL.");
        }
        
        // Connexion à MySQL
        $pdo = getDbConnection();
        
        // Exécuter les requêtes SQL
        $pdo->exec($sql);
        
        echo "Structure de la table users corrigée avec succès!<br>";
        return true;
    } catch (PDOException $e) {
        die("Erreur lors de la correction de la structure de la table users: " . $e->getMessage());
    }
}

// Exécuter le script SQL
if (executeSqlScript('fix_users_table.sql')) {
    echo "La structure de la table users a été corrigée avec succès.<br>";
    
    // Vérifier la structure de la table users
    try {
        $pdo = getDbConnection();
        $stmt = $pdo->query("DESCRIBE users");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h2>Nouvelle structure de la table 'users'</h2>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "<td>" . $column['Extra'] . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
    } catch (PDOException $e) {
        echo "Erreur lors de la vérification de la structure de la table: " . $e->getMessage();
    }
    
    echo "<br><a href='init_users.php' class='btn btn-primary'>Initialiser les utilisateurs</a>";
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Correction de la structure de la table users</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #0d6efd;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        .btn {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Correction de la structure de la table users</h1>
        <!-- Les messages PHP seront affichés ici -->
    </div>
</body>
</html>

<?php
// Script pour vérifier et corriger toutes les tables nécessaires
require_once 'config.php';

// Fonction pour vérifier si une table existe
function tableExists($pdo, $table) {
    try {
        $result = $pdo->query("SHOW TABLES LIKE '$table'");
        return $result->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

// Fonction pour vérifier si une colonne existe dans une table
function columnExists($pdo, $table, $column) {
    try {
        $result = $pdo->query("SHOW COLUMNS FROM `$table` LIKE '$column'");
        return $result->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

// Fonction pour créer la table users
function createUsersTable($pdo) {
    $sql = "CREATE TABLE users (
        id INT PRIMARY KEY AUTO_INCREMENT,
        username VARCHAR(50) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        role ENUM('admin', 'user') NOT NULL DEFAULT 'user',
        last_login DATETIME,
        account_locked TINYINT(1) DEFAULT 0,
        locked_until DATETIME DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    $pdo->exec($sql);
    echo "Table 'users' créée avec succès.<br>";
}

// Fonction pour créer la table avocats
function createAvocatsTable($pdo) {
    $sql = "CREATE TABLE avocats (
        id INT PRIMARY KEY AUTO_INCREMENT,
        nom VARCHAR(100) NOT NULL,
        agrement VARCHAR(20) UNIQUE NOT NULL,
        adresse TEXT,
        telephone VARCHAR(20),
        email VARCHAR(100) UNIQUE NOT NULL,
        date_signature DATE NOT NULL,
        validite_contrat DATE NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    $pdo->exec($sql);
    echo "Table 'avocats' créée avec succès.<br>";
}

// Fonction pour créer la table affaires
function createAffairesTable($pdo) {
    $sql = "CREATE TABLE affaires (
        id INT PRIMARY KEY AUTO_INCREMENT,
        numero VARCHAR(20) UNIQUE NOT NULL,
        nature ENUM('social', 'administrative', 'commerciale', 'foncier', 'refere', 'civile', 'penal') NOT NULL,
        objet TEXT NOT NULL,
        parties TEXT,
        juridiction ENUM('tribunal', 'cour', 'cours-supreme', 'c-etat') NOT NULL,
        engagement ENUM('societe', 'tiers') NOT NULL,
        date_engagement DATE NOT NULL,
        date_jugement DATE,
        statut ENUM('en-cours', 'jugee-faveur', 'jugee-tiers') NOT NULL,
        dispositif TEXT,
        montant DECIMAL(15,2),
        maitre INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (maitre) REFERENCES avocats(id)
    )";
    
    $pdo->exec($sql);
    echo "Table 'affaires' créée avec succès.<br>";
}

// Fonction pour créer la table activity_logs
function createActivityLogsTable($pdo) {
    $sql = "CREATE TABLE activity_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL,
        action ENUM('create', 'update', 'delete', 'delete_failed', 'reset', 'login', 'logout') NOT NULL,
        entity_type ENUM('avocat', 'affaire', 'database', 'user') NOT NULL,
        entity_id INT,
        details TEXT,
        ip_address VARCHAR(45),
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    $pdo->exec($sql);
    echo "Table 'activity_logs' créée avec succès.<br>";
}

// Fonction pour créer la table password_resets
function createPasswordResetsTable($pdo) {
    $sql = "CREATE TABLE password_resets (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        reset_code VARCHAR(10) NOT NULL,
        reset_token VARCHAR(100) NOT NULL,
        expires_at DATETIME NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    
    $pdo->exec($sql);
    echo "Table 'password_resets' créée avec succès.<br>";
}

// Fonction pour créer la table login_attempts
function createLoginAttemptsTable($pdo) {
    $sql = "CREATE TABLE login_attempts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        ip_address VARCHAR(45) NOT NULL,
        attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    
    $pdo->exec($sql);
    echo "Table 'login_attempts' créée avec succès.<br>";
}

try {
    $pdo = getDbConnection();
    
    // Vérifier et créer les tables si elles n'existent pas
    $tables = [
        'users' => 'createUsersTable',
        'avocats' => 'createAvocatsTable',
        'affaires' => 'createAffairesTable',
        'activity_logs' => 'createActivityLogsTable',
        'password_resets' => 'createPasswordResetsTable',
        'login_attempts' => 'createLoginAttemptsTable'
    ];
    
    foreach ($tables as $table => $createFunction) {
        if (!tableExists($pdo, $table)) {
            $createFunction($pdo);
        } else {
            echo "La table '$table' existe déjà.<br>";
        }
    }
    
    // Vérifier et corriger la structure de la table users
    if (tableExists($pdo, 'users')) {
        // Vérifier si la colonne password_hash existe
        if (columnExists($pdo, 'users', 'password_hash') && !columnExists($pdo, 'users', 'password')) {
            // Renommer la colonne password_hash en password
            $pdo->exec("ALTER TABLE users CHANGE COLUMN password_hash password VARCHAR(255) NOT NULL");
            echo "Colonne 'password_hash' renommée en 'password' dans la table 'users'.<br>";
        }
        
        // Vérifier si la colonne email existe
        if (!columnExists($pdo, 'users', 'email')) {
            // Ajouter la colonne email
            $pdo->exec("ALTER TABLE users ADD COLUMN email VARCHAR(100) UNIQUE NOT NULL AFTER password");
            echo "Colonne 'email' ajoutée à la table 'users'.<br>";
        }
        
        // Vérifier si la colonne last_login existe
        if (!columnExists($pdo, 'users', 'last_login')) {
            // Ajouter la colonne last_login
            $pdo->exec("ALTER TABLE users ADD COLUMN last_login DATETIME AFTER role");
            echo "Colonne 'last_login' ajoutée à la table 'users'.<br>";
        }
        
        // Vérifier si la colonne account_locked existe
        if (!columnExists($pdo, 'users', 'account_locked')) {
            // Ajouter la colonne account_locked
            $pdo->exec("ALTER TABLE users ADD COLUMN account_locked TINYINT(1) DEFAULT 0 AFTER last_login");
            echo "Colonne 'account_locked' ajoutée à la table 'users'.<br>";
        }
        
        // Vérifier si la colonne locked_until existe
        if (!columnExists($pdo, 'users', 'locked_until')) {
            // Ajouter la colonne locked_until
            $pdo->exec("ALTER TABLE users ADD COLUMN locked_until DATETIME DEFAULT NULL AFTER account_locked");
            echo "Colonne 'locked_until' ajoutée à la table 'users'.<br>";
        }
        
        // Vérifier si la colonne created_at existe
        if (!columnExists($pdo, 'users', 'created_at')) {
            // Ajouter la colonne created_at
            $pdo->exec("ALTER TABLE users ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP AFTER locked_until");
            echo "Colonne 'created_at' ajoutée à la table 'users'.<br>";
        }
        
        // Vérifier si la colonne updated_at existe
        if (!columnExists($pdo, 'users', 'updated_at')) {
            // Ajouter la colonne updated_at
            $pdo->exec("ALTER TABLE users ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_at");
            echo "Colonne 'updated_at' ajoutée à la table 'users'.<br>";
        }
    }
    
    echo "<br>Toutes les tables ont été vérifiées et corrigées avec succès.<br>";
    
    // Vérifier si des utilisateurs existent
    $stmt = $pdo->query("SELECT COUNT(*) FROM users");
    $userCount = $stmt->fetchColumn();
    
    if ($userCount == 0) {
        echo "<br>Aucun utilisateur n'est présent dans la base de données.<br>";
        echo "<a href='init_users_fixed.php' class='btn btn-primary'>Initialiser les utilisateurs</a>";
    } else {
        echo "<br>La base de données contient $userCount utilisateur(s).<br>";
        echo "<a href='login.html' class='btn btn-primary'>Aller à la page de connexion</a>";
    }
    
} catch (PDOException $e) {
    echo "Erreur: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vérification et correction des tables</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #0d6efd;
            margin-bottom: 20px;
        }
        .btn {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Vérification et correction des tables</h1>
        <!-- Les messages PHP seront affichés ici -->
    </div>
</body>
</html>

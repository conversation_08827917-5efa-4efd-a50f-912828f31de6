<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Suivi des Affaires Juridiques</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="assets/css/styles.css">
    <link rel="stylesheet" href="assets/css/dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="assets/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="container">
        <header class="my-4 d-flex justify-content-between align-items-center">
            <h1>Système de Suivi des Affaires Juridiques</h1>
            <div>
                <span id="user-info" class="me-3"></span>
                <a href="profile.html" class="btn btn-outline-primary btn-sm me-2">
                    <i class="fas fa-user-circle"></i> Profil
                </a>
                <div id="admin-tools" style="display: none;">
                    <a href="admin_users.html" class="btn btn-outline-primary btn-sm me-2">
                        <i class="fas fa-users"></i> Utilisateurs
                    </a>
                    <a href="admin_logs.html" class="btn btn-outline-primary btn-sm me-2">
                        <i class="fas fa-history"></i> Journal d'activité
                    </a>
                    <button id="admin-reset-db" class="btn btn-outline-danger btn-sm me-2">
                        <i class="fas fa-database"></i> Réinitialiser DB
                    </button>
                </div>
                <button id="logout-btn" class="btn btn-outline-danger btn-sm">Déconnexion</button>
            </div>
        </header>

        <nav class="mb-4">
            <ul class="nav nav-tabs">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="dashboard-dropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        Tableau de bord
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="dashboard-dropdown">
                        <li><a class="dropdown-item" href="#" id="dashboard-tab">Tableau de bord simple</a></li>
                        <li><a class="dropdown-item" href="dashboard_improved.html">Tableau de bord amélioré</a></li>
                    </ul>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="#" id="affaires-tab">Affaires</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" id="avocats-tab">Avocats</a>
                </li>
            </ul>
        </nav>

        <!-- Section Tableau de bord -->
        <section id="dashboard-section" class="dashboard-section mb-5">
            <div class="dashboard-title">
                <h2><i class="fas fa-chart-pie me-2"></i>Tableau de bord</h2>
                <div class="dashboard-actions">
                    <button id="btn-refresh-dashboard" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-sync-alt me-1"></i>Actualiser
                    </button>
                    <button id="btn-export-dashboard" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-file-export me-1"></i>Exporter
                    </button>
                </div>
            </div>

            <!-- Filtres du tableau de bord -->
            <div class="dashboard-filters">
                <div class="row align-items-end">
                    <div class="col-md-3">
                        <label for="dashboard-filter-period" class="form-label">Période</label>
                        <select id="dashboard-filter-period" class="form-select form-select-sm">
                            <option value="all">Toutes les périodes</option>
                            <option value="month">Ce mois</option>
                            <option value="quarter">Ce trimestre</option>
                            <option value="year">Cette année</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="dashboard-filter-nature" class="form-label">Nature</label>
                        <select id="dashboard-filter-nature" class="form-select form-select-sm">
                            <option value="">Toutes les natures</option>
                            <option value="social">Social</option>
                            <option value="administrative">Administrative</option>
                            <option value="commerciale">Commerciale</option>
                            <option value="foncier">Foncier</option>
                            <option value="refere">Référé</option>
                            <option value="civile">Civile</option>
                            <option value="penal">Pénal</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="dashboard-filter-status" class="form-label">Statut</label>
                        <select id="dashboard-filter-status" class="form-select form-select-sm">
                            <option value="">Tous les statuts</option>
                            <option value="en-cours">En cours</option>
                            <option value="jugee-faveur">Jugée en faveur</option>
                            <option value="jugee-tiers">Jugée pour tiers</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button id="btn-apply-filters" class="btn btn-primary btn-sm btn-filter">
                            <i class="fas fa-filter me-1"></i>Appliquer
                        </button>
                        <button id="btn-reset-filters" class="btn btn-outline-secondary btn-sm btn-filter ms-2">
                            <i class="fas fa-times me-1"></i>Reset
                        </button>
                    </div>
                </div>
            </div>

            <!-- Cartes KPI -->
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card kpi-card animate-fade-in delay-1">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h5 class="card-title">Total Affaires</h5>
                                    <h2 class="kpi-value text-primary" id="total-affaires">0</h2>
                                    <div class="kpi-trend neutral">
                                        <i class="fas fa-minus"></i>
                                        <span>En cours: <span id="affaires-en-cours">0</span></span>
                                    </div>
                                </div>
                                <div class="kpi-icon text-primary">
                                    <i class="fas fa-briefcase"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card kpi-card animate-fade-in delay-2">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h5 class="card-title">Taux de Succès</h5>
                                    <h2 class="kpi-value text-success" id="taux-succes">0%</h2>
                                    <div class="kpi-trend up">
                                        <i class="fas fa-arrow-up"></i>
                                        <span>Gagnées: <span id="affaires-gagnees">0</span></span>
                                    </div>
                                </div>
                                <div class="kpi-icon text-success">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card kpi-card animate-fade-in delay-3">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h5 class="card-title">Montant Total</h5>
                                    <h2 class="kpi-value text-warning" id="montant-total">0 €</h2>
                                    <div class="kpi-trend neutral">
                                        <i class="fas fa-euro-sign"></i>
                                        <span>Valeur totale</span>
                                    </div>
                                </div>
                                <div class="kpi-icon text-warning">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card kpi-card animate-fade-in delay-4">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h5 class="card-title">Avocats Actifs</h5>
                                    <h2 class="kpi-value text-info" id="total-avocats">0</h2>
                                    <div class="kpi-trend down" id="contrats-trend">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <span>Expirant: <span id="contrats-expirant">0</span></span>
                                    </div>
                                </div>
                                <div class="kpi-icon text-info">
                                    <i class="fas fa-user-tie"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Résumé financier -->
            <div class="card financial-summary mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Résumé Financier
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-3 mb-lg-0">
                            <div class="financial-item">
                                <h6><i class="fas fa-clock me-1"></i>En Cours</h6>
                                <h4 id="montant-en-cours">0 €</h4>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3 mb-lg-0">
                            <div class="financial-item">
                                <h6><i class="fas fa-trophy me-1"></i>Gagné</h6>
                                <h4 id="montant-gagne">0 €</h4>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3 mb-lg-0">
                            <div class="financial-item">
                                <h6><i class="fas fa-times-circle me-1"></i>Perdu</h6>
                                <h4 id="montant-perdu">0 €</h4>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="financial-item">
                                <h6><i class="fas fa-calculator me-1"></i>Moyenne</h6>
                                <h4 id="montant-moyen">0 €</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Graphiques principaux -->
            <div class="row">
                <div class="col-lg-6 mb-4">
                    <div class="card chart-card">
                        <div class="card-header">
                            <h5><i class="fas fa-chart-pie me-2"></i>Répartition par Nature</h5>
                            <div class="chart-actions">
                                <button class="btn btn-outline-secondary btn-sm" onclick="refreshChart('nature-chart')">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                                <button class="btn btn-outline-primary btn-sm" onclick="exportChart('nature-chart')">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <canvas id="nature-chart"></canvas>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6 mb-4">
                    <div class="card chart-card">
                        <div class="card-header">
                            <h5><i class="fas fa-balance-scale me-2"></i>Répartition par Juridiction</h5>
                            <div class="chart-actions">
                                <button class="btn btn-outline-secondary btn-sm" onclick="refreshChart('juridiction-chart')">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                                <button class="btn btn-outline-primary btn-sm" onclick="exportChart('juridiction-chart')">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <canvas id="juridiction-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Graphiques d'analyse -->
            <div class="row">
                <div class="col-lg-6 mb-4">
                    <div class="card chart-card">
                        <div class="card-header">
                            <h5><i class="fas fa-chart-line me-2"></i>Évolution des Affaires</h5>
                            <div class="chart-actions">
                                <button class="btn btn-outline-secondary btn-sm" onclick="refreshChart('evolution-chart')">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                                <button class="btn btn-outline-primary btn-sm" onclick="exportChart('evolution-chart')">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <canvas id="evolution-chart"></canvas>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6 mb-4">
                    <div class="card chart-card">
                        <div class="card-header">
                            <h5><i class="fas fa-euro-sign me-2"></i>Montant par Nature</h5>
                            <div class="chart-actions">
                                <button class="btn btn-outline-secondary btn-sm" onclick="refreshChart('montant-nature-chart')">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                                <button class="btn btn-outline-primary btn-sm" onclick="exportChart('montant-nature-chart')">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <canvas id="montant-nature-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section Performance et Activités -->
            <div class="row">
                <!-- Performances des avocats -->
                <div class="col-lg-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-trophy me-2"></i>Performance des Avocats
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="performance-avocats-list" class="list-group list-group-flush">
                                <!-- Les performances seront ajoutées ici dynamiquement -->
                                <div class="list-group-item d-flex justify-content-center align-items-center py-4">
                                    <div class="text-center text-muted">
                                        <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                        <p class="mb-0">Chargement des performances...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Activités récentes -->
                <div class="col-lg-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-clock me-2"></i>Activités Récentes
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="activites-recentes-list" class="list-group list-group-flush">
                                <!-- Les activités récentes seront ajoutées ici dynamiquement -->
                                <div class="list-group-item d-flex justify-content-center align-items-center py-4">
                                    <div class="text-center text-muted">
                                        <i class="fas fa-history fa-2x mb-2"></i>
                                        <p class="mb-0">Chargement des activités...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Affaires à haut risque -->
            <div class="high-risk-section">
                <div class="card high-risk-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>Affaires à Haut Risque
                            <span class="badge bg-light text-dark ms-2" id="high-risk-count">0</span>
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div id="high-risk-list" class="list-group list-group-flush">
                            <!-- Les affaires à haut risque seront ajoutées ici dynamiquement -->
                            <div class="list-group-item d-flex justify-content-center align-items-center py-4">
                                <div class="text-center text-muted">
                                    <i class="fas fa-shield-alt fa-2x mb-2"></i>
                                    <p class="mb-0">Aucune affaire à haut risque détectée</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Alertes système -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bell me-2"></i>Alertes Système
                        <span class="badge bg-warning text-dark ms-2" id="alerts-count">0</span>
                    </h5>
                </div>
                <div class="card-body">
                    <div id="alertes-list">
                        <!-- Les alertes seront ajoutées ici dynamiquement -->
                        <div class="alert-card warning">
                            <div class="alert-header">
                                <i class="fas fa-info-circle"></i>
                                Information
                            </div>
                            <div class="alert-body">
                                <p class="mb-0">Système initialisé. Chargement des alertes en cours...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <main>
            <!-- Section Affaires -->
            <section id="affaires-section" class="mb-5">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h2>Liste des Affaires</h2>
                    <button class="btn btn-primary" id="btn-nouvelle-affaire">Nouvelle Affaire</button>
                </div>

                <!-- Barre de recherche -->
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input type="text" id="recherche-affaire" class="form-control" placeholder="Rechercher une affaire...">
                                    <button class="btn btn-outline-secondary" type="button" id="btn-recherche-affaire">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select id="filtre-statut" class="form-select">
                                    <option value="">Tous les statuts</option>
                                    <option value="en-cours">En cours</option>
                                    <option value="jugee-faveur">Jugée en faveur</option>
                                    <option value="jugee-tiers">Jugée pour tiers</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select id="filtre-nature" class="form-select">
                                    <option value="">Toutes les natures</option>
                                    <option value="social">Social</option>
                                    <option value="administrative">Administrative</option>
                                    <option value="commerciale">Commerciale</option>
                                    <option value="foncier">Foncier</option>
                                    <option value="refere">Référé</option>
                                    <option value="civile">Civile</option>
                                    <option value="penal">Pénal</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-12 text-end">
                                <button id="btn-reset-filtres" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-undo me-1"></i>Réinitialiser les filtres
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div id="compteur-affaires">Affaires trouvées : 0</div>
                </div>

                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th class="col-numero">Numéro</th>
                                <th class="col-nature">Nature</th>
                                <th class="col-objet">Objet</th>
                                <th class="col-parties">Parties</th>
                                <th class="col-juridiction">Juridiction</th>
                                <th class="col-statut">Statut</th>
                                <th class="col-maitre">Maître</th>
                                <th class="col-actions">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="affaires-liste">
                            <!-- Les affaires seront ajoutées ici dynamiquement -->
                        </tbody>
                    </table>
                </div>

                <!-- Formulaire d'ajout/modification d'affaire (caché par défaut) -->
                <div id="form-affaire" class="card p-4 mb-4" style="display: none;">
                    <h3 id="form-affaire-titre">Nouvelle Affaire</h3>
                    <form id="affaire-form">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="numero-affaire" class="form-label">Numéro d'affaire</label>
                                <input type="text" class="form-control" id="numero-affaire" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="nature-affaire" class="form-label">Nature de l'affaire</label>
                                <select class="form-select" id="nature-affaire" required>
                                    <option value="">Sélectionner...</option>
                                    <option value="social">Social</option>
                                    <option value="administrative">Administrative</option>
                                    <option value="commerciale">Commerciale</option>
                                    <option value="foncier">Foncier</option>
                                    <option value="refere">Référé</option>
                                    <option value="civile">Civile</option>
                                    <option value="penal">Pénal</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="objet-affaire" class="form-label">Objet/Intitulé</label>
                            <input type="text" class="form-control" id="objet-affaire" required>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="parties-affaire" class="form-label">Parties</label>
                                <textarea class="form-control" id="parties-affaire" rows="3"></textarea>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="juridiction-affaire" class="form-label">Juridiction</label>
                                <select class="form-select" id="juridiction-affaire" required>
                                    <option value="">Sélectionner...</option>
                                    <option value="tribunal">Tribunal</option>
                                    <option value="cour">Cour</option>
                                    <option value="cours-supreme">Cours Suprême</option>
                                    <option value="c-etat">C État</option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="engagement-affaire" class="form-label">Engagement</label>
                                <select class="form-select" id="engagement-affaire" required>
                                    <option value="">Sélectionner...</option>
                                    <option value="societe">Par la société</option>
                                    <option value="tiers">Tiers personne</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="date-engagement" class="form-label">Date d'engagement</label>
                                <input type="date" class="form-control" id="date-engagement" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="date-jugement" class="form-label">Date du jugement</label>
                                <input type="date" class="form-control" id="date-jugement">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="statut-affaire" class="form-label">Statut</label>
                                <select class="form-select" id="statut-affaire" required>
                                    <option value="">Sélectionner...</option>
                                    <option value="en-cours">En cours</option>
                                    <option value="jugee-faveur">Jugée en faveur</option>
                                    <option value="jugee-tiers">Jugée pour tiers</option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="dispositif-affaire" class="form-label">Dispositif</label>
                                <textarea class="form-control" id="dispositif-affaire" rows="3"></textarea>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="montant-affaire" class="form-label">Montant</label>
                                <input type="number" class="form-control" id="montant-affaire">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="maitre-affaire" class="form-label">Maître</label>
                            <select class="form-select" id="maitre-affaire" required>
                                <option value="">Sélectionner...</option>
                                <!-- Les avocats seront ajoutés ici dynamiquement -->
                            </select>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-secondary me-2" id="btn-annuler-affaire">Annuler</button>
                            <button type="submit" class="btn btn-primary">Enregistrer</button>
                        </div>
                    </form>
                </div>
            </section>

            <!-- Section Avocats (cachée par défaut) -->
            <section id="avocats-section" class="mb-5" style="display: none;">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h2>Liste des Avocats</h2>
                    <button class="btn btn-primary" id="btn-nouvel-avocat">Nouvel Avocat</button>
                </div>

                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Nom</th>
                                <th>Agrément</th>
                                <th>Téléphone</th>
                                <th>Email</th>
                                <th>Validité du contrat</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="avocats-liste">
                            <!-- Les avocats seront ajoutés ici dynamiquement -->
                        </tbody>
                    </table>
                </div>

                <!-- Formulaire d'ajout/modification d'avocat (caché par défaut) -->
                <div id="form-avocat" class="card p-4 mb-4" style="display: none;">
                    <h3 id="form-avocat-titre">Nouvel Avocat</h3>
                    <form id="avocat-form">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="nom-avocat" class="form-label">Nom du maître</label>
                                <input type="text" class="form-control" id="nom-avocat" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="agrement-avocat" class="form-label">Agrément</label>
                                <input type="text" class="form-control" id="agrement-avocat" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="adresse-avocat" class="form-label">Adresse</label>
                            <textarea class="form-control" id="adresse-avocat" rows="2" required></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="telephone-avocat" class="form-label">Numéro de téléphone</label>
                                <input type="tel" class="form-control" id="telephone-avocat" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email-avocat" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email-avocat" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="date-signature" class="form-label">Date de signature du contrat</label>
                                <input type="date" class="form-control" id="date-signature" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="validite-contrat" class="form-label">Validité du contrat (1 an après signature)</label>
                                <input type="date" class="form-control" id="validite-contrat" readonly>
                                <small class="text-muted">Calculée automatiquement à partir de la date de signature</small>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-secondary me-2" id="btn-annuler-avocat">Annuler</button>
                            <button type="submit" class="btn btn-primary">Enregistrer</button>
                        </div>
                    </form>
                </div>
            </section>
        </main>
    </div>

    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="database_mysql.js"></script>
    <script src="js/dashboard_simple.js"></script>
    <script src="session-manager.js"></script>
    <script src="app.js"></script>
</body>
</html>


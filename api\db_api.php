<?php
// API pour la gestion de la base de données

// Autoriser les requêtes CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

// Si c'est une requête OPTIONS (preflight), renvoyer 200 OK
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Démarrer la session
session_start();

// Inclure les fichiers nécessaires
require_once '../config.php';
require_once '../auth.php';

// Vérifier si l'utilisateur est connecté
if (!isLoggedIn()) {
    jsonResponse(['success' => false, 'message' => 'Non authentifié'], 401);
}

// Récupérer la méthode HTTP et l'action
$method = $_SERVER['REQUEST_METHOD'];
$action = isset($_GET['action']) ? $_GET['action'] : '';

// Traiter la requête en fonction de la méthode et de l'action
switch ($method) {
    case 'GET':
        switch ($action) {
            case 'check':
                // Vérifier la connexion à la base de données
                try {
                    $pdo = getDbConnection();
                    jsonResponse(['success' => true, 'message' => 'Connexion à la base de données établie avec succès']);
                } catch (PDOException $e) {
                    jsonResponse(['success' => false, 'message' => 'Erreur de connexion à la base de données: ' . $e->getMessage()], 500);
                }
                break;
                
            default:
                jsonResponse(['success' => false, 'message' => 'Action non reconnue'], 400);
        }
        break;
        
    case 'POST':
        switch ($action) {
            case 'add_test_data':
                // Vérifier si l'utilisateur est administrateur
                if (!isAdmin()) {
                    jsonResponse(['success' => false, 'message' => 'Seuls les administrateurs peuvent ajouter des données de test'], 403);
                }
                
                // Ajouter des données de test
                try {
                    $pdo = getDbConnection();
                    
                    // Vérifier si des données existent déjà
                    $checkAvocats = $pdo->query("SELECT COUNT(*) FROM avocats");
                    $avocatsCount = $checkAvocats->fetchColumn();
                    
                    $checkAffaires = $pdo->query("SELECT COUNT(*) FROM affaires");
                    $affairesCount = $checkAffaires->fetchColumn();
                    
                    if ($avocatsCount > 0 || $affairesCount > 0) {
                        jsonResponse(['success' => false, 'message' => 'Des données existent déjà dans la base de données. Veuillez la réinitialiser avant d\'ajouter des données de test.'], 400);
                    }
                    
                    // Ajouter des avocats de test
                    $stmt = $pdo->prepare("INSERT INTO avocats (nom, agrement, adresse, telephone, email, date_signature, validite_contrat) 
                                          VALUES (?, ?, ?, ?, ?, ?, ?)");
                    
                    // Avocat 1
                    $stmt->execute([
                        "Me Jean Dupont",
                        "A12345",
                        "123 Avenue des Avocats, Paris",
                        "01 23 45 67 89",
                        "<EMAIL>",
                        "2022-01-01",
                        "2024-01-01"
                    ]);
                    $avocat1Id = $pdo->lastInsertId();
                    
                    // Avocat 2
                    $stmt->execute([
                        "Me Marie Martin",
                        "B67890",
                        "456 Rue du Barreau, Lyon",
                        "01 98 76 54 32",
                        "<EMAIL>",
                        "2022-03-15",
                        "2024-03-15"
                    ]);
                    $avocat2Id = $pdo->lastInsertId();
                    
                    // Ajouter des affaires de test
                    $stmt = $pdo->prepare("INSERT INTO affaires (numero, nature, objet, parties, juridiction, engagement, 
                                          date_engagement, date_jugement, statut, dispositif, montant, maitre) 
                                          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                    
                    // Affaire 1
                    $stmt->execute([
                        "AFF-2023-001",
                        "commerciale",
                        "Litige contractuel avec fournisseur",
                        "Société X c/ Fournisseur Y",
                        "tribunal",
                        "societe",
                        "2023-01-15",
                        "2023-06-20",
                        "jugee-faveur",
                        "Condamnation du fournisseur à verser des dommages et intérêts",
                        50000,
                        $avocat1Id
                    ]);
                    
                    // Affaire 2
                    $stmt->execute([
                        "AFF-2023-002",
                        "social",
                        "Licenciement contesté",
                        "Employé Z c/ Société X",
                        "cour",
                        "tiers",
                        "2023-02-10",
                        null,
                        "en-cours",
                        null,
                        null,
                        $avocat2Id
                    ]);
                    
                    // Journaliser l'ajout des données de test
                    logActivity($_SESSION['username'], 'create', 'database', null, "Ajout de données de test");
                    
                    jsonResponse([
                        'success' => true,
                        'message' => 'Données de test ajoutées avec succès'
                    ]);
                } catch (PDOException $e) {
                    jsonResponse(['success' => false, 'message' => 'Erreur lors de l\'ajout des données de test: ' . $e->getMessage()], 500);
                }
                break;
                
            case 'reset':
                // Vérifier si l'utilisateur est administrateur
                if (!isAdmin()) {
                    jsonResponse(['success' => false, 'message' => 'Seuls les administrateurs peuvent réinitialiser la base de données'], 403);
                }
                
                // Réinitialiser la base de données
                try {
                    $pdo = getDbConnection();
                    
                    // Désactiver les contraintes de clé étrangère
                    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
                    
                    // Vider les tables
                    $pdo->exec("TRUNCATE TABLE affaires");
                    $pdo->exec("TRUNCATE TABLE avocats");
                    $pdo->exec("TRUNCATE TABLE activity_logs");
                    
                    // Réactiver les contraintes de clé étrangère
                    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
                    
                    // Journaliser la réinitialisation de la base de données
                    logActivity($_SESSION['username'], 'reset', 'database', null, "Réinitialisation de la base de données");
                    
                    jsonResponse([
                        'success' => true,
                        'message' => 'Base de données réinitialisée avec succès'
                    ]);
                } catch (PDOException $e) {
                    jsonResponse(['success' => false, 'message' => 'Erreur lors de la réinitialisation de la base de données: ' . $e->getMessage()], 500);
                }
                break;
                
            default:
                jsonResponse(['success' => false, 'message' => 'Action non reconnue'], 400);
        }
        break;
        
    default:
        jsonResponse(['success' => false, 'message' => 'Méthode non autorisée'], 405);
}

/**
 * Gestionnaire de session pour maintenir la session active
 * Ce script vérifie périodiquement l'état de la session et la rafraîchit si nécessaire
 */

// Intervalle de vérification de la session en millisecondes (15 minutes)
const SESSION_CHECK_INTERVAL = 15 * 60 * 1000;

// Fonction pour vérifier et rafraîchir la session
function checkAndRefreshSession() {
    // Vérifier si l'utilisateur est connecté localement
    if (!localStorage.getItem('currentUser')) {
        return; // Ne rien faire si l'utilisateur n'est pas connecté
    }

    // Appeler l'API pour vérifier l'état de la session
    fetch('../../app/api/auth_api.php?action=check', {
        method: 'GET',
        credentials: 'include'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('Session active, rafraîchissement réussi');
        } else {
            console.warn('Session expirée, redirection vers la page de connexion');
            // Supprimer les informations de l'utilisateur du localStorage
            localStorage.removeItem('currentUser');
            // Rediriger vers la page de connexion
            window.location.href = '../../views/auth/login.html';
        }
    })
    .catch(error => {
        console.error('Erreur lors de la vérification de la session:', error);
    });
}

// Démarrer la vérification périodique de la session
let sessionCheckInterval;

// Fonction pour démarrer le rafraîchissement de session
function startSessionRefresh() {
    // Arrêter l'intervalle existant s'il y en a un
    if (sessionCheckInterval) {
        clearInterval(sessionCheckInterval);
    }
    
    // Vérifier immédiatement la session
    checkAndRefreshSession();
    
    // Configurer la vérification périodique
    sessionCheckInterval = setInterval(checkAndRefreshSession, SESSION_CHECK_INTERVAL);
    
    console.log('Rafraîchissement automatique de session démarré');
}

// Fonction pour arrêter le rafraîchissement de session
function stopSessionRefresh() {
    if (sessionCheckInterval) {
        clearInterval(sessionCheckInterval);
        sessionCheckInterval = null;
        console.log('Rafraîchissement automatique de session arrêté');
    }
}

// Démarrer le rafraîchissement de session au chargement de la page
document.addEventListener('DOMContentLoaded', () => {
    // Ne pas démarrer le rafraîchissement sur la page de connexion
    if (!window.location.pathname.includes('login.html')) {
        startSessionRefresh();
    }
});

// Détecter l'activité de l'utilisateur pour rafraîchir la session
document.addEventListener('click', checkAndRefreshSession);
document.addEventListener('keydown', checkAndRefreshSession);

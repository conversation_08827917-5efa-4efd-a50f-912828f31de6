<?php
// Script pour déboguer les réponses brutes des API

// Désactiver la mise en forme HTML pour voir le code source brut
header('Content-Type: text/plain');

// URL de base
$baseUrl = 'http://' . $_SERVER['HTTP_HOST'];

// Fonction pour effectuer une requête HTTP et afficher la réponse brute
function fetchUrlRaw($url) {
    echo "=== Requête vers: $url ===\n\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . session_id());
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    $header = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);
    
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "Code HTTP: $httpCode\n\n";
    
    if ($error) {
        echo "Erreur cURL: $error\n\n";
    }
    
    echo "=== En-têtes HTTP ===\n";
    echo $header . "\n";
    
    echo "=== Corps de la réponse ===\n";
    echo $body . "\n\n";
    
    return $body;
}

// Démarrer la session pour maintenir l'authentification
session_start();

// Simuler une connexion en tant qu'administrateur
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'admin';
$_SESSION['user_role'] = 'admin';
$_SESSION['last_activity'] = time();

echo "=== Débogage des API (réponses brutes) ===\n\n";
echo "Session ID: " . session_id() . "\n";
echo "Session Data: " . print_r($_SESSION, true) . "\n\n";

// Tester l'API des utilisateurs
echo "=== Test de l'API des utilisateurs ===\n";
fetchUrlRaw($baseUrl . '/api/users_api.php?action=list');

// Tester l'API des journaux d'activité
echo "=== Test de l'API des journaux d'activité ===\n";
fetchUrlRaw($baseUrl . '/api/logs_api.php?action=list');

// Tester les chemins alternatifs
echo "=== Test des chemins alternatifs ===\n";
echo "=== API des utilisateurs (app/api) ===\n";
fetchUrlRaw($baseUrl . '/app/api/users_api.php?action=list');

echo "=== API des journaux d'activité (app/api) ===\n";
fetchUrlRaw($baseUrl . '/app/api/logs_api.php?action=list');

# Système de Suivi des Affaires Juridiques

Cette application complète permet de gérer et suivre les affaires juridiques, les avocats associés, et offre un tableau de bord analytique pour une vision globale des performances. Développée pour les cabinets d'avocats et les services juridiques d'entreprise, elle centralise toutes les informations essentielles dans une interface intuitive et responsive.

## Fonctionnalités

### Gestion des Affaires Juridiques
- Liste des affaires avec leurs informations principales
- Ajout, modification et suppression d'affaires (suppression réservée aux administrateurs)
- Suivi du statut des affaires (en cours, jugée en faveur, jugée pour tiers)
- Informations détaillées sur chaque affaire (nature, parties, juridiction, etc.)
- Recherche et filtrage des affaires par multiples critères (statut, nature, texte libre)
- Gestion des montants financiers associés aux affaires

### Gestion des Avocats
- Liste des avocats avec leurs informations principales
- Ajout, modification et suppression d'avocats (suppression réservée aux administrateurs)
- Suivi des contrats (date de signature, validité automatique d'un an)
- Mise en évidence visuelle des contrats expirés (lignes colorées et texte clignotant)
- Association des avocats aux affaires juridiques
- Alertes automatiques pour les contrats expirant prochainement

### Tableau de Bord Analytique
- **Indicateurs clés de performance** :
  - Nombre total d'affaires
  - Nombre d'affaires en cours
  - Nombre d'affaires gagnées
  - Taux de succès global
  - Montant total des affaires
  - Nombre total d'avocats
  - Nombre de contrats expirant prochainement
- **Visualisations graphiques** :
  - Répartition des affaires par nature (graphique en anneau)
  - Répartition des affaires par juridiction (graphique à barres)
  - Évolution temporelle des affaires (graphique linéaire)
  - Montants financiers par nature d'affaire (graphique à barres)
- **Résumé financier** :
  - Montant des affaires en cours
  - Montant des affaires gagnées
  - Montant des affaires perdues
  - Montant moyen par affaire
- **Section d'alertes** pour les contrats d'avocats expirant prochainement
- **Exportation des données** du tableau de bord

### Administration
- Journal d'activité pour suivre toutes les actions des utilisateurs
- Gestion des utilisateurs avec différents niveaux d'accès (admin, user)
- Sécurité renforcée avec restrictions sur les suppressions
- Suivi des connexions et gestion des sessions

## Structure des données

### Affaires Juridiques
- **Nature de l'affaire**: Social, Administrative, Commerciale, Foncier, Référé, Civile, Pénal
- **Objet**: Intitulé de l'affaire
- **Parties**: Personnes ou entités impliquées
- **Numéro d'affaire**: Identifiant unique
- **Juridiction**: Tribunal, Cour, Cours Suprême, C État
- **Engagement**: Par la société ou Tiers personne
- **Dates**: Date d'engagement, Date du jugement
- **Dispositif**: Résumé du jugement
- **Montant**: Montant financier associé
- **Statut**: En cours, Jugée en faveur, Jugée pour tiers
- **Maître**: Avocat en charge de l'affaire

### Avocats
- **Nom du maître**
- **Agrément**
- **Adresse**
- **Numéro de téléphone**
- **Email**
- **Date de signature du contrat**
- **Validité du contrat**

## Structure du projet

Le projet est organisé selon une architecture moderne et maintenable:

```
/
├── app/                    # Code principal de l'application
│   ├── api/                # API endpoints
│   ├── config/             # Fichiers de configuration
│   ├── core/               # Fonctions et classes de base
│   ├── models/             # Modèles de données
│   └── utils/              # Utilitaires
├── assets/                 # Ressources statiques
│   ├── css/                # Fichiers CSS
│   ├── fonts/              # Polices
│   ├── icons/              # Icônes
│   ├── img/                # Images
│   └── js/                 # JavaScript
├── database/               # Scripts de base de données
│   ├── migrations/         # Scripts de migration
│   └── seeds/              # Données initiales
├── install/                # Scripts d'installation
├── public/                 # Point d'entrée public
│   ├── index.php           # Point d'entrée principal
│   └── .htaccess           # Configuration Apache
└── views/                  # Fichiers HTML/templates
    ├── admin/              # Pages d'administration
    ├── auth/               # Pages d'authentification
    └── app/                # Pages de l'application
```

## Installation et configuration

### Prérequis
- Laragon installé (https://laragon.org/download/)
- MySQL 8.0 ou supérieur
- PHP 8.1 ou supérieur
- Apache 2.4 ou supérieur

### Installation
1. Clonez ce dépôt dans le répertoire `www` de Laragon (`C:\laragon\www`)
2. Exécutez le script `install/setup_laragon.bat` pour configurer automatiquement la base de données
3. Démarrez Laragon (Apache et MySQL)
4. Accédez à l'application via http://localhost/

### Initialisation de la base de données
1. Accédez à http://localhost/install/init_db.php pour initialiser la base de données
2. Suivez les instructions à l'écran pour créer les tables et un utilisateur administrateur par défaut

### Connexion
- Utilisateur administrateur par défaut : `admin`
- Mot de passe par défaut : `password`

## Comment utiliser l'application

### Navigation et interface

1. Connectez-vous avec vos identifiants sur la page de connexion
2. Vous accédez à l'interface principale avec trois onglets principaux :
   - **Tableau de bord** : Vue d'ensemble et statistiques
   - **Affaires** : Gestion des dossiers juridiques
   - **Avocats** : Gestion des avocats et de leurs contrats
3. L'interface est entièrement responsive et s'adapte à tous les appareils (ordinateurs, tablettes, smartphones)

### Gestion des affaires

1. Cliquez sur l'onglet "Affaires" pour accéder à la liste des dossiers
2. Utilisez la barre de recherche et les filtres pour trouver rapidement des affaires
3. Cliquez sur "Nouvelle Affaire" pour créer un nouveau dossier
4. Remplissez le formulaire avec les informations requises (le champ numéro d'affaire reçoit automatiquement le focus)
5. Pour modifier une affaire, cliquez sur le bouton "Modifier" correspondant
6. Pour supprimer une affaire (administrateurs uniquement), cliquez sur "Supprimer"

### Gestion des avocats

1. Cliquez sur l'onglet "Avocats" pour accéder à la liste des avocats
2. Cliquez sur "Nouvel Avocat" pour ajouter un avocat
3. Remplissez le formulaire avec les informations requises
4. La date de validité du contrat est automatiquement calculée (un an après la date de signature)
5. Les contrats expirés sont mis en évidence visuellement dans la liste

### Utilisation du tableau de bord

1. Cliquez sur l'onglet "Tableau de bord" pour accéder aux statistiques
2. Consultez les indicateurs clés en haut de la page
3. Explorez les différents graphiques pour analyser les données
4. Vérifiez la section des alertes pour les contrats d'avocats expirant prochainement
5. Utilisez le bouton d'exportation pour télécharger les données du tableau de bord

## Base de données

L'application utilise MySQL pour stocker les données. La structure comprend :

- Table `affaires` : stocke les informations sur les affaires juridiques
- Table `avocats` : stocke les informations sur les avocats
- Table `users` : gère les utilisateurs et leurs rôles
- Table `activity_logs` : enregistre toutes les actions des utilisateurs
- Table `login_attempts` : suit les tentatives de connexion pour la sécurité

## Développement

### Structure des fichiers

- **app/api/**: Contient les endpoints API qui traitent les requêtes AJAX
- **app/config/**: Contient les fichiers de configuration de l'application
- **app/core/**: Contient les classes et fonctions de base de l'application
- **app/models/**: Contient les modèles de données qui interagissent avec la base de données
- **app/utils/**: Contient les fonctions utilitaires utilisées dans toute l'application
- **assets/**: Contient les ressources statiques (CSS, JS, images, etc.)
- **database/**: Contient les scripts de base de données
- **install/**: Contient les scripts d'installation et de configuration
- **public/**: Point d'entrée public de l'application
- **views/**: Contient les fichiers HTML/templates

### Flux de l'application

1. Toutes les requêtes sont dirigées vers `public/index.php` grâce à la configuration `.htaccess`
2. `index.php` charge le fichier `app/bootstrap.php` qui initialise l'application
3. En fonction de la requête, `index.php` charge la vue appropriée depuis le répertoire `views/`
4. Les vues utilisent les fichiers JavaScript du répertoire `assets/js/` pour effectuer des requêtes AJAX vers les endpoints API du répertoire `app/api/`
5. Les endpoints API utilisent les modèles du répertoire `app/models/` pour interagir avec la base de données

### Technologies utilisées

- **Frontend** :
  - HTML5, CSS3, JavaScript (ES6+)
  - Bootstrap 5 pour l'interface responsive
  - Chart.js pour les visualisations graphiques
  - FontAwesome pour les icônes
  - IndexedDB pour le stockage local côté client
- **Backend** :
  - PHP 8.1+ avec architecture MVC légère
  - PDO pour l'accès sécurisé à la base de données
  - API RESTful pour la communication client-serveur
- **Base de données** :
  - MySQL 8.0+ pour le stockage persistant
  - Transactions pour garantir l'intégrité des données
- **Serveur** :
  - Apache 2.4+ via Laragon pour le développement local
  - Support de déploiement sur serveurs partagés ou dédiés

## Sécurité

- Authentification des utilisateurs avec hachage sécurisé des mots de passe
- Protection contre les attaques par force brute (verrouillage de compte après 5 tentatives)
- Journalisation de toutes les actions pour l'audit
- Restrictions d'accès basées sur les rôles (admin, user)
- Validation des données côté serveur et client

## Déploiement

Pour déployer l'application sur un serveur web:

1. Transférez tous les fichiers sur votre serveur web
2. Configurez votre serveur web pour pointer vers le répertoire `public/`
3. Créez une base de données MySQL et importez le schéma depuis `database/migrations/schema.sql`
4. Mettez à jour les informations de connexion à la base de données dans `app/config/config.php`
5. Accédez à l'application via votre navigateur

## Évolutions futures possibles

### Fonctionnalités avancées
- **Système de notifications** :
  - Alertes par email pour les dates importantes
  - Notifications push pour les mises à jour critiques
  - Rappels personnalisables pour les échéances
- **Intégration avec un calendrier** :
  - Synchronisation avec Google Calendar/Outlook
  - Vue calendrier des audiences et rendez-vous
  - Rappels automatiques des événements à venir
- **Module de génération de documents** :
  - Création de modèles de documents juridiques
  - Remplissage automatique avec les données des affaires
  - Export en PDF, Word et autres formats

### Améliorations techniques
- **Application mobile native** :
  - Versions iOS et Android dédiées
  - Fonctionnalités hors ligne
  - Notifications push
- **Système d'archivage intelligent** :
  - Archivage automatique des affaires terminées
  - Politique de rétention configurable
  - Recherche avancée dans les archives
- **Analyses avancées** :
  - Prédiction des résultats d'affaires basée sur l'historique
  - Tableaux de bord personnalisables
  - Rapports détaillés par période, avocat, nature d'affaire, etc.

### Intégrations
- **API pour tiers** :
  - Intégration avec des logiciels de comptabilité
  - Connexion avec des systèmes de gestion documentaire
  - Webhooks pour automatisations personnalisées
- **Système de facturation** :
  - Génération automatique de factures
  - Suivi des paiements
  - Rapports financiers détaillés
- **Intégration avec les bases juridiques** :
  - Connexion aux bases de données de jurisprudence
  - Recherche de précédents juridiques
  - Suggestions automatiques basées sur des cas similaires

## Licence

Ce projet est sous licence MIT. Voir le fichier LICENSE pour plus de détails.

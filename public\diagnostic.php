<?php
/**
 * Script de diagnostic pour identifier les problèmes d'initialisation de l'application
 */

// Activer l'affichage des erreurs
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Fonction pour afficher les informations de diagnostic
function displayDiagnostic($title, $content, $isError = false) {
    echo "<div style='margin-bottom: 20px;'>";
    echo "<h3 style='color: " . ($isError ? "red" : "blue") . ";'>{$title}</h3>";
    echo "<pre style='background-color: #f5f5f5; padding: 10px; border-radius: 5px;'>";
    
    if (is_array($content) || is_object($content)) {
        print_r($content);
    } else {
        echo htmlspecialchars($content);
    }
    
    echo "</pre>";
    echo "</div>";
}

// Afficher l'en-tête HTML
echo "<!DOCTYPE html>
<html>
<head>
    <title>Diagnostic de l'application</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        h1 {
            color: #333;
        }
        h2 {
            color: #666;
            margin-top: 30px;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class='container'>
        <h1>Diagnostic de l'application</h1>";

// Vérifier les chemins de base
$basePath = dirname(__DIR__);
displayDiagnostic("Chemin de base", $basePath);

// Vérifier si le fichier de configuration existe
$configPath = $basePath . '/app/config/config.php';
$configExists = file_exists($configPath);
displayDiagnostic("Fichier de configuration", $configPath . " - " . ($configExists ? "Existe" : "N'existe pas"), !$configExists);

// Vérifier les permissions des répertoires
$directories = [
    $basePath . '/app',
    $basePath . '/app/config',
    $basePath . '/app/api',
    $basePath . '/assets',
    $basePath . '/database',
    $basePath . '/public',
    $basePath . '/views'
];

echo "<h2>Permissions des répertoires</h2>";
foreach ($directories as $dir) {
    $exists = is_dir($dir);
    $readable = is_readable($dir);
    $writable = is_writable($dir);
    
    displayDiagnostic($dir, 
        "Existe: " . ($exists ? "Oui" : "Non") . 
        " | Lisible: " . ($readable ? "Oui" : "Non") . 
        " | Modifiable: " . ($writable ? "Oui" : "Non"),
        !$exists || !$readable
    );
}

// Tester la connexion à la base de données
echo "<h2>Connexion à la base de données</h2>";
try {
    // Inclure le fichier de configuration
    if ($configExists) {
        require_once $configPath;
        
        // Vérifier si la fonction getDbConnection existe
        if (function_exists('getDbConnection')) {
            try {
                $pdo = getDbConnection();
                displayDiagnostic("Connexion à la base de données", "Connexion réussie");
                
                // Vérifier la structure de la table users
                $stmt = $pdo->query("DESCRIBE users");
                $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
                displayDiagnostic("Structure de la table users", $columns);
                
                // Vérifier si la table users contient des données
                $stmt = $pdo->query("SELECT COUNT(*) FROM users");
                $count = $stmt->fetchColumn();
                displayDiagnostic("Nombre d'utilisateurs", $count);
                
                // Vérifier les autres tables
                $tables = ['affaires', 'avocats', 'activity_logs', 'login_attempts'];
                foreach ($tables as $table) {
                    try {
                        $stmt = $pdo->query("SELECT COUNT(*) FROM {$table}");
                        $count = $stmt->fetchColumn();
                        displayDiagnostic("Nombre d'enregistrements dans la table {$table}", $count);
                    } catch (PDOException $e) {
                        displayDiagnostic("Erreur lors de la vérification de la table {$table}", $e->getMessage(), true);
                    }
                }
            } catch (PDOException $e) {
                displayDiagnostic("Erreur de connexion à la base de données", $e->getMessage(), true);
            }
        } else {
            displayDiagnostic("Fonction getDbConnection", "La fonction n'existe pas", true);
        }
    } else {
        displayDiagnostic("Connexion à la base de données", "Impossible de tester la connexion car le fichier de configuration n'existe pas", true);
    }
} catch (Exception $e) {
    displayDiagnostic("Erreur lors du test de connexion à la base de données", $e->getMessage(), true);
}

// Vérifier les variables de session
echo "<h2>Variables de session</h2>";
session_start();
displayDiagnostic("Session ID", session_id());
displayDiagnostic("Variables de session", $_SESSION);

// Vérifier les variables d'environnement
echo "<h2>Variables d'environnement</h2>";
displayDiagnostic("PHP Version", phpversion());
displayDiagnostic("Extensions PHP chargées", get_loaded_extensions());
displayDiagnostic("Variables serveur", $_SERVER);

// Afficher le pied de page HTML
echo "    </div>
</body>
</html>";

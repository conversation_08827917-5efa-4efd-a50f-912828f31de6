<?php
// Script pour déboguer les réponses des API

// Activer l'affichage des erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// URL de base
$baseUrl = 'http://' . $_SERVER['HTTP_HOST'];

// Fonction pour effectuer une requête HTTP et afficher la réponse brute
function fetchUrl($url) {
    echo "<h3>Requête vers: $url</h3>";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    $header = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);
    
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "<p>Code HTTP: $httpCode</p>";
    
    if ($error) {
        echo "<p>Erreur cURL: $error</p>";
    }
    
    echo "<h4>En-têtes HTTP:</h4>";
    echo "<pre>" . htmlspecialchars($header) . "</pre>";
    
    echo "<h4>Corps de la réponse:</h4>";
    echo "<pre>" . htmlspecialchars($body) . "</pre>";
    
    return $body;
}

echo "<h1>Débogage des API</h1>";

// Tester l'API des utilisateurs
echo "<h2>Test de l'API des utilisateurs</h2>";
fetchUrl($baseUrl . '/api/users_api.php?action=list');

// Tester l'API des journaux d'activité
echo "<h2>Test de l'API des journaux d'activité</h2>";
fetchUrl($baseUrl . '/api/logs_api.php?action=list');

// Tester les chemins alternatifs
echo "<h2>Test des chemins alternatifs</h2>";
echo "<h3>API des utilisateurs (app/api)</h3>";
fetchUrl($baseUrl . '/app/api/users_api.php?action=list');

echo "<h3>API des journaux d'activité (app/api)</h3>";
fetchUrl($baseUrl . '/app/api/logs_api.php?action=list');

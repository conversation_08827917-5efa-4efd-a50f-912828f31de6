<?php
// <PERSON><PERSON><PERSON> to fix the users table column name issue
require_once 'config.php';

try {
    $pdo = getDbConnection();
    
    // Check if password_hash column exists
    $stmt = $pdo->query("SELECT COUNT(*) FROM information_schema.COLUMNS 
                         WHERE TABLE_SCHEMA = 'suivi_affaires_juridiques' 
                         AND TABLE_NAME = 'users' 
                         AND COLUMN_NAME = 'password_hash'");
    $passwordHashExists = (bool)$stmt->fetchColumn();
    
    // Check if password column exists
    $stmt = $pdo->query("SELECT COUNT(*) FROM information_schema.COLUMNS 
                         WHERE TABLE_SCHEMA = 'suivi_affaires_juridiques' 
                         AND TABLE_NAME = 'users' 
                         AND COLUMN_NAME = 'password'");
    $passwordExists = (bool)$stmt->fetchColumn();
    
    echo "Current state: password_hash column exists: " . ($passwordHashExists ? "Yes" : "No") . "\n";
    echo "Current state: password column exists: " . ($passwordExists ? "Yes" : "No") . "\n";
    
    // If password_hash exists but password doesn't, rename the column
    if ($passwordHashExists && !$passwordExists) {
        $pdo->exec("ALTER TABLE users CHANGE COLUMN password_hash password VARCHAR(255) NOT NULL");
        echo "Column renamed from password_hash to password successfully.\n";
    } 
    // If neither column exists, add password column
    else if (!$passwordHashExists && !$passwordExists) {
        $pdo->exec("ALTER TABLE users ADD COLUMN password VARCHAR(255) NOT NULL AFTER username");
        echo "Added new password column successfully.\n";
    }
    else if ($passwordExists) {
        echo "The password column already exists. No changes needed.\n";
    }
    
    // Verify the structure after changes
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\nCurrent structure of users table:\n";
    foreach ($columns as $column) {
        echo $column['Field'] . " - " . $column['Type'] . "\n";
    }
    
    echo "\nFix completed successfully!\n";
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>

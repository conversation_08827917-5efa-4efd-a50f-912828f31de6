<?php
// API pour la gestion du profil utilisateur

// Autoriser les requêtes CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

// Si c'est une requête OPTIONS (preflight), renvoyer 200 OK
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Démarrer la session
session_start();

// Inclure les fichiers nécessaires
require_once '../config.php';
require_once '../auth.php';

// Vérifier si l'utilisateur est connecté
if (!isLoggedIn()) {
    jsonResponse(['success' => false, 'message' => 'Non authentifié'], 401);
}

// Récupérer la méthode HTTP et l'action
$method = $_SERVER['REQUEST_METHOD'];
$action = isset($_GET['action']) ? $_GET['action'] : '';

// Traiter la requête en fonction de la méthode et de l'action
switch ($method) {
    case 'GET':
        switch ($action) {
            case 'get':
                // Vérifier que l'ID de l'utilisateur est fourni
                if (!isset($_GET['id'])) {
                    jsonResponse(['success' => false, 'message' => 'ID de l\'utilisateur requis'], 400);
                }
                
                // Vérifier que l'utilisateur demande son propre profil ou est administrateur
                if ($_SESSION['user_id'] != $_GET['id'] && !isAdmin()) {
                    jsonResponse(['success' => false, 'message' => 'Vous n\'êtes pas autorisé à accéder à ce profil'], 403);
                }
                
                // Récupérer les informations du profil
                try {
                    $pdo = getDbConnection();
                    $stmt = $pdo->prepare("SELECT id, username, email, role, last_login, created_at FROM users WHERE id = ?");
                    $stmt->execute([$_GET['id']]);
                    $user = $stmt->fetch();
                    
                    if (!$user) {
                        jsonResponse(['success' => false, 'message' => 'Utilisateur non trouvé'], 404);
                    }
                    
                    jsonResponse(['success' => true, 'user' => $user]);
                } catch (PDOException $e) {
                    jsonResponse(['success' => false, 'message' => 'Erreur lors de la récupération du profil: ' . $e->getMessage()], 500);
                }
                break;
                
            default:
                jsonResponse(['success' => false, 'message' => 'Action non reconnue'], 400);
        }
        break;
        
    case 'POST':
        // Récupérer les données JSON de la requête
        $data = json_decode(file_get_contents('php://input'), true);
        
        switch ($action) {
            case 'update':
                // Vérifier que les données nécessaires sont présentes
                if (!isset($data['user_id']) || !isset($data['email'])) {
                    jsonResponse(['success' => false, 'message' => 'Données incomplètes'], 400);
                }
                
                // Vérifier que l'utilisateur met à jour son propre profil ou est administrateur
                if ($_SESSION['user_id'] != $data['user_id'] && !isAdmin()) {
                    jsonResponse(['success' => false, 'message' => 'Vous n\'êtes pas autorisé à modifier ce profil'], 403);
                }
                
                try {
                    $pdo = getDbConnection();
                    
                    // Vérifier si l'utilisateur existe
                    $checkStmt = $pdo->prepare("SELECT id, username FROM users WHERE id = ?");
                    $checkStmt->execute([$data['user_id']]);
                    $user = $checkStmt->fetch();
                    
                    if (!$user) {
                        jsonResponse(['success' => false, 'message' => 'Utilisateur non trouvé'], 404);
                    }
                    
                    // Vérifier si l'email existe déjà pour un autre utilisateur
                    $checkStmt = $pdo->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
                    $checkStmt->execute([$data['email'], $data['user_id']]);
                    if ($checkStmt->fetch()) {
                        jsonResponse(['success' => false, 'message' => 'Cet email est déjà utilisé'], 400);
                    }
                    
                    // Si un nouveau mot de passe est fourni
                    if (isset($data['new_password']) && !empty($data['new_password'])) {
                        // Vérifier que le mot de passe actuel est fourni
                        if (!isset($data['current_password']) || empty($data['current_password'])) {
                            jsonResponse(['success' => false, 'message' => 'Le mot de passe actuel est requis pour changer de mot de passe'], 400);
                        }
                        
                        // Vérifier que le mot de passe actuel est correct
                        $checkStmt = $pdo->prepare("SELECT password FROM users WHERE id = ?");
                        $checkStmt->execute([$data['user_id']]);
                        $userPassword = $checkStmt->fetch();
                        
                        if (!$userPassword || !password_verify($data['current_password'], $userPassword['password'])) {
                            jsonResponse(['success' => false, 'message' => 'Mot de passe actuel incorrect'], 400);
                        }
                        
                        // Hasher le nouveau mot de passe
                        $hashedPassword = password_hash($data['new_password'], PASSWORD_DEFAULT);
                        
                        // Mettre à jour l'email et le mot de passe
                        $stmt = $pdo->prepare("UPDATE users SET email = ?, password = ?, updated_at = NOW() WHERE id = ?");
                        $stmt->execute([$data['email'], $hashedPassword, $data['user_id']]);
                    } else {
                        // Mettre à jour uniquement l'email
                        $stmt = $pdo->prepare("UPDATE users SET email = ?, updated_at = NOW() WHERE id = ?");
                        $stmt->execute([$data['email'], $data['user_id']]);
                    }
                    
                    // Journaliser la mise à jour du profil
                    logActivity($_SESSION['username'], 'update', 'user', $data['user_id'], "Mise à jour du profil de {$user['username']}");
                    
                    jsonResponse([
                        'success' => true,
                        'message' => 'Profil mis à jour avec succès'
                    ]);
                } catch (PDOException $e) {
                    jsonResponse(['success' => false, 'message' => 'Erreur lors de la mise à jour du profil: ' . $e->getMessage()], 500);
                }
                break;
                
            default:
                jsonResponse(['success' => false, 'message' => 'Action non reconnue'], 400);
        }
        break;
        
    default:
        jsonResponse(['success' => false, 'message' => 'Méthode non autorisée'], 405);
}

/* Styles pour l'intégration WordPress - Système Juridique */

.juridique-dashboard-iframe {
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    background: #fff;
}

.juridique-stats-widget {
    background: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.juridique-stats-widget h3 {
    margin-top: 0;
    color: #333;
    font-size: 1.4em;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 10px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #0073aa;
}

.stat-number {
    display: block;
    font-size: 2em;
    font-weight: bold;
    color: #0073aa;
    margin-bottom: 5px;
}

.stat-date {
    display: block;
    font-size: 1.1em;
    font-weight: bold;
    color: #0073aa;
    margin-bottom: 5px;
}

.stat-label {
    display: block;
    font-size: 0.9em;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Styles pour les formulaires d'intégration */
.juridique-sync-form {
    background: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.juridique-sync-form h4 {
    margin-top: 0;
    color: #333;
}

.sync-button {
    background: #0073aa;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.sync-button:hover {
    background: #005a87;
}

.sync-button:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.sync-status {
    margin-top: 10px;
    padding: 10px;
    border-radius: 4px;
    display: none;
}

.sync-status.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.sync-status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.sync-status.loading {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Responsive design */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .juridique-dashboard-iframe {
        width: 100% !important;
        height: 400px !important;
    }
}

/* Styles pour l'administration WordPress */
.juridique-admin-page {
    max-width: 1200px;
    margin: 20px 0;
}

.juridique-admin-page .nav-tab-wrapper {
    margin-bottom: 20px;
}

.juridique-admin-section {
    background: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.juridique-admin-section h3 {
    margin-top: 0;
    color: #333;
    border-bottom: 1px solid #e1e1e1;
    padding-bottom: 10px;
}

/* Styles pour les notifications */
.juridique-notice {
    padding: 12px;
    margin: 15px 0;
    border-left: 4px solid #0073aa;
    background: #fff;
    box-shadow: 0 1px 1px rgba(0,0,0,0.04);
}

.juridique-notice.notice-success {
    border-left-color: #46b450;
}

.juridique-notice.notice-error {
    border-left-color: #dc3232;
}

.juridique-notice.notice-warning {
    border-left-color: #ffb900;
}

/* Animation de chargement */
.juridique-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

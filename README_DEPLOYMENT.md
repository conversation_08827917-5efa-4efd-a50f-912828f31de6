# Guide de déploiement du système de suivi des affaires juridiques

Ce guide explique comment déployer l'application sur un serveur local ou sur Internet.

## Déploiement sur un serveur local (Laragon)

### Prérequis
- Laragon installé (https://laragon.org/download/)
- PHP 7.4 ou supérieur
- MySQL 5.7 ou supérieur
- Apache 2.4 ou supérieur

### Étapes de déploiement

1. **Préparation de l'environnement**
   - <PERSON><PERSON><PERSON><PERSON>
   - Assurez-vous que MySQL et Apache sont en cours d'exécution

2. **Téléchargement des dépendances**
   - Accédez à http://localhost/download_dependencies.php
   - Ce script téléchargera toutes les dépendances externes (Bootstrap, jQuery, etc.) et les stockera localement

3. **Mise à jour des références**
   - Accédez à http://localhost/update_references.php
   - Ce script mettra à jour les références aux fichiers externes dans les fichiers HTML et JS

4. **Initialisation de la base de données**
   - Accédez à http://localhost/init_db.php
   - Ce script créera la base de données et les tables nécessaires
   - Il créera également un utilisateur administrateur par défaut

5. **Test de l'application**
   - Accédez à http://localhost/test_db.php pour vérifier que la base de données est correctement configurée
   - Accédez à http://localhost/ pour accéder à l'application
   - Connectez-vous avec les identifiants suivants:
     - Nom d'utilisateur: admin
     - Mot de passe: password

## Déploiement sur un serveur web (Internet)

### Prérequis
- Un hébergement web avec PHP 7.4 ou supérieur
- Une base de données MySQL 5.7 ou supérieur
- Accès FTP ou SSH à votre hébergement

### Étapes de déploiement

1. **Préparation des fichiers**
   - Exécutez localement les scripts download_dependencies.php et update_references.php
   - Cela garantira que tous les fichiers nécessaires sont présents et que les références sont correctes

2. **Transfert des fichiers**
   - Transférez tous les fichiers de l'application vers votre hébergement via FTP ou SSH
   - Assurez-vous de transférer également les fichiers dans le répertoire assets/

3. **Configuration de la base de données**
   - Créez une base de données MySQL sur votre hébergement
   - Modifiez le fichier config.php pour utiliser les informations de connexion de votre hébergement:
     ```php
     define('DB_HOST', 'votre_hote_mysql');
     define('DB_NAME', 'votre_nom_de_base_de_donnees');
     define('DB_USER', 'votre_utilisateur_mysql');
     define('DB_PASS', 'votre_mot_de_passe_mysql');
     define('DB_PORT', '3306');
     ```

4. **Initialisation de la base de données**
   - Accédez à https://votre-domaine.com/init_db.php
   - Ce script créera les tables nécessaires et un utilisateur administrateur par défaut

5. **Test de l'application**
   - Accédez à https://votre-domaine.com/test_db.php pour vérifier que la base de données est correctement configurée
   - Accédez à https://votre-domaine.com/ pour accéder à l'application
   - Connectez-vous avec les identifiants suivants:
     - Nom d'utilisateur: admin
     - Mot de passe: password

## Sécurité

Pour renforcer la sécurité de votre application en production:

1. **Activer HTTPS**
   - Décommentez les lignes suivantes dans le fichier .htaccess:
     ```
     RewriteCond %{HTTPS} off
     RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
     ```

2. **Modifier les identifiants par défaut**
   - Connectez-vous avec l'utilisateur administrateur par défaut
   - Allez dans la section "Profil" et changez le mot de passe

3. **Sauvegarder régulièrement la base de données**
   - Configurez des sauvegardes automatiques de la base de données
   - Stockez les sauvegardes dans un emplacement sécurisé

4. **Mettre à jour régulièrement les dépendances**
   - Exécutez périodiquement le script download_dependencies.php pour mettre à jour les bibliothèques externes

## Résolution des problèmes

Si vous rencontrez des problèmes lors du déploiement:

1. **Erreurs de connexion à la base de données**
   - Vérifiez que les informations de connexion dans config.php sont correctes
   - Vérifiez que la base de données existe et que l'utilisateur a les droits nécessaires

2. **Erreurs 500 (Internal Server Error)**
   - Vérifiez les journaux d'erreur d'Apache
   - Assurez-vous que les permissions des fichiers sont correctes (644 pour les fichiers, 755 pour les répertoires)

3. **Problèmes d'affichage**
   - Videz le cache de votre navigateur
   - Vérifiez que tous les fichiers CSS et JS sont correctement chargés (utilisez les outils de développement du navigateur)

4. **Problèmes de session**
   - Vérifiez que le répertoire de session PHP est accessible en écriture
   - Assurez-vous que les cookies sont activés dans votre navigateur

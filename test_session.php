<?php
// Script pour tester les fonctions de session et d'authentification

// Activer l'affichage des erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Démarrer la session
session_start();

// Inclure les fichiers nécessaires
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/auth.php';

// Fonction pour afficher l'état de la session
function displaySessionState() {
    echo "<h3>État de la session</h3>";
    echo "<pre>Session ID: " . session_id() . "</pre>";
    echo "<pre>Session Data: " . print_r($_SESSION, true) . "</pre>";
    
    echo "<p>isLoggedIn(): " . (isLoggedIn() ? "Oui" : "Non") . "</p>";
    echo "<p>isAdmin(): " . (isAdmin() ? "Oui" : "Non") . "</p>";
}

// Fonction pour créer une session d'administrateur
function createAdminSession() {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'admin';
    $_SESSION['user_role'] = 'admin';
    $_SESSION['last_activity'] = time();
    
    echo "<p>Session d'administrateur créée.</p>";
}

// Fonction pour détruire la session
function destroySession() {
    session_unset();
    session_destroy();
    
    echo "<p>Session détruite.</p>";
}

echo "<h1>Test des fonctions de session et d'authentification</h1>";

// Afficher l'état actuel de la session
displaySessionState();

// Traiter les actions
if (isset($_GET['action'])) {
    switch ($_GET['action']) {
        case 'create_admin':
            createAdminSession();
            break;
        case 'destroy':
            destroySession();
            break;
    }
    
    // Rediriger pour éviter les problèmes de rafraîchissement
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
}

// Afficher les liens d'action
echo "<h3>Actions</h3>";
echo "<ul>";
echo "<li><a href='?action=create_admin'>Créer une session d'administrateur</a></li>";
echo "<li><a href='?action=destroy'>Détruire la session</a></li>";
echo "</ul>";

// Tester l'accès aux API
echo "<h3>Test d'accès aux API</h3>";
echo "<ul>";
echo "<li><a href='api/users_api.php?action=list' target='_blank'>API des utilisateurs</a></li>";
echo "<li><a href='api/logs_api.php?action=list' target='_blank'>API des journaux d'activité</a></li>";
echo "</ul>";

// Afficher le contenu des fichiers de configuration et d'authentification
echo "<h3>Contenu des fichiers</h3>";

echo "<h4>config.php</h4>";
echo "<pre>" . htmlspecialchars(file_get_contents(__DIR__ . '/config.php')) . "</pre>";

echo "<h4>auth.php</h4>";
echo "<pre>" . htmlspecialchars(file_get_contents(__DIR__ . '/auth.php')) . "</pre>";

// Vérifier si les fonctions nécessaires existent
echo "<h3>Vérification des fonctions</h3>";
echo "<p>Fonction isLoggedIn: " . (function_exists('isLoggedIn') ? "Existe" : "N'existe pas") . "</p>";
echo "<p>Fonction isAdmin: " . (function_exists('isAdmin') ? "Existe" : "N'existe pas") . "</p>";
echo "<p>Fonction getDbConnection: " . (function_exists('getDbConnection') ? "Existe" : "N'existe pas") . "</p>";
echo "<p>Fonction jsonResponse: " . (function_exists('jsonResponse') ? "Existe" : "N'existe pas") . "</p>";
echo "<p>Fonction logActivity: " . (function_exists('logActivity') ? "Existe" : "N'existe pas") . "</p>";

<?php
// Script pour vérifier la structure de la table activity_logs
require_once('../app/config/config.php');

try {
    $pdo = getDbConnection();
    
    // Vérifier si la table activity_logs existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'activity_logs'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        echo "<p style='color: red;'>La table activity_logs n'existe pas.</p>";
        
        // Créer la table
        echo "<p>Création de la table activity_logs...</p>";
        $pdo->exec("CREATE TABLE IF NOT EXISTS activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL,
            action VARCHAR(20) NOT NULL,
            entity_type VARCHAR(20) NOT NULL,
            entity_id INT,
            details TEXT,
            ip_address VARCHAR(45),
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
        
        echo "<p style='color: green;'>Table activity_logs créée avec succès.</p>";
    } else {
        echo "<p style='color: green;'>La table activity_logs existe.</p>";
        
        // Vérifier la structure de la table
        $stmt = $pdo->query("DESCRIBE activity_logs");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>Structure de la table activity_logs :</h3>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            foreach ($column as $key => $value) {
                echo "<td>" . ($value === null ? 'NULL' : htmlspecialchars($value)) . "</td>";
            }
            echo "</tr>";
        }
        
        echo "</table>";
        
        // Vérifier si les colonnes action et entity_type sont de type VARCHAR
        $actionColumn = null;
        $entityTypeColumn = null;
        
        foreach ($columns as $column) {
            if ($column['Field'] === 'action') {
                $actionColumn = $column;
            }
            if ($column['Field'] === 'entity_type') {
                $entityTypeColumn = $column;
            }
        }
        
        if ($actionColumn && strpos($actionColumn['Type'], 'enum') !== false) {
            echo "<p style='color: red;'>La colonne 'action' est de type ENUM. Elle devrait être de type VARCHAR.</p>";
            
            // Modifier la colonne
            echo "<p>Modification de la colonne 'action'...</p>";
            $pdo->exec("ALTER TABLE activity_logs MODIFY COLUMN action VARCHAR(20) NOT NULL");
            
            echo "<p style='color: green;'>Colonne 'action' modifiée avec succès.</p>";
        } else if ($actionColumn) {
            echo "<p style='color: green;'>La colonne 'action' est de type " . $actionColumn['Type'] . ".</p>";
        }
        
        if ($entityTypeColumn && strpos($entityTypeColumn['Type'], 'enum') !== false) {
            echo "<p style='color: red;'>La colonne 'entity_type' est de type ENUM. Elle devrait être de type VARCHAR.</p>";
            
            // Modifier la colonne
            echo "<p>Modification de la colonne 'entity_type'...</p>";
            $pdo->exec("ALTER TABLE activity_logs MODIFY COLUMN entity_type VARCHAR(20) NOT NULL");
            
            echo "<p style='color: green;'>Colonne 'entity_type' modifiée avec succès.</p>";
        } else if ($entityTypeColumn) {
            echo "<p style='color: green;'>La colonne 'entity_type' est de type " . $entityTypeColumn['Type'] . ".</p>";
        }
    }
    
    // Vérifier s'il y a des données dans la table
    $stmt = $pdo->query("SELECT COUNT(*) FROM activity_logs");
    $count = $stmt->fetchColumn();
    
    echo "<p>Nombre d'entrées dans la table : " . $count . "</p>";
    
    if ($count === 0) {
        echo "<p>Ajout d'une entrée de test...</p>";
        
        // Ajouter une entrée de test
        $stmt = $pdo->prepare("INSERT INTO activity_logs (username, action, entity_type, details, ip_address) 
                              VALUES (?, ?, ?, ?, ?)");
        $stmt->execute(['admin', 'test', 'database', 'Test de la table activity_logs', $_SERVER['REMOTE_ADDR']]);
        
        echo "<p style='color: green;'>Entrée de test ajoutée avec succès.</p>";
    }
    
    // Afficher les 5 dernières entrées
    $stmt = $pdo->query("SELECT * FROM activity_logs ORDER BY timestamp DESC LIMIT 5");
    $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($logs) > 0) {
        echo "<h3>5 dernières entrées :</h3>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr>";
        foreach (array_keys($logs[0]) as $key) {
            echo "<th>" . htmlspecialchars($key) . "</th>";
        }
        echo "</tr>";
        
        foreach ($logs as $log) {
            echo "<tr>";
            foreach ($log as $value) {
                echo "<td>" . ($value === null ? 'NULL' : htmlspecialchars($value)) . "</td>";
            }
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Erreur : " . $e->getMessage() . "</p>";
}

<?php
// API pour la gestion des utilisateurs

// Autoriser les requêtes CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

// Si c'est une requête OPTIONS (preflight), renvoyer 200 OK
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Démarrer la session
session_start();

// Inclure les fichiers nécessaires
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../auth.php';

// Si les fichiers ne sont pas trouvés, essayer avec d'autres chemins
if (!function_exists('getDbConnection')) {
    require_once __DIR__ . '/../app/config/config.php';
    require_once __DIR__ . '/../app/auth.php';
}

// Vérifier si l'utilisateur est connecté et est administrateur
if (!isLoggedIn()) {
    jsonResponse(['success' => false, 'message' => 'Non authentifié'], 401);
}

if (!isAdmin()) {
    jsonResponse(['success' => false, 'message' => 'Accès refusé. Seuls les administrateurs peuvent gérer les utilisateurs'], 403);
}

// Récupérer la méthode HTTP et l'action
$method = $_SERVER['REQUEST_METHOD'];
$action = isset($_GET['action']) ? $_GET['action'] : '';

// Traiter la requête en fonction de la méthode et de l'action
switch ($method) {
    case 'GET':
        switch ($action) {
            case 'list':
                // Récupérer la liste des utilisateurs
                try {
                    $pdo = getDbConnection();
                    $stmt = $pdo->query("SELECT id, username, email, role, last_login, created_at FROM users ORDER BY id");
                    $users = $stmt->fetchAll();

                    jsonResponse(['success' => true, 'users' => $users]);
                } catch (PDOException $e) {
                    jsonResponse(['success' => false, 'message' => 'Erreur lors de la récupération des utilisateurs: ' . $e->getMessage()], 500);
                }
                break;

            case 'get':
                // Vérifier que l'ID de l'utilisateur est fourni
                if (!isset($_GET['id'])) {
                    jsonResponse(['success' => false, 'message' => 'ID de l\'utilisateur requis'], 400);
                }

                // Récupérer les informations d'un utilisateur spécifique
                try {
                    $pdo = getDbConnection();
                    $stmt = $pdo->prepare("SELECT id, username, email, role, last_login, created_at FROM users WHERE id = ?");
                    $stmt->execute([$_GET['id']]);
                    $user = $stmt->fetch();

                    if (!$user) {
                        jsonResponse(['success' => false, 'message' => 'Utilisateur non trouvé'], 404);
                    }

                    jsonResponse(['success' => true, 'user' => $user]);
                } catch (PDOException $e) {
                    jsonResponse(['success' => false, 'message' => 'Erreur lors de la récupération de l\'utilisateur: ' . $e->getMessage()], 500);
                }
                break;

            default:
                jsonResponse(['success' => false, 'message' => 'Action non reconnue'], 400);
        }
        break;

    case 'POST':
        // Récupérer les données JSON de la requête
        $data = json_decode(file_get_contents('php://input'), true);

        switch ($action) {
            case 'create':
                // Vérifier que les données nécessaires sont présentes
                if (!isset($data['username']) || !isset($data['password']) || !isset($data['email']) || !isset($data['role'])) {
                    jsonResponse(['success' => false, 'message' => 'Données incomplètes'], 400);
                }

                // Créer un nouvel utilisateur
                try {
                    $result = createUser($data['username'], $data['password'], $data['email'], $data['role']);
                    jsonResponse($result, $result['success'] ? 201 : 400);
                } catch (Exception $e) {
                    jsonResponse(['success' => false, 'message' => 'Erreur lors de la création de l\'utilisateur: ' . $e->getMessage()], 500);
                }
                break;

            case 'update':
                // Vérifier que les données nécessaires sont présentes
                if (!isset($data['user_id']) || !isset($data['username']) || !isset($data['email']) || !isset($data['role'])) {
                    jsonResponse(['success' => false, 'message' => 'Données incomplètes'], 400);
                }

                // Mettre à jour un utilisateur existant
                try {
                    $pdo = getDbConnection();

                    // Vérifier si l'utilisateur existe
                    $checkStmt = $pdo->prepare("SELECT id FROM users WHERE id = ?");
                    $checkStmt->execute([$data['user_id']]);
                    if (!$checkStmt->fetch()) {
                        jsonResponse(['success' => false, 'message' => 'Utilisateur non trouvé'], 404);
                    }

                    // Vérifier si le nom d'utilisateur existe déjà pour un autre utilisateur
                    $checkStmt = $pdo->prepare("SELECT id FROM users WHERE username = ? AND id != ?");
                    $checkStmt->execute([$data['username'], $data['user_id']]);
                    if ($checkStmt->fetch()) {
                        jsonResponse(['success' => false, 'message' => 'Ce nom d\'utilisateur existe déjà'], 400);
                    }

                    // Vérifier si l'email existe déjà pour un autre utilisateur
                    $checkStmt = $pdo->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
                    $checkStmt->execute([$data['email'], $data['user_id']]);
                    if ($checkStmt->fetch()) {
                        jsonResponse(['success' => false, 'message' => 'Cet email est déjà utilisé'], 400);
                    }

                    // Préparer la requête de mise à jour
                    if (isset($data['password']) && !empty($data['password'])) {
                        // Mettre à jour avec le mot de passe
                        $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
                        $stmt = $pdo->prepare("UPDATE users SET username = ?, email = ?, role = ?, password = ?, updated_at = NOW() WHERE id = ?");
                        $stmt->execute([$data['username'], $data['email'], $data['role'], $hashedPassword, $data['user_id']]);
                    } else {
                        // Mettre à jour sans le mot de passe
                        $stmt = $pdo->prepare("UPDATE users SET username = ?, email = ?, role = ?, updated_at = NOW() WHERE id = ?");
                        $stmt->execute([$data['username'], $data['email'], $data['role'], $data['user_id']]);
                    }

                    // Journaliser la mise à jour de l'utilisateur
                    logActivity($_SESSION['username'], 'update', 'user', $data['user_id'], "Mise à jour de l'utilisateur {$data['username']}");

                    jsonResponse([
                        'success' => true,
                        'message' => 'Utilisateur mis à jour avec succès'
                    ]);
                } catch (PDOException $e) {
                    jsonResponse(['success' => false, 'message' => 'Erreur lors de la mise à jour de l\'utilisateur: ' . $e->getMessage()], 500);
                }
                break;

            case 'delete':
                // Vérifier que l'ID de l'utilisateur est fourni
                if (!isset($data['user_id'])) {
                    jsonResponse(['success' => false, 'message' => 'ID de l\'utilisateur requis'], 400);
                }

                // Empêcher la suppression de son propre compte
                if ($_SESSION['user_id'] == $data['user_id']) {
                    jsonResponse(['success' => false, 'message' => 'Vous ne pouvez pas supprimer votre propre compte'], 400);
                }

                // Supprimer un utilisateur
                try {
                    $pdo = getDbConnection();

                    // Vérifier si l'utilisateur existe
                    $checkStmt = $pdo->prepare("SELECT username FROM users WHERE id = ?");
                    $checkStmt->execute([$data['user_id']]);
                    $user = $checkStmt->fetch();

                    if (!$user) {
                        jsonResponse(['success' => false, 'message' => 'Utilisateur non trouvé'], 404);
                    }

                    // Supprimer l'utilisateur
                    $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
                    $stmt->execute([$data['user_id']]);

                    // Journaliser la suppression de l'utilisateur
                    logActivity($_SESSION['username'], 'delete', 'user', $data['user_id'], "Suppression de l'utilisateur {$user['username']}");

                    jsonResponse([
                        'success' => true,
                        'message' => 'Utilisateur supprimé avec succès'
                    ]);
                } catch (PDOException $e) {
                    jsonResponse(['success' => false, 'message' => 'Erreur lors de la suppression de l\'utilisateur: ' . $e->getMessage()], 500);
                }
                break;

            default:
                jsonResponse(['success' => false, 'message' => 'Action non reconnue'], 400);
        }
        break;

    default:
        jsonResponse(['success' => false, 'message' => 'Méthode non autorisée'], 405);
}

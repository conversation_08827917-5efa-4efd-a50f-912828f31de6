<?php

/**
 * Script pour changer le mot de passe admin dans la base de données
 * Usage: php change_admin_password.php
 */

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use Illuminate\Support\Facades\Hash;

// Charger l'application Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== CHANGEMENT DU MOT DE PASSE ADMIN ===\n\n";

try {
    // Paramètres
    $adminEmail = '<EMAIL>';
    $newPassword = 'admin123';
    
    echo "Recherche de l'utilisateur admin avec l'email: {$adminEmail}\n";
    
    // Chercher l'utilisateur admin
    $admin = User::where('email', $adminEmail)->first();
    
    if (!$admin) {
        echo "❌ Aucun utilisateur trouvé avec l'email: {$adminEmail}\n";
        echo "Création d'un nouvel utilisateur admin...\n";
        
        // Créer un nouvel utilisateur admin
        $admin = User::create([
            'name' => 'Administrateur SIRH',
            'email' => $adminEmail,
            'password' => Hash::make($newPassword),
            'email_verified_at' => now(),
        ]);
        
        echo "✅ Nouvel utilisateur admin créé avec succès !\n";
    } else {
        echo "✅ Utilisateur admin trouvé: {$admin->name}\n";
        echo "Mise à jour du mot de passe...\n";
        
        // Mettre à jour le mot de passe
        $admin->update([
            'password' => Hash::make($newPassword),
        ]);
        
        echo "✅ Mot de passe mis à jour avec succès !\n";
    }
    
    echo "\n=== INFORMATIONS DE CONNEXION ===\n";
    echo "Email: {$admin->email}\n";
    echo "Mot de passe: {$newPassword}\n";
    echo "Nom: {$admin->name}\n";
    echo "ID: {$admin->id}\n";
    echo "Créé le: " . $admin->created_at->format('d/m/Y H:i:s') . "\n";
    echo "Mis à jour le: " . $admin->updated_at->format('d/m/Y H:i:s') . "\n";
    
    // Vérifier que le mot de passe fonctionne
    if (Hash::check($newPassword, $admin->password)) {
        echo "\n✅ Vérification: Le mot de passe est correct !\n";
    } else {
        echo "\n❌ Erreur: Le mot de passe ne correspond pas !\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
    exit(1);
}

echo "\n🎉 Opération terminée avec succès !\n";
echo "Vous pouvez maintenant vous connecter avec:\n";
echo "Email: <EMAIL>\n";
echo "Mot de passe: admin123\n";

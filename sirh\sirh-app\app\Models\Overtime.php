<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Overtime extends Model
{
    use HasFactory;

    protected $fillable = [
        'worker_id',
        'date',
        'hours',
        'rate_multiplier',
        'total_pay',
        'description',
        'approved_by',
        'status'
    ];

    protected $casts = [
        'date' => 'date',
        'hours' => 'decimal:2',
        'rate_multiplier' => 'decimal:2',
        'total_pay' => 'decimal:2',
    ];

    /**
     * Relation avec le travailleur
     */
    public function worker(): BelongsTo
    {
        return $this->belongsTo(Worker::class);
    }

    /**
     * Calculer automatiquement le total à payer
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($overtime) {
            if ($overtime->worker && $overtime->hours && $overtime->rate_multiplier) {
                $overtime->total_pay = $overtime->worker->hourly_rate * $overtime->hours * $overtime->rate_multiplier;
            }
        });
    }
}

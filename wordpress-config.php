<?php
/**
 * Configuration WordPress pour le système juridique
 * Ce fichier doit être placé dans le dossier WordPress
 */

// Configuration de la base de données WordPress
define('DB_NAME', 'juridique_cms');
define('DB_USER', 'root');
define('DB_PASSWORD', '');
define('DB_HOST', 'localhost');
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', '');

// Clés de sécurité WordPress (à générer sur https://api.wordpress.org/secret-key/1.1/salt/)
define('AUTH_KEY',         'votre-clé-auth-unique');
define('SECURE_AUTH_KEY',  'votre-clé-secure-auth-unique');
define('LOGGED_IN_KEY',    'votre-clé-logged-in-unique');
define('NONCE_KEY',        'votre-clé-nonce-unique');
define('AUTH_SALT',        'votre-salt-auth-unique');
define('SECURE_AUTH_SALT', 'votre-salt-secure-auth-unique');
define('LOGGED_IN_SALT',   'votre-salt-logged-in-unique');
define('NONCE_SALT',       'votre-salt-nonce-unique');

// Préfixe des tables WordPress
$table_prefix = 'wp_';

// Mode debug (à désactiver en production)
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);

// Configuration pour l'intégration avec votre application
define('JURIDIQUE_APP_URL', 'http://localhost/juridique-app');
define('JURIDIQUE_API_KEY', 'votre-clé-api-sécurisée');

// Chemins WordPress
if (!defined('ABSPATH')) {
    define('ABSPATH', __DIR__ . '/');
}

require_once ABSPATH . 'wp-settings.php';

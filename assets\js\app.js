// Vérifier si l'utilisateur est connecté
const currentUser = localStorage.getItem('currentUser');
if (!currentUser) {
    // Rediriger vers la page de connexion si l'utilisateur n'est pas connecté
    window.location.href = '../../views/auth/login.html';
}

// Variables pour stocker les données
let affaires = [];
let avocats = [];

// Éléments DOM
const affairesTab = document.getElementById('affaires-tab');
const avocatsTab = document.getElementById('avocats-tab');
const affairesSection = document.getElementById('affaires-section');
const avocatsSection = document.getElementById('avocats-section');
const affairesListe = document.getElementById('affaires-liste');
const avocatsListe = document.getElementById('avocats-liste');
const formAffaire = document.getElementById('form-affaire');
const formAvocat = document.getElementById('form-avocat');
const btnNouvelleAffaire = document.getElementById('btn-nouvelle-affaire');
const btnNouvelAvocat = document.getElementById('btn-nouvel-avocat');
const btnAnnulerAffaire = document.getElementById('btn-annuler-affaire');
const btnAnnulerAvocat = document.getElementById('btn-annuler-avocat');
const affaireForm = document.getElementById('affaire-form');
const avocatForm = document.getElementById('avocat-form');
const formAffaireTitre = document.getElementById('form-affaire-titre');
const formAvocatTitre = document.getElementById('form-avocat-titre');
const maitreAffaire = document.getElementById('maitre-affaire');
const dashboardTab = document.getElementById('dashboard-tab');
const dashboardSection = document.getElementById('dashboard-section');

// Variables globales
let modeEditionAffaire = false;
let modeEditionAvocat = false;
let affaireEnEdition = null;
let avocatEnEdition = null;
let affairesFiltrees = [];

// Initialisation
document.addEventListener('DOMContentLoaded', async () => {
    // Afficher les informations de l'utilisateur connecté
    const userInfoElement = document.getElementById('user-info');
    if (userInfoElement) {
        const userData = JSON.parse(currentUser);
        userInfoElement.textContent = `Connecté en tant que: ${userData.username}`;

        // Afficher les outils d'administration uniquement pour les administrateurs
        const adminTools = document.getElementById('admin-tools');
        if (adminTools && userData.role === 'admin') {
            adminTools.style.display = 'inline-block';

            // Ajouter le gestionnaire d'événement pour le bouton de réinitialisation de la base de données
            const resetDbBtn = document.getElementById('admin-reset-db');
            if (resetDbBtn) {
                resetDbBtn.addEventListener('click', async () => {
                    if (confirm('ATTENTION : Cette action va réinitialiser complètement la base de données. Toutes les données seront perdues. Voulez-vous continuer ?')) {
                        try {
                            // Afficher un indicateur de chargement
                            afficherSucces('Réinitialisation de la base de données en cours...');

                            // Réinitialiser la base de données
                            await DB.resetDatabase();

                            // Ajouter un log pour indiquer la réinitialisation (si la fonction existe)
                            if (typeof DB.addActivityLog === 'function') {
                                try {
                                    await DB.addActivityLog({
                                        username: userData.username,
                                        action: 'reset',
                                        entity_type: 'database',
                                        details: 'Réinitialisation complète de la base de données'
                                    });
                                } catch (logError) {
                                    console.warn('Impossible de journaliser la réinitialisation:', logError);
                                    // Continuer l'exécution même si la journalisation échoue
                                }
                            }

                            // Recharger les données
                            await chargerDonnees();

                            // Afficher un message de succès
                            afficherSucces('Base de données réinitialisée avec succès.');
                        } catch (error) {
                            console.error('Erreur lors de la réinitialisation de la base de données:', error);
                            afficherErreur('Erreur lors de la réinitialisation de la base de données. Veuillez rafraîchir la page et réessayer.');
                        }
                    }
                });
            }
        }
    }

    // Gérer le bouton de déconnexion
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', () => {
            // Appeler l'API de déconnexion pour détruire la session côté serveur
            fetch('../../app/api/auth_api.php?action=logout', {
                method: 'POST',
                credentials: 'include'
            })
            .then(response => response.json())
            .then(() => {
                // Supprimer les informations de l'utilisateur du localStorage
                localStorage.removeItem('currentUser');
                // Rediriger vers la page de connexion
                window.location.href = '../../views/auth/login.html';
            })
            .catch(error => {
                console.error('Erreur lors de la déconnexion:', error);
                // Supprimer quand même les informations de l'utilisateur du localStorage
                localStorage.removeItem('currentUser');
                // Rediriger vers la page de connexion
                window.location.href = '../../views/auth/login.html';
            });
        });
    }
    try {
        // Initialiser la base de données
        await DB.initDatabase();

        // Vérifier si des données existent déjà
        const affairesExistantes = await DB.getAllAffaires();
        const avocatsExistants = await DB.getAllAvocats();

        // Si aucune donnée n'existe, ajouter des données de test
        if (affairesExistantes.length === 0 && avocatsExistants.length === 0) {
            await DB.addTestData();
        }

        // Charger les données depuis la base de données
        await chargerDonnees();

        // Afficher la section Affaires par défaut et cacher les autres sections
        // Cacher complètement le dashboard selon les préférences de l'utilisateur
        dashboardSection.style.display = 'none';
        dashboardTab.style.display = 'none'; // Cacher l'onglet dashboard
        affairesSection.style.display = 'block';
        avocatsSection.style.display = 'none';
    } catch (error) {
        console.error('Erreur lors de l\'initialisation:', error);
        alert('Erreur lors de l\'initialisation de l\'application. Veuillez réessayer.');
    }

    // Gestion des onglets
    affairesTab.addEventListener('click', (e) => {
        e.preventDefault();
        affairesTab.classList.add('active');
        avocatsTab.classList.remove('active');
        dashboardTab.classList.remove('active');
        affairesSection.style.display = 'block';
        avocatsSection.style.display = 'none';
        dashboardSection.style.display = 'none';
    });

    avocatsTab.addEventListener('click', (e) => {
        e.preventDefault();
        avocatsTab.classList.add('active');
        affairesTab.classList.remove('active');
        dashboardTab.classList.remove('active');
        avocatsSection.style.display = 'block';
        affairesSection.style.display = 'none';
        dashboardSection.style.display = 'none';
    });

    dashboardTab.addEventListener('click', (e) => {
        e.preventDefault();
        dashboardTab.classList.add('active');
        affairesTab.classList.remove('active');
        avocatsTab.classList.remove('active');
        dashboardSection.style.display = 'block';
        affairesSection.style.display = 'none';
        avocatsSection.style.display = 'none';
        mettreAJourTableauBord();
    });

    // Gestion des boutons
    btnNouvelleAffaire.addEventListener('click', () => {
        modeEditionAffaire = false;
        affaireEnEdition = null;
        formAffaireTitre.textContent = 'Nouvelle Affaire';
        affaireForm.reset();
        formAffaire.style.display = 'block';

        // Auto-focus sur le premier champ du formulaire (numéro d'affaire)
        setTimeout(() => {
            document.getElementById('numero-affaire').focus();
        }, 100); // Petit délai pour s'assurer que le formulaire est affiché
    });

    btnNouvelAvocat.addEventListener('click', () => {
        modeEditionAvocat = false;
        avocatEnEdition = null;
        formAvocatTitre.textContent = 'Nouvel Avocat';
        avocatForm.reset();
        formAvocat.style.display = 'block';
    });

    btnAnnulerAffaire.addEventListener('click', () => {
        formAffaire.style.display = 'none';
    });

    btnAnnulerAvocat.addEventListener('click', () => {
        formAvocat.style.display = 'none';
    });

    // Soumission des formulaires
    affaireForm.addEventListener('submit', (e) => {
        e.preventDefault();
        enregistrerAffaire();
    });

    avocatForm.addEventListener('submit', (e) => {
        e.preventDefault();
        enregistrerAvocat();
    });

    // Gestion de la date de validité du contrat (1 an après la date de signature)
    const dateSignature = document.getElementById('date-signature');
    const validiteContrat = document.getElementById('validite-contrat');

    dateSignature.addEventListener('change', () => {
        if (dateSignature.value) {
            // Calculer la date de validité (1 an après la date de signature)
            const dateSign = new Date(dateSignature.value);
            const dateValidite = new Date(dateSign);
            dateValidite.setFullYear(dateValidite.getFullYear() + 1);

            // Formater la date au format YYYY-MM-DD pour l'input date
            const year = dateValidite.getFullYear();
            const month = String(dateValidite.getMonth() + 1).padStart(2, '0');
            const day = String(dateValidite.getDate()).padStart(2, '0');
            validiteContrat.value = `${year}-${month}-${day}`;
        }
    });

    // Gestion de la recherche et des filtres
    const rechercheAffaire = document.getElementById('recherche-affaire');
    const btnRechercheAffaire = document.getElementById('btn-recherche-affaire');
    const filtreStatut = document.getElementById('filtre-statut');
    const filtreNature = document.getElementById('filtre-nature');

    // Fonction pour effectuer la recherche lorsqu'on appuie sur Entrée
    rechercheAffaire.addEventListener('keyup', (e) => {
        if (e.key === 'Enter') {
            filtrerAffaires();
        }
    });

    // Fonction pour effectuer la recherche lorsqu'on clique sur le bouton
    btnRechercheAffaire.addEventListener('click', () => {
        filtrerAffaires();
    });

    // Fonction pour filtrer par statut
    filtreStatut.addEventListener('change', () => {
        filtrerAffaires();
    });

    // Fonction pour filtrer par nature
    filtreNature.addEventListener('change', () => {
        filtrerAffaires();
    });

    // Fonction pour réinitialiser les filtres
    const btnResetFiltres = document.getElementById('btn-reset-filtres');
    btnResetFiltres.addEventListener('click', () => {
        document.getElementById('recherche-affaire').value = '';
        document.getElementById('filtre-statut').value = '';
        document.getElementById('filtre-nature').value = '';
        afficherAffaires(affaires); // Afficher toutes les affaires
        affairesFiltrees = []; // Réinitialiser les affaires filtrées
    });
});

// Fonction pour charger les données depuis la base de données
async function chargerDonnees() {
    try {
        // Récupérer toutes les affaires et tous les avocats depuis IndexedDB
        affaires = await DB.getAllAffaires();
        avocats = await DB.getAllAvocats();

        // Afficher les données
        afficherAffaires();
        afficherAvocats();
        chargerListeAvocats();
    } catch (error) {
        console.error('Erreur lors du chargement des données:', error);
        afficherErreur('Erreur lors du chargement des données. Veuillez rafraîchir la page.');
    }
}

// Fonctions pour les affaires
function afficherAffaires(affairesAfficher = null) {
    affairesListe.innerHTML = '';

    // Utiliser les affaires filtrées si elles existent, sinon utiliser toutes les affaires
    const affairesAAfficher = affairesAfficher || affaires;

    // Mettre à jour le compteur d'affaires
    const compteurAffaires = document.getElementById('compteur-affaires');
    compteurAffaires.textContent = `Affaires trouvées : ${affairesAAfficher.length}`;

    if (affairesAAfficher.length === 0) {
        const tr = document.createElement('tr');
        tr.innerHTML = `<td colspan="7" class="text-center">Aucune affaire trouvée</td>`;
        affairesListe.appendChild(tr);
        return;
    }

    // Vérifier si l'utilisateur est administrateur pour autoriser la suppression
    const userData = JSON.parse(localStorage.getItem('currentUser'));
    const isAdmin = userData && userData.role === 'admin';

    affairesAAfficher.forEach(affaire => {
        const tr = document.createElement('tr');

        // Formatage des données pour l'affichage
        const natureAffichee = formaterNature(affaire.nature);
        const juridictionAffichee = formaterJuridiction(affaire.juridiction);
        const statutAffiche = formaterStatut(affaire.statut);
        const nomMaitre = trouverNomAvocat(affaire.maitre);

        // Bouton de suppression différent selon le rôle de l'utilisateur
        let deleteButton = '';
        if (isAdmin) {
            // Pour l'administrateur, le bouton est actif
            deleteButton = `<button class="btn btn-sm btn-danger delete-affaire" data-id="${affaire.id}">Supprimer</button>`;
        } else {
            // Pour les autres utilisateurs, le bouton est désactivé
            deleteButton = `<button class="btn btn-sm btn-secondary" disabled title="Seul l'administrateur peut supprimer">Supprimer</button>`;
        }

        tr.innerHTML = `
            <td class="cell-numero">${affaire.numero}</td>
            <td class="cell-nature">${natureAffichee}</td>
            <td class="cell-objet text-truncate" title="${affaire.objet}">${affaire.objet}</td>
            <td class="parties-cell" title="${affaire.parties || '-'}">${affaire.parties || '-'}</td>
            <td class="cell-juridiction">${juridictionAffichee}</td>
            <td class="cell-statut">${statutAffiche}</td>
            <td class="cell-maitre">${nomMaitre}</td>
            <td class="action-buttons">
                <button class="btn btn-sm btn-warning edit-affaire" data-id="${affaire.id}">Modifier</button>
                ${deleteButton}
            </td>
        `;

        affairesListe.appendChild(tr);
    });

    // Ajout des écouteurs d'événements pour les boutons d'action
    document.querySelectorAll('.edit-affaire').forEach(btn => {
        btn.addEventListener('click', () => {
            const id = parseInt(btn.getAttribute('data-id'));
            editerAffaire(id);
        });
    });

    // Ajout des écouteurs d'événements pour les boutons de suppression (seulement pour les boutons actifs)
    document.querySelectorAll('.delete-affaire').forEach(btn => {
        btn.addEventListener('click', () => {
            const id = parseInt(btn.getAttribute('data-id'));
            supprimerAffaire(id);
        });
    });
}

// Fonction pour filtrer les affaires selon les critères de recherche
function filtrerAffaires() {
    const termeRecherche = document.getElementById('recherche-affaire').value.toLowerCase();
    const statutFiltre = document.getElementById('filtre-statut').value;
    const natureFiltre = document.getElementById('filtre-nature').value;

    // Filtrer les affaires selon les critères
    affairesFiltrees = affaires.filter(affaire => {
        // Vérifier si l'affaire correspond au terme de recherche
        const correspondRecherche = termeRecherche === '' ||
            affaire.numero.toLowerCase().includes(termeRecherche) ||
            affaire.objet.toLowerCase().includes(termeRecherche) ||
            (affaire.parties && affaire.parties.toLowerCase().includes(termeRecherche));

        // Vérifier si l'affaire correspond au filtre de statut
        const correspondStatut = statutFiltre === '' || affaire.statut === statutFiltre;

        // Vérifier si l'affaire correspond au filtre de nature
        const correspondNature = natureFiltre === '' || affaire.nature === natureFiltre;

        // Retourner true si l'affaire correspond à tous les critères
        return correspondRecherche && correspondStatut && correspondNature;
    });

    // Afficher les affaires filtrées
    afficherAffaires(affairesFiltrees);
}

function chargerListeAvocats() {
    maitreAffaire.innerHTML = '<option value="">Sélectionner...</option>';

    avocats.forEach(avocat => {
        const option = document.createElement('option');
        option.value = avocat.id;
        option.textContent = avocat.nom;
        maitreAffaire.appendChild(option);
    });
}

async function editerAffaire(id) {
    try {
        const affaire = await DB.getAffaireById(id);
        if (!affaire) {
            alert('Affaire non trouvée.');
            return;
        }

        affaireEnEdition = affaire;
        modeEditionAffaire = true;
        formAffaireTitre.textContent = 'Modifier l\'Affaire';

        // Remplir le formulaire avec les données de l'affaire
        document.getElementById('numero-affaire').value = affaire.numero;
        document.getElementById('nature-affaire').value = affaire.nature;
        document.getElementById('objet-affaire').value = affaire.objet;
        document.getElementById('parties-affaire').value = affaire.parties;
        document.getElementById('juridiction-affaire').value = affaire.juridiction;
        document.getElementById('engagement-affaire').value = affaire.engagement;
        document.getElementById('date-engagement').value = affaire.dateEngagement;
        document.getElementById('date-jugement').value = affaire.dateJugement || '';
        document.getElementById('statut-affaire').value = affaire.statut;
        document.getElementById('dispositif-affaire').value = affaire.dispositif || '';
        document.getElementById('montant-affaire').value = affaire.montant;
        document.getElementById('maitre-affaire').value = affaire.maitre;

        formAffaire.style.display = 'block';
    } catch (error) {
        console.error('Erreur lors de l\'édition de l\'affaire:', error);
        alert('Erreur lors de l\'édition de l\'affaire. Veuillez réessayer.');
    }
}

async function supprimerAffaire(id) {
    // Vérifier si l'utilisateur est administrateur (double vérification de sécurité)
    const userData = JSON.parse(localStorage.getItem('currentUser'));
    if (!userData || userData.role !== 'admin') {
        afficherErreur('Seul l\'administrateur peut supprimer des affaires.');
        return;
    }

    if (confirm('Êtes-vous sûr de vouloir supprimer cette affaire ?')) {
        try {
            // Récupérer les informations de l'affaire avant de la supprimer pour le journal
            const affaire = await DB.getAffaireById(id);
            if (!affaire) {
                throw new Error('Affaire non trouvée');
            }

            await DB.deleteAffaire(id);

            // Journaliser la suppression (si la fonction existe)
            if (typeof DB.addActivityLog === 'function') {
                try {
                    await DB.addActivityLog({
                        username: userData.username,
                        action: 'delete',
                        entity_type: 'affaire',
                        entity_id: id,
                        details: `Suppression de l'affaire ${affaire.numero} - ${affaire.objet}`
                    });
                } catch (logError) {
                    console.warn('Impossible de journaliser la suppression:', logError);
                    // Continuer l'exécution même si la journalisation échoue
                }
            }

            await chargerDonnees(); // Recharger les données depuis la base de données

            // Si des filtres sont actifs, réappliquer les filtres
            const termeRecherche = document.getElementById('recherche-affaire').value;
            const statutFiltre = document.getElementById('filtre-statut').value;
            const natureFiltre = document.getElementById('filtre-nature').value;

            if (termeRecherche !== '' || statutFiltre !== '' || natureFiltre !== '') {
                filtrerAffaires();
            }

            afficherSucces('Affaire supprimée avec succès');
        } catch (error) {
            console.error('Erreur lors de la suppression de l\'affaire:', error);
            afficherErreur('Erreur lors de la suppression de l\'affaire. Veuillez réessayer.');
        }
    }
}

async function enregistrerAffaire() {
    try {
        const nouvelleAffaire = {
            numero: document.getElementById('numero-affaire').value,
            nature: document.getElementById('nature-affaire').value,
            objet: document.getElementById('objet-affaire').value,
            parties: document.getElementById('parties-affaire').value,
            juridiction: document.getElementById('juridiction-affaire').value,
            engagement: document.getElementById('engagement-affaire').value,
            dateEngagement: document.getElementById('date-engagement').value,
            dateJugement: document.getElementById('date-jugement').value,
            statut: document.getElementById('statut-affaire').value,
            dispositif: document.getElementById('dispositif-affaire').value,
            montant: parseFloat(document.getElementById('montant-affaire').value) || 0,
            maitre: parseInt(document.getElementById('maitre-affaire').value)
        };

        // Récupérer les informations de l'utilisateur connecté
        const userData = JSON.parse(localStorage.getItem('currentUser'));
        const username = userData ? userData.username : 'Utilisateur inconnu';

        if (modeEditionAffaire && affaireEnEdition) {
            nouvelleAffaire.id = affaireEnEdition.id;
            await DB.updateAffaire(nouvelleAffaire);

            // Journaliser la modification d'affaire (si la fonction existe)
            if (typeof DB.addActivityLog === 'function') {
                try {
                    await DB.addActivityLog({
                        username: username,
                        action: 'update',
                        entity_type: 'affaire',
                        entity_id: nouvelleAffaire.id,
                        details: `Modification de l'affaire ${nouvelleAffaire.numero} - ${nouvelleAffaire.objet}`
                    });
                } catch (logError) {
                    console.warn('Impossible de journaliser la modification:', logError);
                    // Continuer l'exécution même si la journalisation échoue
                }
            }
        } else {
            const newId = await DB.addAffaire(nouvelleAffaire);

            // Journaliser la création d'affaire (si la fonction existe)
            if (typeof DB.addActivityLog === 'function') {
                try {
                    await DB.addActivityLog({
                        username: username,
                        action: 'create',
                        entity_type: 'affaire',
                        entity_id: newId,
                        details: `Création de l'affaire ${nouvelleAffaire.numero} - ${nouvelleAffaire.objet}`
                    });
                } catch (logError) {
                    console.warn('Impossible de journaliser la création:', logError);
                    // Continuer l'exécution même si la journalisation échoue
                }
            }
        }

        formAffaire.style.display = 'none';
        affaireForm.reset();
        await chargerDonnees();

        // Si des filtres sont actifs, réappliquer les filtres
        const termeRecherche = document.getElementById('recherche-affaire').value;
        const statutFiltre = document.getElementById('filtre-statut').value;
        const natureFiltre = document.getElementById('filtre-nature').value;

        if (termeRecherche !== '' || statutFiltre !== '' || natureFiltre !== '') {
            filtrerAffaires();
        }

        afficherSucces('Affaire enregistrée avec succès');
    } catch (error) {
        console.error('Erreur lors de l\'enregistrement de l\'affaire:', error);
        afficherErreur('Erreur lors de l\'enregistrement de l\'affaire: ' + error.message);
    }
}

// Fonctions pour les avocats
function afficherAvocats() {
    avocatsListe.innerHTML = '';

    // Vérifier si l'utilisateur est admin pour autoriser la suppression
    const userData = JSON.parse(localStorage.getItem('currentUser'));
    const isAdmin = userData && userData.role === 'admin';

    avocats.forEach(avocat => {
        const tr = document.createElement('tr');

        // Bouton de suppression différent selon le rôle de l'utilisateur
        let deleteButton = '';
        if (isAdmin) {
            // Pour l'administrateur, le bouton est actif
            deleteButton = `<button class="btn btn-sm btn-danger delete-avocat" data-id="${avocat.id}">Supprimer</button>`;
        } else {
            // Pour les autres utilisateurs, le bouton est désactivé
            deleteButton = `<button class="btn btn-sm btn-secondary" disabled title="Seul l'administrateur peut supprimer">Supprimer</button>`;
        }

        // Vérifier si la date de validité est expirée
        const dateExpiree = estDateExpiree(avocat.validiteContrat);

        // Appliquer une classe à la ligne entière si la date est expirée
        if (dateExpiree) {
            tr.classList.add('table-danger');
        }

        // Appliquer une classe spécifique à la cellule de date
        const classeDate = dateExpiree ? 'text-danger fw-bold' : '';

        tr.innerHTML = `
            <td>${avocat.nom}</td>
            <td>${avocat.agrement}</td>
            <td>${avocat.telephone}</td>
            <td>${avocat.email}</td>
            <td class="${classeDate}">${formaterDate(avocat.validiteContrat)}</td>
            <td class="action-buttons">
                <button class="btn btn-sm btn-warning edit-avocat" data-id="${avocat.id}">Modifier</button>
                ${deleteButton}
            </td>
        `;

        avocatsListe.appendChild(tr);
    });

    // Ajout des écouteurs d'événements pour les boutons d'action
    document.querySelectorAll('.edit-avocat').forEach(btn => {
        btn.addEventListener('click', () => {
            const id = parseInt(btn.getAttribute('data-id'));
            editerAvocat(id);
        });
    });

    document.querySelectorAll('.delete-avocat').forEach(btn => {
        btn.addEventListener('click', () => {
            const id = parseInt(btn.getAttribute('data-id'));
            supprimerAvocat(id);
        });
    });
}

async function editerAvocat(id) {
    try {
        const avocat = await DB.getAvocatById(id);
        if (!avocat) {
            alert('Avocat non trouvé.');
            return;
        }

        avocatEnEdition = avocat;
        modeEditionAvocat = true;
        formAvocatTitre.textContent = 'Modifier l\'Avocat';

        // Remplir le formulaire avec les données de l'avocat
        document.getElementById('nom-avocat').value = avocat.nom;
        document.getElementById('agrement-avocat').value = avocat.agrement;
        document.getElementById('adresse-avocat').value = avocat.adresse || '';
        document.getElementById('telephone-avocat').value = avocat.telephone;
        document.getElementById('email-avocat').value = avocat.email;
        document.getElementById('date-signature').value = avocat.dateSignature || '';

        // Si la date de validité existe, l'utiliser, sinon la calculer
        if (avocat.validiteContrat) {
            document.getElementById('validite-contrat').value = avocat.validiteContrat;
        } else if (avocat.dateSignature) {
            // Calculer la date de validité (1 an après la date de signature)
            const dateSign = new Date(avocat.dateSignature);
            const dateValidite = new Date(dateSign);
            dateValidite.setFullYear(dateValidite.getFullYear() + 1);

            // Formater la date au format YYYY-MM-DD pour l'input date
            const year = dateValidite.getFullYear();
            const month = String(dateValidite.getMonth() + 1).padStart(2, '0');
            const day = String(dateValidite.getDate()).padStart(2, '0');
            document.getElementById('validite-contrat').value = `${year}-${month}-${day}`;
        } else {
            document.getElementById('validite-contrat').value = '';
        }

        formAvocat.style.display = 'block';
    } catch (error) {
        console.error('Erreur lors de l\'édition de l\'avocat:', error);
        alert('Erreur lors de l\'édition de l\'avocat. Veuillez réessayer.');
    }
}

async function supprimerAvocat(id) {
    // Vérifier si l'utilisateur est administrateur
    const userData = JSON.parse(localStorage.getItem('currentUser'));
    if (!userData || userData.role !== 'admin') {
        afficherErreur('Seul l\'administrateur peut supprimer des avocats.');
        return;
    }

    if (confirm('Êtes-vous sûr de vouloir supprimer cet avocat ?')) {
        try {
            // Récupérer les informations de l'avocat avant de le supprimer pour le journal
            const avocat = await DB.getAvocatById(id);
            if (!avocat) {
                throw new Error('Avocat non trouvé');
            }

            await DB.deleteAvocat(id);

            // Journaliser la suppression (si la fonction existe)
            if (typeof DB.addActivityLog === 'function') {
                try {
                    await DB.addActivityLog({
                        username: userData.username,
                        action: 'delete',
                        entity_type: 'avocat',
                        entity_id: id,
                        details: `Suppression de l'avocat ${avocat.nom} (${avocat.agrement})`
                    });
                } catch (logError) {
                    console.warn('Impossible de journaliser la suppression:', logError);
                    // Continuer l'exécution même si la journalisation échoue
                }
            }

            await chargerDonnees(); // Recharger les données depuis la base de données
            afficherSucces('Avocat supprimé avec succès');
        } catch (error) {
            console.error('Erreur lors de la suppression de l\'avocat:', error);
            if (error.message.includes('associé')) {
                afficherErreur(error.message);

                // Journaliser l'échec de suppression (si la fonction existe)
                if (typeof DB.addActivityLog === 'function') {
                    try {
                        await DB.addActivityLog({
                            username: userData.username,
                            action: 'delete_failed',
                            entity_type: 'avocat',
                            entity_id: id,
                            details: `Échec de suppression d'un avocat: ${error.message}`
                        });
                    } catch (logError) {
                        console.warn('Impossible de journaliser l\'\u00e9chec de suppression:', logError);
                        // Continuer l'exécution même si la journalisation échoue
                    }
                }
            } else {
                afficherErreur('Erreur lors de la suppression de l\'avocat. Veuillez réessayer.');
            }
        }
    }
}

async function enregistrerAvocat() {
    try {
        const dateSignature = document.getElementById('date-signature').value;
        let validiteContrat = document.getElementById('validite-contrat').value;

        // Si la date de validité n'est pas définie mais que la date de signature l'est,
        // calculer automatiquement la date de validité (1 an après la signature)
        if (!validiteContrat && dateSignature) {
            const dateSign = new Date(dateSignature);
            const dateValidite = new Date(dateSign);
            dateValidite.setFullYear(dateValidite.getFullYear() + 1);

            // Formater la date au format YYYY-MM-DD
            const year = dateValidite.getFullYear();
            const month = String(dateValidite.getMonth() + 1).padStart(2, '0');
            const day = String(dateValidite.getDate()).padStart(2, '0');
            validiteContrat = `${year}-${month}-${day}`;

            // Mettre à jour le champ dans le formulaire
            document.getElementById('validite-contrat').value = validiteContrat;
        }

        const nouvelAvocat = {
            nom: document.getElementById('nom-avocat').value,
            agrement: document.getElementById('agrement-avocat').value,
            adresse: document.getElementById('adresse-avocat').value,
            telephone: document.getElementById('telephone-avocat').value,
            email: document.getElementById('email-avocat').value,
            dateSignature: dateSignature,
            validiteContrat: validiteContrat
        };

        // Récupérer les informations de l'utilisateur connecté
        const userData = JSON.parse(localStorage.getItem('currentUser'));
        const username = userData ? userData.username : 'Utilisateur inconnu';

        if (modeEditionAvocat && avocatEnEdition) {
            nouvelAvocat.id = avocatEnEdition.id;
            await DB.updateAvocat(nouvelAvocat);

            // Journaliser la modification d'avocat (si la fonction existe)
            if (typeof DB.addActivityLog === 'function') {
                try {
                    await DB.addActivityLog({
                        username: username,
                        action: 'update',
                        entity_type: 'avocat',
                        entity_id: nouvelAvocat.id,
                        details: `Modification de l'avocat ${nouvelAvocat.nom} (${nouvelAvocat.agrement})`
                    });
                } catch (logError) {
                    console.warn('Impossible de journaliser la modification:', logError);
                    // Continuer l'exécution même si la journalisation échoue
                }
            }
        } else {
            const newId = await DB.addAvocat(nouvelAvocat);

            // Journaliser la création d'avocat (si la fonction existe)
            if (typeof DB.addActivityLog === 'function') {
                try {
                    await DB.addActivityLog({
                        username: username,
                        action: 'create',
                        entity_type: 'avocat',
                        entity_id: newId,
                        details: `Création de l'avocat ${nouvelAvocat.nom} (${nouvelAvocat.agrement})`
                    });
                } catch (logError) {
                    console.warn('Impossible de journaliser la création:', logError);
                    // Continuer l'exécution même si la journalisation échoue
                }
            }
        }

        formAvocat.style.display = 'none';
        avocatForm.reset();
        await chargerDonnees();
        afficherSucces('Avocat enregistré avec succès');
    } catch (error) {
        console.error('Erreur lors de l\'enregistrement de l\'avocat:', error);
        afficherErreur('Erreur lors de l\'enregistrement de l\'avocat: ' + error.message);
    }
}

// Fonctions utilitaires
function formaterNature(nature) {
    const mapping = {
        'social': 'Social',
        'administrative': 'Administrative',
        'commerciale': 'Commerciale',
        'foncier': 'Foncier',
        'refere': 'Référé',
        'civile': 'Civile',
        'penal': 'Pénal'
    };
    return mapping[nature] || nature;
}

function formaterJuridiction(juridiction) {
    const mapping = {
        'tribunal': 'Tribunal',
        'cour': 'Cour',
        'cours-supreme': 'Cours Suprême',
        'c-etat': 'C État'
    };
    return mapping[juridiction] || juridiction;
}

function formaterStatut(statut) {
    const mapping = {
        'en-cours': 'En cours',
        'jugee-faveur': 'Jugée en faveur',
        'jugee-tiers': 'Jugée pour tiers'
    };
    return mapping[statut] || statut;
}

function formaterDate(dateStr) {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    return date.toLocaleDateString('fr-FR');
}

// Fonction pour vérifier si une date est expirée
function estDateExpiree(dateStr) {
    if (!dateStr) return false;
    const date = new Date(dateStr);
    const aujourdhui = new Date();

    // Réinitialiser les heures, minutes, secondes et millisecondes pour comparer uniquement les dates
    aujourdhui.setHours(0, 0, 0, 0);
    date.setHours(0, 0, 0, 0);

    return date < aujourdhui;
}

function trouverNomAvocat(id) {
    const avocat = avocats.find(a => a.id === id);
    return avocat ? avocat.nom : 'Non assigné';
}

// Ajouter des fonctions utilitaires pour la gestion des messages
function afficherErreur(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.container').insertBefore(alertDiv, document.querySelector('main'));
    setTimeout(() => alertDiv.remove(), 5000);
}

function afficherSucces(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.container').insertBefore(alertDiv, document.querySelector('main'));
    setTimeout(() => alertDiv.remove(), 3000);
}

async function mettreAJourTableauBord() {
    try {
        const stats = await Dashboard.getStatistiques();

        // Vérifier que les statistiques sont valides
        if (!stats) {
            throw new Error('Statistiques non disponibles');
        }

        // Mettre à jour les compteurs avec vérification des éléments DOM
        const totalAffairesElement = document.getElementById('total-affaires');
        if (totalAffairesElement) totalAffairesElement.textContent = stats.totalAffaires;

        const affairesEnCoursElement = document.getElementById('affaires-en-cours');
        if (affairesEnCoursElement) affairesEnCoursElement.textContent = stats.affairesEnCours;

        const affairesGagneesElement = document.getElementById('affaires-gagnees');
        if (affairesGagneesElement) affairesGagneesElement.textContent = stats.affairesGagnees;

        const totalAvocatsElement = document.getElementById('total-avocats');
        if (totalAvocatsElement) totalAvocatsElement.textContent = stats.totalAvocats;

        const contratsExpirantElement = document.getElementById('contrats-expirant');
        if (contratsExpirantElement) contratsExpirantElement.textContent = stats.avocatsContratExpirant.length;

        // Calculer et afficher le taux de succès
        const tauxSucces = stats.affairesJugees > 0
            ? Math.round((stats.affairesGagnees / stats.affairesJugees) * 100)
            : 0;

        const tauxSuccesElement = document.getElementById('taux-succes');
        if (tauxSuccesElement) tauxSuccesElement.textContent = `${tauxSucces}%`;

        // Formater et afficher le montant total
        const montantTotalElement = document.getElementById('montant-total');
        if (montantTotalElement) {
            montantTotalElement.textContent = new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(stats.montantTotal);
        }

        try {
            // Mettre à jour les graphiques
            mettreAJourGraphiques(stats);
        } catch (graphError) {
            console.error('Erreur lors de la mise à jour des graphiques:', graphError);
        }

        try {
            // Mettre à jour les alertes
            mettreAJourAlertes(stats);
        } catch (alertesError) {
            console.error('Erreur lors de la mise à jour des alertes:', alertesError);
        }

        try {
            // Mettre à jour les activités récentes
            mettreAJourActivitesRecentes(stats);
        } catch (activitesError) {
            console.error('Erreur lors de la mise à jour des activités récentes:', activitesError);
        }

        try {
            // Mettre à jour les performances des avocats
            mettreAJourPerformanceAvocats(stats);
        } catch (performanceError) {
            console.error('Erreur lors de la mise à jour des performances des avocats:', performanceError);
        }

        try {
            // Mettre à jour le résumé financier
            mettreAJourResumeFinancier(stats);
        } catch (resumeError) {
            console.error('Erreur lors de la mise à jour du résumé financier:', resumeError);
        }

        // Configurer le bouton d'export
        const btnExport = document.getElementById('btn-export-dashboard');
        if (btnExport) {
            // Supprimer les écouteurs d'événements existants pour éviter les doublons
            const newBtn = btnExport.cloneNode(true);
            btnExport.parentNode.replaceChild(newBtn, btnExport);

            newBtn.addEventListener('click', () => {
                try {
                    Dashboard.exporterDonnees(stats);
                } catch (exportError) {
                    console.error('Erreur lors de l\'export des données:', exportError);
                    afficherErreur('Erreur lors de l\'export des données');
                }
            });
        }

    } catch (error) {
        console.error('Erreur lors de la mise à jour du tableau de bord:', error);
        afficherErreur('Erreur lors de la mise à jour du tableau de bord: ' + error.message);
    }
}

function mettreAJourGraphiques(stats) {
    // Fonction utilitaire pour créer un graphique avec vérification de l'élément DOM
    function creerGraphique(elementId, config) {
        const canvas = document.getElementById(elementId);
        if (!canvas) {
            console.error(`Élément canvas #${elementId} non trouvé`);
            return null;
        }

        try {
            // Détruire le graphique existant s'il y en a un
            if (window.Chart && Chart.getChart) {
                const existingChart = Chart.getChart(canvas);
                if (existingChart) {
                    existingChart.destroy();
                }
            }

            return new Chart(canvas, config);
        } catch (error) {
            console.error(`Erreur lors de la création du graphique ${elementId}:`, error);
            return null;
        }
    }

    // Graphique de répartition par nature
    creerGraphique('nature-chart', {
        type: 'pie',
        data: {
            labels: Object.keys(stats.repartitionNature),
            datasets: [{
                data: Object.values(stats.repartitionNature),
                backgroundColor: [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40'
                ]
            }]
        }
    });

    // Graphique de répartition par juridiction
    creerGraphique('juridiction-chart', {
        type: 'bar',
        data: {
            labels: Object.keys(stats.repartitionJuridiction),
            datasets: [{
                label: 'Nombre d\'affaires',
                data: Object.values(stats.repartitionJuridiction),
                backgroundColor: '#36A2EB'
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Graphique d'évolution des affaires dans le temps
    creerGraphique('evolution-chart', {
        type: 'line',
        data: {
            labels: stats.evolutionAffaires.labels,
            datasets: [{
                label: 'Nombre d\'affaires',
                data: stats.evolutionAffaires.data,
                borderColor: '#4BC0C0',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1,
                fill: true
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Graphique des montants par nature d'affaire
    creerGraphique('montant-nature-chart', {
        type: 'bar',
        data: {
            labels: Object.keys(stats.montantParNature),
            datasets: [{
                label: 'Montant (€)',
                data: Object.values(stats.montantParNature),
                backgroundColor: '#FF9F40'
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function mettreAJourAlertes(stats) {
    const alertesList = document.getElementById('alertes-list');
    if (!alertesList) {
        console.error('Élément #alertes-list non trouvé');
        return;
    }

    alertesList.innerHTML = '';

    // Alerte pour les contrats d'avocats expirant
    if (stats.avocatsContratExpirant && Array.isArray(stats.avocatsContratExpirant)) {
        stats.avocatsContratExpirant.forEach(avocat => {
            if (avocat && avocat.nom && avocat.validiteContrat) {
                const alerte = document.createElement('div');
                alerte.className = 'list-group-item list-group-item-warning';
                alerte.innerHTML = `
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Le contrat de ${avocat.nom} expire le ${new Date(avocat.validiteContrat).toLocaleDateString()}
                `;
                alertesList.appendChild(alerte);
            }
        });
    }

    // Alerte pour les affaires sans montant défini
    if (stats.activitesRecentes && Array.isArray(stats.activitesRecentes)) {
        const affairesSansMontant = stats.activitesRecentes.filter(a => a && (!a.montant || a.montant === 0));
        if (affairesSansMontant.length > 0) {
            const alerte = document.createElement('div');
            alerte.className = 'list-group-item list-group-item-info';
            alerte.innerHTML = `
                <i class="fas fa-info-circle me-2"></i>
                ${affairesSansMontant.length} affaire(s) récente(s) sans montant défini
            `;
            alertesList.appendChild(alerte);
        }
    }

    // Alerte pour les affaires en cours depuis longtemps
    // (à implémenter si la date d'engagement est disponible)

    // Si aucune alerte, afficher un message
    if (alertesList.children.length === 0) {
        const alerte = document.createElement('div');
        alerte.className = 'list-group-item list-group-item-success';
        alerte.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>
            Aucune alerte à signaler
        `;
        alertesList.appendChild(alerte);
    }
}

// Fonction pour mettre à jour les activités récentes
function mettreAJourActivitesRecentes(stats) {
    const activitesList = document.getElementById('activites-recentes-list');
    if (!activitesList) {
        console.error('Élément #activites-recentes-list non trouvé');
        return;
    }

    activitesList.innerHTML = '';

    if (!stats.activitesRecentes || !Array.isArray(stats.activitesRecentes)) {
        const item = document.createElement('div');
        item.className = 'list-group-item text-center';
        item.textContent = 'Aucune donnée disponible';
        activitesList.appendChild(item);
        return;
    }

    stats.activitesRecentes.forEach(activite => {
        if (!activite) return;

        const item = document.createElement('div');
        item.className = 'list-group-item';

        // Déterminer la classe de statut pour l'affichage
        let statutClass = 'text-primary';
        let statutIcon = 'fa-clock';

        if (activite.statut === 'jugee-faveur') {
            statutClass = 'text-success';
            statutIcon = 'fa-check-circle';
        } else if (activite.statut === 'jugee-defaveur') {
            statutClass = 'text-danger';
            statutIcon = 'fa-times-circle';
        }

        try {
            const dateFormatee = activite.date ? new Date(activite.date).toLocaleDateString() : 'Date inconnue';
            const statutFormatte = activite.statut ? formaterStatut(activite.statut) : 'Statut inconnu';

            item.innerHTML = `
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">${activite.numero || 'Sans numéro'} - ${activite.objet || 'Sans objet'}</h6>
                    <small>${dateFormatee}</small>
                </div>
                <p class="mb-1">Avocat: ${activite.avocat || 'Non assigné'}</p>
                <small class="${statutClass}"><i class="fas ${statutIcon} me-1"></i>${statutFormatte}</small>
            `;

            activitesList.appendChild(item);
        } catch (error) {
            console.error('Erreur lors de l\'affichage d\'une activité:', error);
        }
    });

    // Si aucune activité, afficher un message
    if (activitesList.children.length === 0) {
        const item = document.createElement('div');
        item.className = 'list-group-item text-center';
        item.textContent = 'Aucune activité récente';
        activitesList.appendChild(item);
    }
}

// Fonction pour mettre à jour les performances des avocats
function mettreAJourPerformanceAvocats(stats) {
    const performanceList = document.getElementById('performance-avocats-list');
    if (!performanceList) {
        console.error('Élément #performance-avocats-list non trouvé');
        return;
    }

    performanceList.innerHTML = '';

    if (!stats.performanceAvocats || !Array.isArray(stats.performanceAvocats)) {
        const item = document.createElement('div');
        item.className = 'list-group-item text-center';
        item.textContent = 'Aucune donnée disponible';
        performanceList.appendChild(item);
        return;
    }

    try {
        // Trier les avocats par taux de réussite décroissant
        const avocatsTries = [...stats.performanceAvocats]
            .sort((a, b) => (b.tauxReussite || 0) - (a.tauxReussite || 0));

        avocatsTries.forEach(avocat => {
            if (!avocat) return;

            if (avocat.totalAffaires > 0) { // N'afficher que les avocats ayant des affaires
                const item = document.createElement('div');
                item.className = 'list-group-item';

                // Déterminer la classe de couleur pour le taux de réussite
                let tauxClass = 'text-warning';
                const tauxReussite = avocat.tauxReussite || 0;

                if (tauxReussite >= 70) {
                    tauxClass = 'text-success';
                } else if (tauxReussite < 30) {
                    tauxClass = 'text-danger';
                }

                try {
                    const montantFormatte = new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(avocat.montantTotal || 0);

                    item.innerHTML = `
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">${avocat.nom || 'Avocat inconnu'}</h6>
                            <span class="${tauxClass} fw-bold">${tauxReussite}%</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <small>Affaires: ${avocat.totalAffaires} (${avocat.affairesGagnees || 0} gagnées)</small>
                            <small>Montant: ${montantFormatte}</small>
                        </div>
                    `;

                    performanceList.appendChild(item);
                } catch (error) {
                    console.error('Erreur lors de l\'affichage d\'un avocat:', error);
                }
            }
        });
    } catch (error) {
        console.error('Erreur lors du tri des avocats:', error);
    }

    // Si aucun avocat avec des affaires, afficher un message
    if (performanceList.children.length === 0) {
        const item = document.createElement('div');
        item.className = 'list-group-item text-center';
        item.textContent = 'Aucune donnée de performance disponible';
        performanceList.appendChild(item);
    }
}

// Fonction pour mettre à jour le résumé financier
function mettreAJourResumeFinancier(stats) {
    if (!stats.resumeFinancier) {
        console.error('Données de résumé financier non disponibles');
        return;
    }

    // Fonction utilitaire pour mettre à jour un élément avec vérification
    function mettreAJourMontant(elementId, valeur) {
        const element = document.getElementById(elementId);
        if (element) {
            try {
                element.textContent = new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(valeur || 0);
            } catch (error) {
                console.error(`Erreur lors de la mise à jour de ${elementId}:`, error);
                element.textContent = '0,00 €';
            }
        } else {
            console.error(`Élément #${elementId} non trouvé`);
        }
    }

    // Mettre à jour les montants
    mettreAJourMontant('montant-en-cours', stats.resumeFinancier.montantEnCours);
    mettreAJourMontant('montant-gagne', stats.resumeFinancier.montantGagne);
    mettreAJourMontant('montant-perdu', stats.resumeFinancier.montantPerdu);
    mettreAJourMontant('montant-moyen', stats.resumeFinancier.montantMoyen);
}
const API_URL = 'http://localhost:3000/api';

const API = {
    // Fonctions pour les avocats
    async getAllAvocats() {
        try {
            const response = await fetch(`${API_URL}/avocats`);
            if (!response.ok) throw new Error('Erreur réseau');
            return await response.json();
        } catch (error) {
            console.error('Erreur API getAllAvocats:', error);
            throw error;
        }
    },

    async addAvocat(avocat) {
        try {
            const response = await fetch(`${API_URL}/avocats`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify(avocat)
            });
            if (!response.ok) throw new Error('Erreur réseau');
            return await response.json();
        } catch (error) {
            console.error('Erreur API addAvocat:', error);
            throw error;
        }
    },

    async updateAvocat(avocat) {
        try {
            const response = await fetch(`${API_URL}/avocats/${avocat.id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify(avocat)
            });
            if (!response.ok) throw new Error('Erreur réseau');
            return await response.json();
        } catch (error) {
            console.error('Erreur API updateAvocat:', error);
            throw error;
        }
    },

    async deleteAvocat(id) {
        try {
            const response = await fetch(`${API_URL}/avocats/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                }
            });
            if (!response.ok) throw new Error('Erreur réseau');
            return await response.json();
        } catch (error) {
            console.error('Erreur API deleteAvocat:', error);
            throw error;
        }
    },

    // Fonctions pour les affaires
    async getAllAffaires() {
        try {
            const response = await fetch(`${API_URL}/affaires`);
            if (!response.ok) throw new Error('Erreur réseau');
            return await response.json();
        } catch (error) {
            console.error('Erreur API getAllAffaires:', error);
            throw error;
        }
    },

    async addAffaire(affaire) {
        try {
            const response = await fetch(`${API_URL}/affaires`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify(affaire)
            });
            if (!response.ok) throw new Error('Erreur réseau');
            return await response.json();
        } catch (error) {
            console.error('Erreur API addAffaire:', error);
            throw error;
        }
    },

    async updateAffaire(affaire) {
        try {
            const response = await fetch(`${API_URL}/affaires/${affaire.id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify(affaire)
            });
            if (!response.ok) throw new Error('Erreur réseau');
            return await response.json();
        } catch (error) {
            console.error('Erreur API updateAffaire:', error);
            throw error;
        }
    },

    async deleteAffaire(id) {
        try {
            const response = await fetch(`${API_URL}/affaires/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                }
            });
            if (!response.ok) throw new Error('Erreur réseau');
            return await response.json();
        } catch (error) {
            console.error('Erreur API deleteAffaire:', error);
            throw error;
        }
    }
};
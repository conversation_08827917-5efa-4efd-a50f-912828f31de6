/**
 * JavaScript pour l'intégration WordPress - Système Juridique
 */

(function($) {
    'use strict';

    // Objet principal pour l'intégration
    const JuridiqueIntegration = {
        
        init: function() {
            this.bindEvents();
            this.loadInitialStats();
            this.setupAutoSync();
        },
        
        bindEvents: function() {
            // Bouton de synchronisation manuelle
            $(document).on('click', '.sync-affaires-btn', this.syncAffaires.bind(this));
            $(document).on('click', '.sync-avocats-btn', this.syncAvocats.bind(this));
            $(document).on('click', '.sync-all-btn', this.syncAll.bind(this));
            
            // Rafraîchissement des statistiques
            $(document).on('click', '.refresh-stats-btn', this.refreshStats.bind(this));
            
            // Communication avec l'iframe de l'application
            window.addEventListener('message', this.handleIframeMessage.bind(this), false);
        },
        
        loadInitialStats: function() {
            this.refreshStats();
        },
        
        setupAutoSync: function() {
            // Synchronisation automatique toutes les 5 minutes
            setInterval(() => {
                this.syncAll(true); // true = mode silencieux
            }, 5 * 60 * 1000);
        },
        
        syncAffaires: function(event, silent = false) {
            if (event && event.preventDefault) {
                event.preventDefault();
            }
            
            const button = $(event ? event.target : '.sync-affaires-btn');
            const statusDiv = button.siblings('.sync-status');
            
            if (!silent) {
                this.showSyncStatus(statusDiv, 'loading', 'Synchronisation des affaires en cours...');
                button.prop('disabled', true);
            }
            
            // Récupérer les données depuis l'application principale
            this.fetchDataFromApp('affaires')
                .then(data => {
                    return this.sendSyncRequest('sync_affaires', data);
                })
                .then(response => {
                    if (!silent) {
                        this.showSyncStatus(statusDiv, 'success', 'Affaires synchronisées avec succès');
                        button.prop('disabled', false);
                    }
                    this.refreshStats();
                })
                .catch(error => {
                    console.error('Erreur lors de la synchronisation des affaires:', error);
                    if (!silent) {
                        this.showSyncStatus(statusDiv, 'error', 'Erreur lors de la synchronisation des affaires');
                        button.prop('disabled', false);
                    }
                });
        },
        
        syncAvocats: function(event, silent = false) {
            if (event && event.preventDefault) {
                event.preventDefault();
            }
            
            const button = $(event ? event.target : '.sync-avocats-btn');
            const statusDiv = button.siblings('.sync-status');
            
            if (!silent) {
                this.showSyncStatus(statusDiv, 'loading', 'Synchronisation des avocats en cours...');
                button.prop('disabled', true);
            }
            
            // Récupérer les données depuis l'application principale
            this.fetchDataFromApp('avocats')
                .then(data => {
                    return this.sendSyncRequest('sync_avocats', data);
                })
                .then(response => {
                    if (!silent) {
                        this.showSyncStatus(statusDiv, 'success', 'Avocats synchronisés avec succès');
                        button.prop('disabled', false);
                    }
                    this.refreshStats();
                })
                .catch(error => {
                    console.error('Erreur lors de la synchronisation des avocats:', error);
                    if (!silent) {
                        this.showSyncStatus(statusDiv, 'error', 'Erreur lors de la synchronisation des avocats');
                        button.prop('disabled', false);
                    }
                });
        },
        
        syncAll: function(silent = false) {
            Promise.all([
                this.syncAffaires(null, silent),
                this.syncAvocats(null, silent)
            ]).then(() => {
                if (!silent) {
                    this.showGlobalMessage('Synchronisation complète terminée', 'success');
                }
            }).catch(error => {
                if (!silent) {
                    this.showGlobalMessage('Erreur lors de la synchronisation complète', 'error');
                }
            });
        },
        
        fetchDataFromApp: function(type) {
            return new Promise((resolve, reject) => {
                if (!juridique_ajax.app_url) {
                    reject(new Error('URL de l\'application non configurée'));
                    return;
                }
                
                // Essayer de récupérer les données via l'iframe
                const iframe = document.querySelector('.juridique-dashboard-iframe');
                if (iframe && iframe.contentWindow) {
                    iframe.contentWindow.postMessage({
                        action: 'getData',
                        type: type
                    }, juridique_ajax.app_url);
                    
                    // Attendre la réponse
                    const messageHandler = (event) => {
                        if (event.origin !== new URL(juridique_ajax.app_url).origin) {
                            return;
                        }
                        
                        if (event.data.action === 'dataResponse' && event.data.type === type) {
                            window.removeEventListener('message', messageHandler);
                            resolve(event.data.data);
                        }
                    };
                    
                    window.addEventListener('message', messageHandler);
                    
                    // Timeout après 10 secondes
                    setTimeout(() => {
                        window.removeEventListener('message', messageHandler);
                        reject(new Error('Timeout lors de la récupération des données'));
                    }, 10000);
                } else {
                    // Fallback: essayer de récupérer via AJAX direct
                    $.ajax({
                        url: juridique_ajax.app_url + '/api/get_data.php',
                        method: 'GET',
                        data: { type: type },
                        dataType: 'json'
                    }).done(function(data) {
                        resolve(data);
                    }).fail(function() {
                        reject(new Error('Impossible de récupérer les données'));
                    });
                }
            });
        },
        
        sendSyncRequest: function(action, data) {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: juridique_ajax.ajax_url,
                    method: 'POST',
                    data: {
                        action: 'juridique_sync',
                        sync_action: action,
                        data: JSON.stringify(data),
                        nonce: juridique_ajax.nonce
                    },
                    dataType: 'json'
                }).done(function(response) {
                    if (response.success) {
                        resolve(response.data);
                    } else {
                        reject(new Error(response.data || 'Erreur inconnue'));
                    }
                }).fail(function(xhr, status, error) {
                    reject(new Error('Erreur de communication: ' + error));
                });
            });
        },
        
        refreshStats: function() {
            $.ajax({
                url: juridique_ajax.ajax_url,
                method: 'POST',
                data: {
                    action: 'juridique_sync',
                    sync_action: 'get_stats',
                    nonce: juridique_ajax.nonce
                },
                dataType: 'json'
            }).done(function(response) {
                if (response.success) {
                    JuridiqueIntegration.updateStatsDisplay(response.data);
                }
            }).fail(function() {
                console.error('Erreur lors du rafraîchissement des statistiques');
            });
        },
        
        updateStatsDisplay: function(stats) {
            $('.stat-item .stat-number').each(function() {
                const $this = $(this);
                const label = $this.siblings('.stat-label').text().toLowerCase();
                
                if (label.includes('affaires')) {
                    $this.text(stats.total_affaires || 0);
                } else if (label.includes('avocats')) {
                    $this.text(stats.total_avocats || 0);
                }
            });
            
            $('.stat-item .stat-date').each(function() {
                const $this = $(this);
                const label = $this.siblings('.stat-label').text().toLowerCase();
                
                if (label.includes('sync')) {
                    if (stats.last_sync) {
                        const date = new Date(stats.last_sync);
                        $this.text(date.toLocaleDateString('fr-FR') + ' ' + date.toLocaleTimeString('fr-FR'));
                    } else {
                        $this.text('Jamais');
                    }
                }
            });
        },
        
        showSyncStatus: function(statusDiv, type, message) {
            statusDiv.removeClass('success error loading').addClass(type);
            statusDiv.html(type === 'loading' ? '<span class="juridique-loading"></span>' + message : message);
            statusDiv.show();
            
            if (type !== 'loading') {
                setTimeout(() => {
                    statusDiv.fadeOut();
                }, 5000);
            }
        },
        
        showGlobalMessage: function(message, type) {
            const messageDiv = $('<div class="juridique-notice notice-' + type + '">' + message + '</div>');
            $('.juridique-admin-page').prepend(messageDiv);
            
            setTimeout(() => {
                messageDiv.fadeOut(() => {
                    messageDiv.remove();
                });
            }, 5000);
        },
        
        handleIframeMessage: function(event) {
            // Gérer les messages provenant de l'iframe de l'application
            if (event.origin !== new URL(juridique_ajax.app_url).origin) {
                return;
            }
            
            const data = event.data;
            
            switch (data.action) {
                case 'dataUpdated':
                    // Les données ont été mises à jour dans l'application
                    this.syncAll(true); // Synchronisation silencieuse
                    break;
                    
                case 'requestSync':
                    // L'application demande une synchronisation
                    this.syncAll();
                    break;
            }
        }
    };
    
    // Initialiser quand le DOM est prêt
    $(document).ready(function() {
        JuridiqueIntegration.init();
    });
    
    // Exposer l'objet globalement pour les tests et le debug
    window.JuridiqueIntegration = JuridiqueIntegration;
    
})(jQuery);

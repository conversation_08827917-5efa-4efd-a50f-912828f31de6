/**
 * Gestionnaire de session pour maintenir la session active
 * Ce script vérifie périodiquement l'état de la session et la rafraîchit si nécessaire
 */

// Intervalle de vérification de la session en millisecondes (15 minutes)
const SESSION_CHECK_INTERVAL = 15 * 60 * 1000;

// Fonction pour vérifier et rafraîchir la session
function checkAndRefreshSession() {
    // Vérifier si l'utilisateur est connecté localement
    if (!localStorage.getItem('currentUser')) {
        return Promise.resolve(); // Retourner une promesse résolue si l'utilisateur n'est pas connecté
    }

    // Variable pour suivre si la requête a été envoyée
    let requestSent = false;

    // Créer une promesse avec timeout pour éviter les blocages
    return new Promise((resolve) => {
        // Définir un timeout pour résoudre la promesse après 5 secondes
        const timeoutId = setTimeout(() => {
            if (!requestSent) {
                resolve();
            }
        }, 5000);

        // Appeler l'API pour vérifier l'état de la session
        requestSent = true;

        // Créer un signal d'abandon avec timeout si disponible
        let fetchOptions = {
            method: 'GET',
            credentials: 'include'
        };

        // Vérifier si AbortSignal.timeout est disponible (pas disponible dans tous les navigateurs)
        if (typeof AbortSignal !== 'undefined' && AbortSignal.timeout) {
            try {
                fetchOptions.signal = AbortSignal.timeout(4000);
            } catch (e) {
                // AbortSignal.timeout non supporté, utilisation du timeout standard
            }
        }

        fetch('api/auth_api.php?action=check', fetchOptions)
        .then(response => {
            clearTimeout(timeoutId);
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // Session active, rafraîchissement réussi
            } else {
                // Session expirée, redirection vers la page de connexion
                localStorage.removeItem('currentUser');
                window.location.href = 'login.html';
            }
            resolve();
        })
        .catch(error => {
            clearTimeout(timeoutId);
            resolve(); // Résoudre la promesse même en cas d'erreur
        });
    });
}

// Démarrer la vérification périodique de la session
let sessionCheckInterval;

// Fonction pour démarrer le rafraîchissement de session
function startSessionRefresh() {
    // Arrêter l'intervalle existant s'il y en a un
    if (sessionCheckInterval) {
        clearInterval(sessionCheckInterval);
    }

    // Vérifier immédiatement la session
    checkAndRefreshSession()
        .catch(() => {
            // Ignorer les erreurs
        });

    // Configurer la vérification périodique avec une fonction wrapper
    // qui s'assure que les promesses sont correctement gérées
    sessionCheckInterval = setInterval(() => {
        checkAndRefreshSession()
            .catch(() => {
                // Ignorer les erreurs
            });
    }, SESSION_CHECK_INTERVAL);
}

// Fonction pour arrêter le rafraîchissement de session
function stopSessionRefresh() {
    if (sessionCheckInterval) {
        clearInterval(sessionCheckInterval);
        sessionCheckInterval = null;
        console.log('Rafraîchissement automatique de session arrêté');
    }
}

// Démarrer le rafraîchissement de session au chargement de la page
document.addEventListener('DOMContentLoaded', () => {
    // Ne pas démarrer le rafraîchissement sur la page de connexion
    if (!window.location.pathname.includes('login.html')) {
        startSessionRefresh();
    }
});

// Détecter l'activité de l'utilisateur pour rafraîchir la session
// Utiliser une fonction anonyme pour éviter de retourner la promesse au gestionnaire d'événements
document.addEventListener('click', () => {
    // Vérifier si l'utilisateur est connecté avant de rafraîchir la session
    if (localStorage.getItem('currentUser')) {
        checkAndRefreshSession();
    }
});

document.addEventListener('keydown', () => {
    // Vérifier si l'utilisateur est connecté avant de rafraîchir la session
    if (localStorage.getItem('currentUser')) {
        checkAndRefreshSession();
    }
});

<?php
// API pour la gestion des journaux d'activité

// Activer l'affichage des erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Autoriser les requêtes CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

// Si c'est une requête OPTIONS (preflight), renvoyer 200 OK
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Démarrer la session
session_start();

// Inclure les fichiers nécessaires
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../auth.php';

// Vérifier si l'utilisateur est connecté et est administrateur
if (!isLoggedIn()) {
    jsonResponse(['success' => false, 'message' => 'Non authentifié'], 401);
}

if (!isAdmin()) {
    jsonResponse(['success' => false, 'message' => 'Accès refusé. Seuls les administrateurs peuvent accéder aux journaux d\'activité'], 403);
}

// Récupérer la méthode HTTP et l'action
$method = $_SERVER['REQUEST_METHOD'];
$action = isset($_GET['action']) ? $_GET['action'] : '';

// Traiter la requête en fonction de la méthode et de l'action
switch ($method) {
    case 'GET':
        switch ($action) {
            case 'list':
                // Récupérer les paramètres de pagination
                $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
                $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 20;
                $offset = ($page - 1) * $limit;

                // Récupérer les paramètres de filtrage
                $username = isset($_GET['username']) ? $_GET['username'] : '';
                $actionFilter = isset($_GET['action']) ? $_GET['action'] : '';
                $entityType = isset($_GET['entity_type']) ? $_GET['entity_type'] : '';
                $date = isset($_GET['date']) ? $_GET['date'] : '';

                try {
                    $pdo = getDbConnection();

                    // Utiliser une requête SQL simplifiée pour le débogage
                    try {
                        // Récupérer tous les logs sans filtrage
                        $sql = "SELECT * FROM activity_logs ORDER BY timestamp DESC LIMIT 20";
                        error_log("Requête SQL simplifiée: " . $sql);

                        $stmt = $pdo->query($sql);
                        $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);

                        // Compter le nombre total de logs
                        $countSql = "SELECT COUNT(*) FROM activity_logs";
                        $total = $pdo->query($countSql)->fetchColumn();

                        error_log("Nombre de logs récupérés: " . count($logs));
                        error_log("Total des logs: " . $total);
                    } catch (Exception $e) {
                        error_log("Erreur lors de l'exécution de la requête simplifiée: " . $e->getMessage());
                        throw $e;
                    }

                    // Vérifier les données avant de les renvoyer
                    error_log("Nombre de logs trouvés: " . count($logs));
                    error_log("Total des logs: " . $total);

                    // S'assurer que les logs sont bien un tableau
                    if (!is_array($logs)) {
                        $logs = [];
                        error_log("ERREUR: Les logs ne sont pas un tableau!");
                    }

                    $response = [
                        'success' => true,
                        'logs' => $logs,
                        'total' => (int)$total,
                        'page' => (int)$page,
                        'limit' => (int)$limit,
                        'pages' => (int)ceil($total / $limit)
                    ];

                    // Vérifier la réponse JSON avant de l'envoyer
                    $jsonResponse = json_encode($response);
                    if ($jsonResponse === false) {
                        error_log("ERREUR JSON: " . json_last_error_msg());
                        // Essayer d'encoder sans les logs en cas d'erreur
                        $response['logs'] = 'Erreur d\'encodage JSON';
                        $jsonResponse = json_encode($response);
                    }

                    echo $jsonResponse;
                    exit;
                } catch (PDOException $e) {
                    jsonResponse(['success' => false, 'message' => 'Erreur lors de la récupération des journaux d\'activité: ' . $e->getMessage()], 500);
                }
                break;

            default:
                jsonResponse(['success' => false, 'message' => 'Action non reconnue'], 400);
        }
        break;

    case 'POST':
        switch ($action) {
            case 'clear':
                try {
                    $pdo = getDbConnection();

                    // Vider la table des journaux d'activité
                    $stmt = $pdo->prepare("TRUNCATE TABLE activity_logs");
                    $stmt->execute();

                    // Ajouter une entrée pour indiquer que le journal a été vidé
                    logActivity($_SESSION['username'], 'reset', 'database', null, 'Journal d\'activité vidé');

                    jsonResponse([
                        'success' => true,
                        'message' => 'Journal d\'activité vidé avec succès'
                    ]);
                } catch (PDOException $e) {
                    jsonResponse(['success' => false, 'message' => 'Erreur lors du vidage du journal d\'activité: ' . $e->getMessage()], 500);
                }
                break;

            default:
                jsonResponse(['success' => false, 'message' => 'Action non reconnue'], 400);
        }
        break;

    default:
        jsonResponse(['success' => false, 'message' => 'Méthode non autorisée'], 405);
}

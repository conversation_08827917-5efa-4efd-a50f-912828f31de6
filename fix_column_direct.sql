USE suivi_affaires_juridiques;

-- Check if password_hash column exists
SELECT 'Checking if password_hash column exists...' AS message;
SET @password_hash_exists = (
    SELECT COUNT(*) 
    FROM information_schema.COLUMNS 
    WHERE TABLE_SCHEMA = 'suivi_affaires_juridiques' 
    AND TABLE_NAME = 'users' 
    AND COLUMN_NAME = 'password_hash'
);

-- Check if password column exists
SELECT 'Checking if password column exists...' AS message;
SET @password_exists = (
    SELECT COUNT(*) 
    FROM information_schema.COLUMNS 
    WHERE TABLE_SCHEMA = 'suivi_affaires_juridiques' 
    AND TABLE_NAME = 'users' 
    AND COLUMN_NAME = 'password'
);

-- If password_hash exists but password doesn't, rename the column
SELECT IF(@password_hash_exists > 0 AND @password_exists = 0, 
          'Renaming password_hash to password...', 
          'No need to rename password_hash') AS message;

SET @rename_sql = IF(@password_hash_exists > 0 AND @password_exists = 0,
    'ALTER TABLE users CHANGE COLUMN password_hash password VARCHAR(255) NOT NULL',
    'SELECT 1'
);

PREPARE stmt FROM @rename_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- If neither column exists, add password column
SELECT IF(@password_hash_exists = 0 AND @password_exists = 0, 
          'Adding password column...', 
          'No need to add password column') AS message;

SET @add_sql = IF(@password_hash_exists = 0 AND @password_exists = 0,
    'ALTER TABLE users ADD COLUMN password VARCHAR(255) NOT NULL AFTER username',
    'SELECT 1'
);

PREPARE stmt FROM @add_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Show the current structure of the users table
SELECT 'Current structure of users table:' AS message;
DESCRIBE users;

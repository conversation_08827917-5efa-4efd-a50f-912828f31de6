// Base de données IndexedDB pour le système de suivi des affaires juridiques
const DB_NAME = 'SuiviAffairesJuridiquesDB';
const DB_VERSION = 2; // Augmenter la version pour la mise à jour de la structure
let db;

// Initialisation de la base de données
function initDatabase() {
    return new Promise((resolve, reject) => {
        // Vérifier si IndexedDB est disponible
        if (!window.indexedDB) {
            console.error('Votre navigateur ne supporte pas IndexedDB');
            reject(new Error('Votre navigateur ne supporte pas IndexedDB'));
            return;
        }

        const request = indexedDB.open(DB_NAME, DB_VERSION);

        // Création ou mise à jour de la structure de la base de données
        request.onupgradeneeded = (event) => {
            const db = event.target.result;
            const oldVersion = event.oldVersion;

            console.log(`Mise à jour de la base de données de la version ${oldVersion} à la version ${DB_VERSION}`);

            // Création du magasin d'objets pour les avocats
            if (!db.objectStoreNames.contains('avocats')) {
                const avocatsStore = db.createObjectStore('avocats', { keyPath: 'id', autoIncrement: true });
                avocatsStore.createIndex('nom', 'nom', { unique: false });
                avocatsStore.createIndex('email', 'email', { unique: true });
                avocatsStore.createIndex('agrement', 'agrement', { unique: true });
            }

            // Création du magasin d'objets pour les affaires
            if (!db.objectStoreNames.contains('affaires')) {
                const affairesStore = db.createObjectStore('affaires', { keyPath: 'id', autoIncrement: true });
                affairesStore.createIndex('numero', 'numero', { unique: true });
                affairesStore.createIndex('nature', 'nature', { unique: false });
                affairesStore.createIndex('statut', 'statut', { unique: false });
                affairesStore.createIndex('maitre', 'maitre', { unique: false });
            }

            // Création du magasin d'objets pour les journaux d'activité
            if (!db.objectStoreNames.contains('activity_logs')) {
                const logsStore = db.createObjectStore('activity_logs', { keyPath: 'id', autoIncrement: true });
                logsStore.createIndex('timestamp', 'timestamp', { unique: false });
                logsStore.createIndex('username', 'username', { unique: false });
                logsStore.createIndex('action', 'action', { unique: false });
                logsStore.createIndex('entity_type', 'entity_type', { unique: false });
                console.log('Magasin d\'objets pour les journaux d\'activité créé');
            }
        };

        // Gestion des erreurs
        request.onerror = (event) => {
            console.error('Erreur lors de l\'ouverture de la base de données:', event.target.error);
            reject(event.target.error);
        };

        // Succès de l'ouverture de la base de données
        request.onsuccess = (event) => {
            db = event.target.result;
            console.log('Base de données ouverte avec succès');
            resolve(db);
        };
    });
}

// ===== FONCTIONS POUR LES AVOCATS =====

// Récupérer tous les avocats
function getAllAvocats() {
    return new Promise((resolve, reject) => {
        if (!db) {
            reject(new Error('La base de données n\'est pas initialisée'));
            return;
        }

        const transaction = db.transaction(['avocats'], 'readonly');
        const store = transaction.objectStore('avocats');
        const request = store.getAll();

        request.onsuccess = () => {
            resolve(request.result);
        };

        request.onerror = (event) => {
            reject(event.target.error);
        };
    });
}

// Récupérer un avocat par son ID
function getAvocatById(id) {
    return new Promise((resolve, reject) => {
        if (!db) {
            reject(new Error('La base de données n\'est pas initialisée'));
            return;
        }

        const transaction = db.transaction(['avocats'], 'readonly');
        const store = transaction.objectStore('avocats');
        const request = store.get(id);

        request.onsuccess = () => {
            resolve(request.result);
        };

        request.onerror = (event) => {
            reject(event.target.error);
        };
    });
}

// Ajouter un nouvel avocat
function addAvocat(avocat) {
    return new Promise((resolve, reject) => {
        if (!db) {
            reject(new Error('La base de données n\'est pas initialisée'));
            return;
        }

        const transaction = db.transaction(['avocats'], 'readwrite');
        const store = transaction.objectStore('avocats');
        const request = store.add(avocat);

        request.onsuccess = () => {
            resolve(request.result); // Retourne l'ID généré
        };

        request.onerror = (event) => {
            reject(event.target.error);
        };
    });
}

// Mettre à jour un avocat existant
function updateAvocat(avocat) {
    return new Promise((resolve, reject) => {
        if (!db) {
            reject(new Error('La base de données n\'est pas initialisée'));
            return;
        }

        const transaction = db.transaction(['avocats'], 'readwrite');
        const store = transaction.objectStore('avocats');
        const request = store.put(avocat);

        request.onsuccess = () => {
            resolve(true);
        };

        request.onerror = (event) => {
            reject(event.target.error);
        };
    });
}

// Supprimer un avocat
function deleteAvocat(id) {
    return new Promise((resolve, reject) => {
        if (!db) {
            reject(new Error('La base de données n\'est pas initialisée'));
            return;
        }

        // Vérifier d'abord si l'avocat est utilisé dans des affaires
        const affairesTransaction = db.transaction(['affaires'], 'readonly');
        const affairesStore = affairesTransaction.objectStore('affaires');
        const affairesIndex = affairesStore.index('maitre');
        const affairesRequest = affairesIndex.getAll(id);

        affairesRequest.onsuccess = () => {
            if (affairesRequest.result.length > 0) {
                reject(new Error('Cet avocat ne peut pas être supprimé car il est associé à une ou plusieurs affaires.'));
                return;
            }

            // Si l'avocat n'est pas utilisé, procéder à la suppression
            const transaction = db.transaction(['avocats'], 'readwrite');
            const store = transaction.objectStore('avocats');
            const request = store.delete(id);

            request.onsuccess = () => {
                resolve(true);
            };

            request.onerror = (event) => {
                reject(event.target.error);
            };
        };

        affairesRequest.onerror = (event) => {
            reject(event.target.error);
        };
    });
}

// ===== FONCTIONS POUR LES AFFAIRES =====

// Récupérer toutes les affaires
function getAllAffaires() {
    return new Promise((resolve, reject) => {
        if (!db) {
            reject(new Error('La base de données n\'est pas initialisée'));
            return;
        }

        const transaction = db.transaction(['affaires'], 'readonly');
        const store = transaction.objectStore('affaires');
        const request = store.getAll();

        request.onsuccess = () => {
            resolve(request.result);
        };

        request.onerror = (event) => {
            reject(event.target.error);
        };
    });
}

// Récupérer une affaire par son ID
function getAffaireById(id) {
    return new Promise((resolve, reject) => {
        if (!db) {
            reject(new Error('La base de données n\'est pas initialisée'));
            return;
        }

        const transaction = db.transaction(['affaires'], 'readonly');
        const store = transaction.objectStore('affaires');
        const request = store.get(id);

        request.onsuccess = () => {
            resolve(request.result);
        };

        request.onerror = (event) => {
            reject(event.target.error);
        };
    });
}

// Ajouter une nouvelle affaire
function addAffaire(affaire) {
    return new Promise((resolve, reject) => {
        if (!db) {
            reject(new Error('La base de données n\'est pas initialisée'));
            return;
        }

        const transaction = db.transaction(['affaires'], 'readwrite');
        const store = transaction.objectStore('affaires');

        // Vérifier si le numéro d'affaire existe déjà
        const index = store.index('numero');
        const checkRequest = index.get(affaire.numero);

        checkRequest.onsuccess = () => {
            if (checkRequest.result) {
                reject(new Error('Ce numéro d\'affaire existe déjà.'));
                return;
            }

            // Si le numéro n'existe pas, ajouter l'affaire
            const addRequest = store.add(affaire);

            addRequest.onsuccess = () => {
                resolve(addRequest.result); // Retourne l'ID généré
            };

            addRequest.onerror = (event) => {
                reject(event.target.error);
            };
        };

        checkRequest.onerror = (event) => {
            reject(event.target.error);
        };
    });
}

// Mettre à jour une affaire existante
function updateAffaire(affaire) {
    return new Promise((resolve, reject) => {
        if (!db) {
            reject(new Error('La base de données n\'est pas initialisée'));
            return;
        }

        const transaction = db.transaction(['affaires'], 'readwrite');
        const store = transaction.objectStore('affaires');

        // Vérifier si le numéro d'affaire existe déjà pour une autre affaire
        const index = store.index('numero');
        const checkRequest = index.get(affaire.numero);

        checkRequest.onsuccess = () => {
            if (checkRequest.result && checkRequest.result.id !== affaire.id) {
                reject(new Error('Ce numéro d\'affaire est déjà utilisé par une autre affaire.'));
                return;
            }

            // Si le numéro n'existe pas ou appartient à cette affaire, mettre à jour
            const updateRequest = store.put(affaire);

            updateRequest.onsuccess = () => {
                resolve(true);
            };

            updateRequest.onerror = (event) => {
                reject(event.target.error);
            };
        };

        checkRequest.onerror = (event) => {
            reject(event.target.error);
        };
    });
}

// Supprimer une affaire
function deleteAffaire(id) {
    return new Promise((resolve, reject) => {
        if (!db) {
            reject(new Error('La base de données n\'est pas initialisée'));
            return;
        }

        const transaction = db.transaction(['affaires'], 'readwrite');
        const store = transaction.objectStore('affaires');
        const request = store.delete(id);

        request.onsuccess = () => {
            resolve(true);
        };

        request.onerror = (event) => {
            reject(event.target.error);
        };
    });
}

// Fonction pour ajouter des données de test
function addTestData() {
    return new Promise(async (resolve, reject) => {
        try {
            // Ajouter des avocats de test
            const avocat1 = {
                nom: "Me Jean Dupont",
                agrement: "A12345",
                adresse: "123 Avenue des Avocats, Paris",
                telephone: "01 23 45 67 89",
                email: "<EMAIL>",
                dateSignature: "2022-01-01",
                validiteContrat: "2024-01-01"
            };

            const avocat2 = {
                nom: "Me Marie Martin",
                agrement: "B67890",
                adresse: "456 Rue du Barreau, Lyon",
                telephone: "01 98 76 54 32",
                email: "<EMAIL>",
                dateSignature: "2022-03-15",
                validiteContrat: "2024-03-15"
            };

            const avocat1Id = await addAvocat(avocat1);
            const avocat2Id = await addAvocat(avocat2);

            // Ajouter des affaires de test
            const affaire1 = {
                numero: "AFF-2023-001",
                nature: "commerciale",
                objet: "Litige contractuel avec fournisseur",
                parties: "Société X c/ Fournisseur Y",
                juridiction: "tribunal",
                engagement: "societe",
                dateEngagement: "2023-01-15",
                dateJugement: "2023-06-20",
                statut: "jugee-faveur",
                dispositif: "Condamnation du fournisseur à verser des dommages et intérêts",
                montant: 50000,
                maitre: avocat1Id
            };

            const affaire2 = {
                numero: "AFF-2023-002",
                nature: "social",
                objet: "Licenciement contesté",
                parties: "Employé Z c/ Société X",
                juridiction: "cour",
                engagement: "tiers",
                dateEngagement: "2023-02-10",
                dateJugement: "",
                statut: "en-cours",
                dispositif: "",
                montant: 0,
                maitre: avocat2Id
            };

            await addAffaire(affaire1);
            await addAffaire(affaire2);

            resolve(true);
        } catch (error) {
            console.error('Erreur lors de l\'ajout des données de test:', error);
            reject(error);
        }
    });
}

// ===== FONCTIONS POUR LES JOURNAUX D'ACTIVITÉ =====

// Ajouter une entrée dans le journal d'activité
function addActivityLog(logEntry) {
    return new Promise((resolve, reject) => {
        if (!db) {
            reject(new Error('La base de données n\'est pas initialisée'));
            return;
        }

        // Ajouter automatiquement un timestamp si non fourni
        if (!logEntry.timestamp) {
            logEntry.timestamp = new Date().toISOString();
        }

        const transaction = db.transaction(['activity_logs'], 'readwrite');
        const store = transaction.objectStore('activity_logs');
        const request = store.add(logEntry);

        request.onsuccess = () => {
            resolve(request.result); // Retourne l'ID généré
        };

        request.onerror = (event) => {
            reject(event.target.error);
        };
    });
}

// Récupérer tous les journaux d'activité
function getAllActivityLogs() {
    return new Promise((resolve, reject) => {
        if (!db) {
            reject(new Error('La base de données n\'est pas initialisée'));
            return;
        }

        const transaction = db.transaction(['activity_logs'], 'readonly');
        const store = transaction.objectStore('activity_logs');
        const request = store.getAll();

        request.onsuccess = () => {
            resolve(request.result);
        };

        request.onerror = (event) => {
            reject(event.target.error);
        };
    });
}

// Récupérer les journaux d'activité filtrés par utilisateur
function getActivityLogsByUser(username) {
    return new Promise((resolve, reject) => {
        if (!db) {
            reject(new Error('La base de données n\'est pas initialisée'));
            return;
        }

        const transaction = db.transaction(['activity_logs'], 'readonly');
        const store = transaction.objectStore('activity_logs');
        const index = store.index('username');
        const request = index.getAll(username);

        request.onsuccess = () => {
            resolve(request.result);
        };

        request.onerror = (event) => {
            reject(event.target.error);
        };
    });
}

// Récupérer les journaux d'activité filtrés par type d'action
function getActivityLogsByAction(action) {
    return new Promise((resolve, reject) => {
        if (!db) {
            reject(new Error('La base de données n\'est pas initialisée'));
            return;
        }

        const transaction = db.transaction(['activity_logs'], 'readonly');
        const store = transaction.objectStore('activity_logs');
        const index = store.index('action');
        const request = index.getAll(action);

        request.onsuccess = () => {
            resolve(request.result);
        };

        request.onerror = (event) => {
            reject(event.target.error);
        };
    });
}

// Récupérer les journaux d'activité filtrés par type d'entité
function getActivityLogsByEntityType(entityType) {
    return new Promise((resolve, reject) => {
        if (!db) {
            reject(new Error('La base de données n\'est pas initialisée'));
            return;
        }

        const transaction = db.transaction(['activity_logs'], 'readonly');
        const store = transaction.objectStore('activity_logs');
        const index = store.index('entity_type');
        const request = index.getAll(entityType);

        request.onsuccess = () => {
            resolve(request.result);
        };

        request.onerror = (event) => {
            reject(event.target.error);
        };
    });
}

// Fonction pour réinitialiser complètement la base de données
function resetDatabase() {
    return new Promise((resolve, reject) => {
        // Fermer la connexion à la base de données si elle est ouverte
        if (db) {
            db.close();
            db = null;
        }

        // Supprimer la base de données
        const deleteRequest = indexedDB.deleteDatabase(DB_NAME);

        deleteRequest.onsuccess = () => {
            console.log('Base de données supprimée avec succès');
            // Réinitialiser la base de données
            initDatabase()
                .then(() => {
                    console.log('Base de données réinitialisée avec succès');
                    resolve(true);
                })
                .catch(error => {
                    console.error('Erreur lors de la réinitialisation de la base de données:', error);
                    reject(error);
                });
        };

        deleteRequest.onerror = (event) => {
            console.error('Erreur lors de la suppression de la base de données:', event.target.error);
            reject(event.target.error);
        };
    });
}

// Exporter les fonctions dans l'objet global window
window.DB = {
    initDatabase,
    getAllAvocats,
    getAvocatById,
    addAvocat,
    updateAvocat,
    deleteAvocat,
    getAllAffaires,
    getAffaireById,
    addAffaire,
    updateAffaire,
    deleteAffaire,
    addTestData,
    // Fonctions pour les journaux d'activité
    addActivityLog,
    getAllActivityLogs,
    getActivityLogsByUser,
    getActivityLogsByAction,
    getActivityLogsByEntityType,
    // Fonction de réinitialisation
    resetDatabase
};


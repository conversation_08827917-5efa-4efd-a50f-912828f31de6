<?php
// Script pour tester l'authentification et les API

// Activer l'affichage des erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Inclure les fichiers nécessaires
require_once __DIR__ . '/config.php';

// Fonction pour effectuer une requête HTTP
function makeRequest($url, $method = 'GET', $data = null, $cookies = []) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, false);
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
    }
    
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    
    // Ajouter les cookies
    if (!empty($cookies)) {
        $cookieStr = '';
        foreach ($cookies as $name => $value) {
            $cookieStr .= $name . '=' . $value . '; ';
        }
        curl_setopt($ch, CURLOPT_COOKIE, $cookieStr);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    return [
        'code' => $httpCode,
        'response' => $response,
        'error' => $error
    ];
}

// Fonction pour créer un utilisateur administrateur si nécessaire
function createAdminUser() {
    try {
        $pdo = getDbConnection();
        
        // Vérifier si l'utilisateur admin existe déjà
        $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
        $stmt->execute(['admin']);
        
        if (!$stmt->fetch()) {
            // Créer l'utilisateur admin
            $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("INSERT INTO users (username, password_hash, email, role) VALUES (?, ?, ?, ?)");
            $stmt->execute(['admin', $hashedPassword, '<EMAIL>', 'admin']);
            
            echo "<p>Utilisateur admin créé avec succès.</p>";
        } else {
            echo "<p>L'utilisateur admin existe déjà.</p>";
        }
    } catch (Exception $e) {
        echo "<p>Erreur lors de la création de l'utilisateur admin: " . $e->getMessage() . "</p>";
    }
}

// Fonction pour créer une session PHP
function createSession($userId, $username, $role) {
    session_start();
    
    $_SESSION['user_id'] = $userId;
    $_SESSION['username'] = $username;
    $_SESSION['user_role'] = $role;
    $_SESSION['last_activity'] = time();
    
    return session_id();
}

// URL de base
$baseUrl = 'http://' . $_SERVER['HTTP_HOST'];

echo "<h1>Test d'authentification et des API</h1>";

// Créer un utilisateur admin si nécessaire
echo "<h2>1. Création d'un utilisateur admin</h2>";
createAdminUser();

// Tester l'authentification directe
echo "<h2>2. Test d'authentification directe</h2>";
try {
    $pdo = getDbConnection();
    
    // Récupérer l'utilisateur admin
    $stmt = $pdo->prepare("SELECT id, username, role FROM users WHERE username = ?");
    $stmt->execute(['admin']);
    $user = $stmt->fetch();
    
    if ($user) {
        // Créer une session pour l'utilisateur
        $sessionId = createSession($user['id'], $user['username'], $user['role']);
        
        echo "<p>Session créée avec succès. ID de session: " . $sessionId . "</p>";
        echo "<p>Utilisateur: " . $user['username'] . ", Rôle: " . $user['role'] . "</p>";
        
        // Tester l'accès à l'API des journaux d'activité
        echo "<h2>3. Test d'accès à l'API des journaux d'activité</h2>";
        
        // Ajouter un enregistrement de test dans la table activity_logs
        try {
            $stmt = $pdo->prepare("INSERT INTO activity_logs (username, action, entity_type, details, ip_address) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute(['admin', 'test', 'database', 'Test direct depuis test_auth.php', '127.0.0.1']);
            
            echo "<p>Enregistrement de test ajouté avec succès.</p>";
        } catch (Exception $e) {
            echo "<p>Erreur lors de l'ajout d'un enregistrement de test: " . $e->getMessage() . "</p>";
        }
        
        // Récupérer les journaux d'activité
        try {
            $stmt = $pdo->query("SELECT * FROM activity_logs ORDER BY id DESC LIMIT 5");
            $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h3>Les 5 derniers enregistrements:</h3>";
            echo "<pre>" . print_r($logs, true) . "</pre>";
        } catch (Exception $e) {
            echo "<p>Erreur lors de la récupération des journaux d'activité: " . $e->getMessage() . "</p>";
        }
        
        // Liens vers les pages
        echo "<h2>4. Liens vers les pages</h2>";
        echo "<ul>";
        echo "<li><a href='" . $baseUrl . "/views/admin/admin_logs.html' target='_blank'>Page du journal d'activité</a></li>";
        echo "<li><a href='" . $baseUrl . "/api/logs_api.php?action=list' target='_blank'>API des journaux d'activité</a></li>";
        echo "</ul>";
        
        // Afficher les cookies de session
        echo "<h2>5. Cookies de session</h2>";
        echo "<pre>" . print_r($_COOKIE, true) . "</pre>";
        
        // Afficher les variables de session
        echo "<h2>6. Variables de session</h2>";
        echo "<pre>" . print_r($_SESSION, true) . "</pre>";
    } else {
        echo "<p>Utilisateur admin non trouvé.</p>";
    }
} catch (Exception $e) {
    echo "<p>Erreur lors du test d'authentification: " . $e->getMessage() . "</p>";
}

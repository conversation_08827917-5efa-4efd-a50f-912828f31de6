<?php
// Script pour télécharger les dépendances externes et les stocker localement

// Créer les répertoires nécessaires s'ils n'existent pas
$directories = [
    'assets',
    'assets/css',
    'assets/js',
    'assets/fonts',
    'assets/icons'
];

foreach ($directories as $dir) {
    if (!file_exists($dir)) {
        mkdir($dir, 0755, true);
        echo "Répertoire créé: $dir<br>";
    }
}

// Liste des fichiers à télécharger
$files = [
    // Bootstrap CSS
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' => 'assets/css/bootstrap.min.css',
    
    // Bootstrap JS
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js' => 'assets/js/bootstrap.bundle.min.js',
    
    // Bootstrap Icons CSS
    'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css' => 'assets/css/bootstrap-icons.css',
    
    // jQuery
    'https://code.jquery.com/jquery-3.6.0.min.js' => 'assets/js/jquery.min.js',
    
    // DataTables CSS
    'https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css' => 'assets/css/dataTables.bootstrap5.min.css',
    
    // DataTables JS
    'https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js' => 'assets/js/jquery.dataTables.min.js',
    'https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js' => 'assets/js/dataTables.bootstrap5.min.js'
];

// Télécharger les fichiers
foreach ($files as $url => $path) {
    $content = @file_get_contents($url);
    if ($content === false) {
        echo "Erreur lors du téléchargement de $url<br>";
        continue;
    }
    
    if (file_put_contents($path, $content)) {
        echo "Fichier téléchargé: $path<br>";
    } else {
        echo "Erreur lors de l'enregistrement de $path<br>";
    }
}

// Télécharger les polices Bootstrap Icons
$bootstrapIconsFontsUrl = 'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/fonts/bootstrap-icons.woff2';
$bootstrapIconsFontsPath = 'assets/fonts/bootstrap-icons.woff2';

$content = @file_get_contents($bootstrapIconsFontsUrl);
if ($content !== false && file_put_contents($bootstrapIconsFontsPath, $content)) {
    echo "Fichier téléchargé: $bootstrapIconsFontsPath<br>";
} else {
    echo "Erreur lors du téléchargement ou de l'enregistrement de $bootstrapIconsFontsPath<br>";
}

// Mettre à jour le chemin des polices dans le fichier CSS de Bootstrap Icons
$cssContent = file_get_contents('assets/css/bootstrap-icons.css');
$cssContent = str_replace('fonts/bootstrap-icons', '../fonts/bootstrap-icons', $cssContent);
file_put_contents('assets/css/bootstrap-icons.css', $cssContent);
echo "Fichier CSS de Bootstrap Icons mis à jour<br>";

echo "<h2>Téléchargement des dépendances terminé!</h2>";
echo "<p>Vous pouvez maintenant modifier les fichiers HTML pour utiliser les fichiers locaux au lieu des CDN.</p>";
echo "<a href='update_references.php' class='btn btn-primary'>Mettre à jour les références</a>";
?>

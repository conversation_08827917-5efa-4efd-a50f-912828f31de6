<?php
// Script d'initialisation des utilisateurs par défaut
require_once 'config.php';

// Fonction pour ajouter des utilisateurs par défaut
function addDefaultUsers() {
    try {
        $pdo = getDbConnection();
        
        // Vérifier si des utilisateurs existent déjà
        $stmt = $pdo->query("SELECT COUNT(*) FROM users");
        $userCount = $stmt->fetchColumn();
        
        if ($userCount > 0) {
            echo "Des utilisateurs existent déjà dans la base de données.<br>";
            return false;
        }
        
        // Ajouter un utilisateur administrateur par défaut
        $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (username, password, email, role) VALUES (?, ?, ?, ?)");
        $stmt->execute(['admin', $adminPassword, '<EMAIL>', 'admin']);
        
        // Ajouter un utilisateur standard par défaut
        $userPassword = password_hash('user123', PASSWORD_DEFAULT);
        $stmt->execute(['user', $userPassword, '<EMAIL>', 'user']);
        
        echo "Utilisateurs par défaut créés avec succès:<br>";
        echo "- Administrateur: admin / admin123<br>";
        echo "- Utilisateur: user / user123<br>";
        
        return true;
    } catch (PDOException $e) {
        die("Erreur lors de l'ajout des utilisateurs par défaut: " . $e->getMessage());
    }
}

// Ajouter des utilisateurs par défaut
if (addDefaultUsers()) {
    echo "<br>Vous pouvez maintenant vous connecter avec l'un des comptes ci-dessus.<br>";
    echo "<br><a href='login.html' class='btn btn-primary'>Aller à la page de connexion</a>";
} else {
    echo "<br><a href='login.html' class='btn btn-primary'>Retour à la page de connexion</a>";
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Initialisation des utilisateurs</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #0d6efd;
            margin-bottom: 20px;
        }
        .btn {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Initialisation des utilisateurs</h1>
        <!-- Les messages PHP seront affichés ici -->
    </div>
</body>
</html>

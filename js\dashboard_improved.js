// Script pour le tableau de bord amélioré

// Attendre que le DOM soit chargé
document.addEventListener('DOMContentLoaded', function() {
    // Vérifier si l'utilisateur est connecté
    if (!SessionManager.isLoggedIn()) {
        window.location.href = 'login.html';
        return;
    }

    // Afficher les informations de l'utilisateur
    const currentUser = SessionManager.getCurrentUser();
    if (currentUser) {
        document.getElementById('user-info').textContent = `Connecté en tant que: ${currentUser.username} (${currentUser.role})`;
    }

    // Gérer la déconnexion
    document.getElementById('logout-btn').addEventListener('click', function(e) {
        e.preventDefault();
        SessionManager.logout()
            .then(() => {
                window.location.href = 'login.html';
            })
            .catch(error => {
                console.error('Erreur lors de la déconnexion:', error);
                alert('Erreur lors de la déconnexion. Veuillez réessayer.');
            });
    });

    // Initialiser le tableau de bord
    initDashboard();

    // Gérer les événements
    setupEventListeners();
});

// Fonction pour initialiser le tableau de bord
async function initDashboard() {
    try {
        // Récupérer les statistiques
        const stats = await DashboardSimple.getStatistiques();

        // Mettre à jour les KPI
        updateKPIs(stats);

        // Mettre à jour le résumé financier
        updateFinancialSummary(stats);

        // Créer les graphiques
        createCharts(stats);

        // Afficher les affaires à haut risque
        await displayHighRiskCases();

        // Afficher les alertes
        displayAlerts(stats);

        // Afficher les performances des avocats
        displayLawyerPerformance(stats);

        // Remplir le filtre des avocats
        await populateLawyerFilter();

        console.log('Tableau de bord initialisé avec succès');
    } catch (error) {
        console.error('Erreur lors de l\'initialisation du tableau de bord:', error);
        alert('Erreur lors du chargement du tableau de bord. Veuillez actualiser la page.');
    }
}

// Fonction pour mettre à jour les KPI
function updateKPIs(stats) {
    // Mettre à jour les compteurs avec animation
    animateCounter('total-affaires', stats.totalAffaires);

    // Calculer et afficher le taux de succès
    const tauxSucces = stats.affairesJugees > 0
        ? Math.round((stats.affairesGagnees / stats.affairesJugees) * 100)
        : 0;
    animateCounter('taux-succes', tauxSucces, '%');

    // Formater et afficher le montant total
    const montantTotal = new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(stats.montantTotal);
    document.getElementById('montant-total').textContent = montantTotal;

    // Mettre à jour le nombre d'avocats
    animateCounter('total-avocats', stats.totalAvocats);

    // Mettre à jour le nombre de contrats expirants
    document.getElementById('contrats-expirant').textContent = stats.avocatsContratExpirant.length;

    // Simuler des tendances pour la démonstration
    document.getElementById('trend-affaires').textContent = '12%';
    document.getElementById('trend-succes').textContent = '0%';
    document.getElementById('trend-montant').textContent = '8%';
}

// Fonction pour animer un compteur
function animateCounter(elementId, targetValue, suffix = '') {
    const element = document.getElementById(elementId);
    if (!element) return;

    const duration = 1000; // durée de l'animation en ms
    const startValue = 0;
    const increment = targetValue / (duration / 16); // 60 FPS

    let currentValue = startValue;
    const startTime = performance.now();

    function updateCounter(timestamp) {
        const elapsed = timestamp - startTime;

        if (elapsed < duration) {
            currentValue = Math.min(startValue + (increment * elapsed), targetValue);
            element.textContent = Math.round(currentValue) + suffix;
            requestAnimationFrame(updateCounter);
        } else {
            element.textContent = targetValue + suffix;
        }
    }

    requestAnimationFrame(updateCounter);
}

// Fonction pour mettre à jour le résumé financier
function updateFinancialSummary(stats) {
    document.getElementById('montant-en-cours').textContent = new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(stats.resumeFinancier.montantEnCours);
    document.getElementById('montant-gagne').textContent = new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(stats.resumeFinancier.montantGagne);
    document.getElementById('montant-perdu').textContent = new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(stats.resumeFinancier.montantPerdu);
    document.getElementById('montant-moyen').textContent = new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(stats.resumeFinancier.montantMoyen);
}

// Fonction pour créer les graphiques
function createCharts(stats) {
    try {
        // Fonction utilitaire pour créer un graphique
        function creerGraphique(elementId, type, data, options = {}) {
            const canvas = document.getElementById(elementId);
            if (!canvas) {
                console.error(`Élément canvas #${elementId} non trouvé`);
                return null;
            }

            try {
                // Détruire le graphique existant s'il y en a un
                if (window.Chart && Chart.getChart) {
                    const existingChart = Chart.getChart(canvas);
                    if (existingChart) {
                        existingChart.destroy();
                    }
                }

                // Créer le nouveau graphique
                return new Chart(canvas, {
                    type: type,
                    data: data,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        ...options
                    }
                });
            } catch (error) {
                console.error(`Erreur lors de la création du graphique ${elementId}:`, error);
                return null;
            }
        }

        // Graphique de répartition par nature
        // Vérifier si les données sont vides et ajouter des données de test si nécessaire
        let natureLabels = Object.keys(stats.repartitionNature);
        let natureValues = Object.values(stats.repartitionNature);

        // Si aucune donnée n'est disponible, utiliser des données de test
        if (natureLabels.length === 0) {
            console.warn('Aucune donnée de répartition par nature disponible, utilisation de données de test');
            natureLabels = ['Social', 'Administrative', 'Commerciale', 'Foncier', 'Référé', 'Civile', 'Pénal'];
            natureValues = [5, 8, 12, 3, 7, 10, 4];
        }

        const natureData = {
            labels: natureLabels,
            datasets: [{
                data: natureValues,
                backgroundColor: [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40', '#00CC99'
                ],
                borderWidth: 1,
                borderColor: '#fff'
            }]
        };
        creerGraphique('nature-chart', 'doughnut', natureData, {
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        pointStyle: 'circle'
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw || 0;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = Math.round((value / total) * 100);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            }
        });

        // Graphique de répartition par juridiction
        // Vérifier si les données sont vides et ajouter des données de test si nécessaire
        let juridictionLabels = Object.keys(stats.repartitionJuridiction);
        let juridictionValues = Object.values(stats.repartitionJuridiction);

        // Si aucune donnée n'est disponible, utiliser des données de test
        if (juridictionLabels.length === 0) {
            console.warn('Aucune donnée de répartition par juridiction disponible, utilisation de données de test');
            juridictionLabels = ['Tribunal', 'Cour', 'Cours Suprême', 'C État'];
            juridictionValues = [15, 8, 3, 5];
        }

        const juridictionData = {
            labels: juridictionLabels,
            datasets: [{
                label: 'Nombre d\'affaires',
                data: juridictionValues,
                backgroundColor: '#36A2EB',
                borderRadius: 6
            }]
        };
        creerGraphique('juridiction-chart', 'bar', juridictionData, {
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        drawBorder: false
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        });

        // Graphique d'évolution des affaires dans le temps
        let evolutionLabels = [];
        let evolutionData = [];

        if (stats.evolutionAffaires && stats.evolutionAffaires.labels.length > 0) {
            evolutionLabels = stats.evolutionAffaires.labels;
            evolutionData = stats.evolutionAffaires.data;
        } else {
            // Données de test si aucune donnée n'est disponible
            console.warn('Aucune donnée d\'évolution des affaires disponible, utilisation de données de test');
            const currentDate = new Date();
            for (let i = 6; i >= 0; i--) {
                const month = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
                evolutionLabels.push(`${month.getMonth() + 1}/${month.getFullYear()}`);
                evolutionData.push(Math.floor(Math.random() * 10) + 5); // Valeurs aléatoires entre 5 et 15
            }
        }

        const evolutionDataset = {
            labels: evolutionLabels,
            datasets: [{
                label: 'Nombre d\'affaires',
                data: evolutionData,
                borderColor: '#4BC0C0',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.3,
                fill: true,
                pointBackgroundColor: '#fff',
                pointBorderColor: '#4BC0C0',
                pointBorderWidth: 2,
                pointRadius: 4
            }]
        };

        creerGraphique('evolution-chart', 'line', evolutionDataset, {
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        drawBorder: false
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        });

        // Graphique des montants par nature d'affaire
        let montantLabels = [];
        let montantValues = [];

        if (stats.montantParNature && Object.keys(stats.montantParNature).length > 0) {
            montantLabels = Object.keys(stats.montantParNature);
            montantValues = Object.values(stats.montantParNature);
        } else {
            // Données de test si aucune donnée n'est disponible
            console.warn('Aucune donnée de montant par nature disponible, utilisation de données de test');
            montantLabels = ['Social', 'Administrative', 'Commerciale', 'Foncier', 'Référé', 'Civile', 'Pénal'];
            montantValues = [250000, 420000, 680000, 150000, 320000, 480000, 190000];
        }

        const montantNatureData = {
            labels: montantLabels,
            datasets: [{
                label: 'Montant (€)',
                data: montantValues,
                backgroundColor: '#FF9F40',
                borderRadius: 6
            }]
        };

        creerGraphique('montant-nature-chart', 'bar', montantNatureData, {
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        drawBorder: false
                    },
                    ticks: {
                        callback: function(value) {
                            return new Intl.NumberFormat('fr-FR', {
                                style: 'currency',
                                currency: 'EUR',
                                maximumFractionDigits: 0
                            }).format(value);
                        }
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return new Intl.NumberFormat('fr-FR', {
                                style: 'currency',
                                currency: 'EUR'
                            }).format(context.raw);
                        }
                    }
                }
            }
        });
    } catch (chartError) {
        console.error('Erreur lors de la création des graphiques:', chartError);
    }
}

// Fonction pour afficher les affaires à haut risque
async function displayHighRiskCases() {
    try {
        // Récupérer toutes les affaires
        const affaires = await DB.getAllAffaires();

        // Filtrer les affaires à haut risque (montant > 500 000 €)
        const highRiskCases = affaires.filter(affaire =>
            affaire && parseFloat(affaire.montant) > 500000
        );

        // Récupérer les avocats pour afficher leurs noms
        const avocats = await DB.getAllAvocats();
        const avocatsMap = {};
        avocats.forEach(avocat => {
            if (avocat && avocat.id) {
                avocatsMap[avocat.id] = avocat.nom;
            }
        });

        // Référence au tableau
        const tableBody = document.getElementById('high-risk-table-body');
        if (!tableBody) return;

        // Vider le tableau
        tableBody.innerHTML = '';

        // Si aucune affaire à haut risque, afficher un message
        if (highRiskCases.length === 0) {
            const row = document.createElement('tr');
            row.innerHTML = `<td colspan="7" class="text-center">Aucune affaire à haut risque trouvée</td>`;
            tableBody.appendChild(row);
            return;
        }

        // Trier les affaires par montant décroissant
        highRiskCases.sort((a, b) => parseFloat(b.montant) - parseFloat(a.montant));

        // Ajouter chaque affaire au tableau
        highRiskCases.forEach(affaire => {
            const row = document.createElement('tr');

            // Formater le montant
            const montantFormatte = new Intl.NumberFormat('fr-FR', {
                style: 'currency',
                currency: 'EUR'
            }).format(affaire.montant);

            // Déterminer la classe de statut
            let statutClass = '';
            let statutLibelle = '';

            switch(affaire.statut) {
                case 'en-cours':
                    statutClass = 'bg-primary';
                    statutLibelle = 'En cours';
                    break;
                case 'jugee-faveur':
                    statutClass = 'bg-success';
                    statutLibelle = 'Jugée en faveur';
                    break;
                case 'jugee-tiers':
                    statutClass = 'bg-danger';
                    statutLibelle = 'Jugée pour tiers';
                    break;
                default:
                    statutClass = 'bg-secondary';
                    statutLibelle = 'Non défini';
            }

            // Nom de l'avocat
            const nomAvocat = avocatsMap[affaire.avocat_id] || 'Non assigné';

            row.innerHTML = `
                <td>${affaire.numero || 'N/A'}</td>
                <td>${affaire.objet || 'N/A'}</td>
                <td class="fw-bold">${montantFormatte}</td>
                <td><span class="badge ${statutClass}">${statutLibelle}</span></td>
                <td>${affaire.juridiction || 'N/A'}</td>
                <td>${nomAvocat}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary view-case" data-id="${affaire.id}">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            `;

            tableBody.appendChild(row);
        });

        // Ajouter des écouteurs d'événements pour les boutons de visualisation
        document.querySelectorAll('.view-case').forEach(button => {
            button.addEventListener('click', function() {
                const caseId = this.getAttribute('data-id');
                window.location.href = `app.html#affaires?id=${caseId}`;
            });
        });

    } catch (error) {
        console.error('Erreur lors de l\'affichage des affaires à haut risque:', error);
        const tableBody = document.getElementById('high-risk-table-body');
        if (tableBody) {
            tableBody.innerHTML = `<tr><td colspan="7" class="text-center text-danger">Erreur lors du chargement des affaires à haut risque</td></tr>`;
        }
    }
}

// Fonction pour afficher les alertes
function displayAlerts(stats) {
    const alertesList = document.getElementById('contrats-expirants-list');
    if (!alertesList) return;

    // Vider la liste
    alertesList.innerHTML = '';

    // Afficher les avocats dont le contrat expire bientôt
    if (stats.avocatsContratExpirant && stats.avocatsContratExpirant.length > 0) {
        const ul = document.createElement('ul');
        ul.className = 'list-unstyled mb-0';

        stats.avocatsContratExpirant.forEach(avocat => {
            const li = document.createElement('li');
            li.className = 'mb-2';

            // Calculer le nombre de jours restants
            const today = new Date();
            const expirationDate = new Date(avocat.validiteContrat);
            const daysRemaining = Math.ceil((expirationDate - today) / (1000 * 60 * 60 * 24));

            // Déterminer la classe d'urgence
            let urgencyClass = 'text-warning';
            if (daysRemaining <= 0) {
                urgencyClass = 'text-danger fw-bold';
            } else if (daysRemaining <= 30) {
                urgencyClass = 'text-danger';
            }

            li.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <span>${avocat.nom}</span>
                    <span class="${urgencyClass}">
                        ${daysRemaining <= 0 ? 'Expiré' : `Expire dans ${daysRemaining} jour${daysRemaining > 1 ? 's' : ''}`}
                    </span>
                </div>
                <small class="text-muted">Date d'expiration: ${new Date(avocat.validiteContrat).toLocaleDateString()}</small>
            `;

            ul.appendChild(li);
        });

        alertesList.appendChild(ul);
    } else {
        // Si aucune alerte, afficher un message
        alertesList.innerHTML = `
            <p class="text-center text-success mb-0">
                <i class="fas fa-check-circle me-2"></i>
                Tous les contrats sont à jour
            </p>
        `;
    }
}

// Fonction pour afficher les performances des avocats
function displayLawyerPerformance(stats) {
    // Cette fonction est simulée car nous n'avons pas les données de performance dans notre modèle actuel
    const performanceList = document.getElementById('performance-avocats-list');
    if (!performanceList) return;

    // Vider la liste
    performanceList.innerHTML = '';

    // Simuler des données de performance pour la démonstration
    const performanceData = [
        { nom: 'Me Dupont', tauxReussite: 85, totalAffaires: 12, affairesGagnees: 10, montantTotal: 750000 },
        { nom: 'Me Martin', tauxReussite: 70, totalAffaires: 10, affairesGagnees: 7, montantTotal: 520000 },
        { nom: 'Me Durand', tauxReussite: 60, totalAffaires: 15, affairesGagnees: 9, montantTotal: 980000 },
        { nom: 'Me Petit', tauxReussite: 40, totalAffaires: 5, affairesGagnees: 2, montantTotal: 320000 },
        { nom: 'Me Leroy', tauxReussite: 90, totalAffaires: 8, affairesGagnees: 7, montantTotal: 620000 }
    ];

    // Trier par taux de réussite décroissant
    performanceData.sort((a, b) => b.tauxReussite - a.tauxReussite);

    // Ajouter chaque avocat à la liste
    performanceData.forEach(avocat => {
        const item = document.createElement('div');
        item.className = 'list-group-item';

        // Déterminer la classe de couleur pour le taux de réussite
        let tauxClass = 'text-warning';
        if (avocat.tauxReussite >= 70) {
            tauxClass = 'text-success';
        } else if (avocat.tauxReussite < 50) {
            tauxClass = 'text-danger';
        }

        // Formater le montant
        const montantFormatte = new Intl.NumberFormat('fr-FR', {
            style: 'currency',
            currency: 'EUR'
        }).format(avocat.montantTotal);

        item.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="mb-1">${avocat.nom}</h6>
                <span class="${tauxClass} fw-bold">${avocat.tauxReussite}%</span>
            </div>
            <div class="progress mt-2 mb-2" style="height: 6px;">
                <div class="progress-bar bg-${tauxClass.replace('text-', '')}" role="progressbar"
                    style="width: ${avocat.tauxReussite}%" aria-valuenow="${avocat.tauxReussite}"
                    aria-valuemin="0" aria-valuemax="100"></div>
            </div>
            <div class="d-flex justify-content-between">
                <small>Affaires: ${avocat.totalAffaires} (${avocat.affairesGagnees} gagnées)</small>
                <small>Montant: ${montantFormatte}</small>
            </div>
        `;

        performanceList.appendChild(item);
    });
}

// Fonction pour remplir le filtre des avocats
async function populateLawyerFilter() {
    try {
        // Récupérer tous les avocats
        const avocats = await DB.getAllAvocats();

        // Référence au select
        const select = document.getElementById('filter-avocat');
        if (!select) return;

        // Ajouter chaque avocat au select
        avocats.forEach(avocat => {
            if (avocat && avocat.nom) {
                const option = document.createElement('option');
                option.value = avocat.id;
                option.textContent = avocat.nom;
                select.appendChild(option);
            }
        });
    } catch (error) {
        console.error('Erreur lors du remplissage du filtre des avocats:', error);
    }
}

// Fonction pour configurer les écouteurs d'événements
function setupEventListeners() {
    // Bouton d'actualisation du tableau de bord
    const refreshBtn = document.getElementById('btn-refresh-dashboard');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Actualisation...';

            initDashboard().then(() => {
                this.disabled = false;
                this.innerHTML = '<i class="fas fa-sync-alt me-1"></i> Actualiser';
            });
        });
    }

    // Bouton d'export
    const exportBtn = document.getElementById('btn-export-dashboard');
    if (exportBtn) {
        exportBtn.addEventListener('click', async function() {
            try {
                const stats = await DashboardSimple.getStatistiques();
                DashboardSimple.exporterDonnees(stats);
            } catch (error) {
                console.error('Erreur lors de l\'export des données:', error);
                alert('Erreur lors de l\'export des données. Veuillez réessayer.');
            }
        });
    }

    // Bouton d'application des filtres
    const applyFiltersBtn = document.getElementById('btn-apply-filters');
    if (applyFiltersBtn) {
        applyFiltersBtn.addEventListener('click', function() {
            // Cette fonctionnalité nécessiterait une implémentation plus complexe
            // pour filtrer les données du tableau de bord
            alert('Fonctionnalité de filtrage en cours de développement.');
        });
    }

    // Bouton de réinitialisation des filtres
    const resetFiltersBtn = document.getElementById('btn-reset-filters');
    if (resetFiltersBtn) {
        resetFiltersBtn.addEventListener('click', function() {
            // Réinitialiser tous les filtres
            document.getElementById('filter-nature').value = '';
            document.getElementById('filter-juridiction').value = '';
            document.getElementById('filter-statut').value = '';
            document.getElementById('filter-avocat').value = '';

            // Actualiser le tableau de bord
            initDashboard();
        });
    }
}

// Exporter les fonctions
window.initDashboard = initDashboard;

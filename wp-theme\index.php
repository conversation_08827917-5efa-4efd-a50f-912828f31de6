<?php get_header(); ?>

<main class="site-main">
    <div class="container">
        <div class="content-area">
            <div class="main-content">
                <?php if (have_posts()) : ?>
                    <?php while (have_posts()) : the_post(); ?>
                        <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
                            <header class="entry-header">
                                <h1 class="entry-title">
                                    <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                </h1>
                                <div class="entry-meta">
                                    <span class="posted-on">
                                        Publié le <?php echo get_the_date(); ?>
                                    </span>
                                    <span class="byline">
                                        par <?php the_author(); ?>
                                    </span>
                                    <?php if (has_category()) : ?>
                                        <span class="cat-links">
                                            dans <?php the_category(', '); ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </header>

                            <div class="entry-content">
                                <?php if (is_home() || is_archive()) : ?>
                                    <?php the_excerpt(); ?>
                                    <a href="<?php the_permalink(); ?>" class="juridique-btn">Lire la suite</a>
                                <?php else : ?>
                                    <?php the_content(); ?>
                                <?php endif; ?>
                            </div>
                        </article>
                    <?php endwhile; ?>

                    <!-- Navigation des pages -->
                    <div class="posts-navigation">
                        <?php
                        the_posts_pagination(array(
                            'prev_text' => '&laquo; Précédent',
                            'next_text' => 'Suivant &raquo;',
                        ));
                        ?>
                    </div>

                <?php else : ?>
                    <article class="no-results">
                        <header class="entry-header">
                            <h1 class="entry-title">Aucun contenu trouvé</h1>
                        </header>
                        <div class="entry-content">
                            <p>Désolé, aucun contenu ne correspond à votre recherche.</p>
                        </div>
                    </article>
                <?php endif; ?>
            </div>

            <?php get_sidebar(); ?>
        </div>

        <!-- Section d'intégration du système juridique -->
        <?php if (is_home()) : ?>
            <div class="juridique-integration-section">
                <h2>Accès au Système de Gestion Juridique</h2>
                <p style="text-align: center; margin-bottom: 1.5rem;">
                    Accédez directement à notre système de gestion des affaires juridiques pour consulter vos dossiers et suivre l'avancement de vos affaires.
                </p>
                
                <div class="juridique-access-buttons">
                    <a href="<?php echo admin_url('admin.php?page=juridique-integration'); ?>" class="juridique-btn">
                        Tableau de bord
                    </a>
                    <a href="<?php echo defined('JURIDIQUE_APP_URL') ? JURIDIQUE_APP_URL : '#'; ?>" class="juridique-btn secondary" target="_blank">
                        Application complète
                    </a>
                </div>

                <!-- Affichage des statistiques -->
                <?php echo do_shortcode('[juridique_stats]'); ?>
            </div>
        <?php endif; ?>
    </div>
</main>

<?php get_footer(); ?>

# Structure du projet de suivi des affaires juridiques

Ce document explique la nouvelle structure du projet pour une meilleure organisation et maintenance.

## Structure des répertoires

```
/
├── app/                    # Code principal de l'application
│   ├── api/                # API endpoints
│   ├── config/             # Fichiers de configuration
│   ├── core/               # Fonctions et classes de base
│   ├── models/             # Modèles de données
│   └── utils/              # Utilitaires
├── assets/                 # Ressources statiques
│   ├── css/                # Fichiers CSS
│   ├── fonts/              # Polices
│   ├── icons/              # Icônes
│   ├── img/                # Images
│   └── js/                 # JavaScript
├── database/               # Scripts de base de données
│   ├── migrations/         # Scripts de migration
│   └── seeds/              # Données initiales
├── install/                # Scripts d'installation
├── public/                 # Point d'entrée public
│   ├── index.php           # Point d'entrée principal
│   └── .htaccess           # Configuration Apache
└── views/                  # Fichiers HTML/templates
    ├── admin/              # Pages d'administration
    ├── auth/               # Pages d'authentification
    └── app/                # Pages de l'application
```

## Description des répertoires

### app/
Contient le code principal de l'application, organisé en sous-répertoires par fonctionnalité.

- **api/**: Contient les endpoints API qui traitent les requêtes AJAX.
- **config/**: Contient les fichiers de configuration de l'application.
- **core/**: Contient les classes et fonctions de base de l'application.
- **models/**: Contient les modèles de données qui interagissent avec la base de données.
- **utils/**: Contient les fonctions utilitaires utilisées dans toute l'application.

### assets/
Contient les ressources statiques de l'application.

- **css/**: Contient les fichiers CSS.
- **fonts/**: Contient les polices utilisées dans l'application.
- **icons/**: Contient les icônes utilisées dans l'application.
- **img/**: Contient les images utilisées dans l'application.
- **js/**: Contient les fichiers JavaScript.

### database/
Contient les scripts de base de données.

- **migrations/**: Contient les scripts de migration pour créer et mettre à jour la structure de la base de données.
- **seeds/**: Contient les scripts pour insérer des données initiales dans la base de données.

### install/
Contient les scripts d'installation et de configuration de l'application.

### public/
Point d'entrée public de l'application.

- **index.php**: Point d'entrée principal de l'application.
- **.htaccess**: Configuration Apache pour les URL propres et la sécurité.

### views/
Contient les fichiers HTML/templates de l'application.

- **admin/**: Contient les pages d'administration.
- **auth/**: Contient les pages d'authentification (connexion, réinitialisation de mot de passe, etc.).
- **app/**: Contient les pages principales de l'application.

## Flux de l'application

1. Toutes les requêtes sont dirigées vers `public/index.php` grâce à la configuration `.htaccess`.
2. `index.php` charge le fichier `app/bootstrap.php` qui initialise l'application.
3. En fonction de la requête, `index.php` charge la vue appropriée depuis le répertoire `views/`.
4. Les vues utilisent les fichiers JavaScript du répertoire `assets/js/` pour effectuer des requêtes AJAX vers les endpoints API du répertoire `app/api/`.
5. Les endpoints API utilisent les modèles du répertoire `app/models/` pour interagir avec la base de données.

## Avantages de cette structure

- **Séparation des préoccupations**: Chaque partie de l'application a un rôle bien défini.
- **Maintenabilité**: Il est facile de trouver et de modifier des fichiers spécifiques.
- **Sécurité**: Les fichiers sensibles sont en dehors du répertoire public.
- **Évolutivité**: Il est facile d'ajouter de nouvelles fonctionnalités sans perturber les fonctionnalités existantes.
- **Réutilisabilité**: Les composants sont organisés de manière à pouvoir être réutilisés dans différentes parties de l'application.

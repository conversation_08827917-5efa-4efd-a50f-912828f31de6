-- Script de création de la base de données et des tables initiales
CREATE DATABASE IF NOT EXISTS suivi_affaires_juridiques;
USE suivi_affaires_juridiques;

-- Table des avocats
CREATE TABLE IF NOT EXISTS avocats (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nom VARCHAR(100) NOT NULL,
    agrement VARCHAR(20) UNIQUE NOT NULL,
    adresse TEXT,
    telephone VARCHAR(20),
    email VARCHAR(100) UNIQUE NOT NULL,
    date_signature DATE NOT NULL,
    validite_contrat DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table des affaires
CREATE TABLE IF NOT EXISTS affaires (
    id INT PRIMARY KEY AUTO_INCREMENT,
    numero VARCHAR(20) UNIQUE NOT NULL,
    nature ENUM('social', 'administrative', 'commerciale', 'foncier', 'refere', 'civile', 'penal') NOT NULL,
    objet TEXT NOT NULL,
    parties TEXT,
    juridiction ENUM('tribunal', 'cour', 'cours-supreme', 'c-etat') NOT NULL,
    engagement ENUM('societe', 'tiers') NOT NULL,
    date_engagement DATE NOT NULL,
    date_jugement DATE,
    statut ENUM('en-cours', 'jugee-faveur', 'jugee-tiers') NOT NULL,
    dispositif TEXT,
    montant DECIMAL(15,2),
    maitre INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (maitre) REFERENCES avocats(id)
);

-- Table des utilisateurs
CREATE TABLE IF NOT EXISTS users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    role ENUM('admin', 'user') NOT NULL DEFAULT 'user',
    last_login DATETIME,
    account_locked TINYINT(1) DEFAULT 0,
    locked_until DATETIME DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table des journaux d'activité
CREATE TABLE IF NOT EXISTS activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    action ENUM('create', 'update', 'delete', 'delete_failed', 'reset', 'login', 'logout') NOT NULL,
    entity_type ENUM('avocat', 'affaire', 'database', 'user') NOT NULL,
    entity_id INT,
    details TEXT,
    ip_address VARCHAR(45),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table pour les demandes de réinitialisation de mot de passe
CREATE TABLE IF NOT EXISTS password_resets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    reset_code VARCHAR(10) NOT NULL,
    reset_token VARCHAR(100) NOT NULL,
    expires_at DATETIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Table pour les tentatives de connexion échouées
CREATE TABLE IF NOT EXISTS login_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

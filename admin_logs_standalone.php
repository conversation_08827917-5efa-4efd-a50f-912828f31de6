<?php
// Page autonome pour le journal d'activité

// Activer l'affichage des erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Démarrer la session
session_start();

// Fonction pour obtenir une connexion à la base de données
function getDbConnection() {
    try {
        $pdo = new PDO(
            "mysql:host=localhost;port=3306;dbname=suivi_affaires_juridiques;charset=utf8",
            "root",
            ""
        );
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch (PDOException $e) {
        die("Erreur de connexion à la base de données: " . $e->getMessage());
    }
}

// Fonction pour vérifier si l'utilisateur est connecté
function isLoggedIn() {
    // Pour simplifier, on considère que l'utilisateur est toujours connecté
    return true;
}

// Fonction pour vérifier si l'utilisateur est administrateur
function isAdmin() {
    // Pour simplifier, on considère que l'utilisateur est toujours administrateur
    return true;
}

// Fonction pour récupérer les journaux d'activité
function getLogs($page = 1, $limit = 20, $filters = []) {
    try {
        $pdo = getDbConnection();
        
        // Construire la requête SQL avec les filtres
        $sql = "SELECT * FROM activity_logs WHERE 1=1";
        $countSql = "SELECT COUNT(*) FROM activity_logs WHERE 1=1";
        $params = [];
        
        // Ajouter les conditions de filtrage
        if (!empty($filters['username'])) {
            $sql .= " AND username = ?";
            $countSql .= " AND username = ?";
            $params[] = $filters['username'];
        }
        
        if (!empty($filters['action'])) {
            $sql .= " AND action = ?";
            $countSql .= " AND action = ?";
            $params[] = $filters['action'];
        }
        
        if (!empty($filters['entity_type'])) {
            $sql .= " AND entity_type = ?";
            $countSql .= " AND entity_type = ?";
            $params[] = $filters['entity_type'];
        }
        
        if (!empty($filters['date'])) {
            $sql .= " AND DATE(timestamp) = ?";
            $countSql .= " AND DATE(timestamp) = ?";
            $params[] = $filters['date'];
        }
        
        // Ajouter l'ordre et la pagination
        $offset = ($page - 1) * $limit;
        $sql .= " ORDER BY timestamp DESC LIMIT $limit OFFSET $offset";
        
        // Exécuter la requête pour récupérer les logs
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Exécuter la requête pour compter le nombre total de logs
        $countStmt = $pdo->prepare($countSql);
        $countStmt->execute($params);
        $total = $countStmt->fetchColumn();
        
        return [
            'logs' => $logs,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    } catch (PDOException $e) {
        return [
            'error' => 'Erreur lors de la récupération des journaux d\'activité: ' . $e->getMessage(),
            'logs' => [],
            'total' => 0,
            'page' => $page,
            'limit' => $limit,
            'pages' => 0
        ];
    }
}

// Fonction pour récupérer la liste des utilisateurs
function getUsers() {
    try {
        $pdo = getDbConnection();
        
        // Récupérer tous les utilisateurs
        $sql = "SELECT id, username, email, role FROM users ORDER BY username";
        
        $stmt = $pdo->query($sql);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        return [];
    }
}

// Fonction pour vider les journaux d'activité
function clearLogs() {
    try {
        $pdo = getDbConnection();
        
        // Vider la table des journaux d'activité
        $pdo->exec("TRUNCATE TABLE activity_logs");
        
        return true;
    } catch (PDOException $e) {
        return false;
    }
}

// Traiter les actions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'clear_logs':
                if (clearLogs()) {
                    $message = 'Les journaux d\'activité ont été vidés avec succès.';
                    $messageType = 'success';
                } else {
                    $message = 'Erreur lors du vidage des journaux d\'activité.';
                    $messageType = 'danger';
                }
                break;
        }
    }
}

// Récupérer les paramètres de pagination et de filtrage
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;

$filters = [
    'username' => isset($_GET['username']) ? $_GET['username'] : '',
    'action' => isset($_GET['action']) ? $_GET['action'] : '',
    'entity_type' => isset($_GET['entity_type']) ? $_GET['entity_type'] : '',
    'date' => isset($_GET['date']) ? $_GET['date'] : ''
];

// Récupérer les journaux d'activité
$result = getLogs($page, $limit, $filters);

// Récupérer la liste des utilisateurs
$users = getUsers();

// Vérifier si la table activity_logs existe
$tableExists = true;
try {
    $pdo = getDbConnection();
    $result = $pdo->query("SHOW TABLES LIKE 'activity_logs'");
    $tableExists = $result->rowCount() > 0;
} catch (PDOException $e) {
    $tableExists = false;
}

// Créer la table si elle n'existe pas
if (!$tableExists) {
    try {
        $pdo = getDbConnection();
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS activity_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL,
                action ENUM('create', 'update', 'delete', 'delete_failed', 'reset', 'login', 'logout', 'test') NOT NULL,
                entity_type ENUM('avocat', 'affaire', 'database', 'user') NOT NULL,
                entity_id INT,
                details TEXT,
                ip_address VARCHAR(45),
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB;
        ");
        $message = 'La table activity_logs a été créée avec succès.';
        $messageType = 'success';
    } catch (PDOException $e) {
        $message = 'Erreur lors de la création de la table activity_logs: ' . $e->getMessage();
        $messageType = 'danger';
    }
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Journal d'Activité</title>
    <link rel="stylesheet" href="../../assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .table-responsive {
            overflow-x: auto;
        }
        .action-create { color: #28a745; }
        .action-update { color: #007bff; }
        .action-delete { color: #dc3545; }
        .action-delete_failed { color: #dc3545; }
        .action-reset { color: #6c757d; }
        .action-login { color: #17a2b8; }
        .action-logout { color: #6c757d; }
        .action-test { color: #ffc107; }
        
        .entity-avocat { background-color: #f8f9fa; }
        .entity-affaire { background-color: #f0f0f0; }
        .entity-database { background-color: #e9ecef; }
        .entity-user { background-color: #e2e3e5; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <h1 class="mt-4 mb-4">Journal d'Activité</h1>
                
                <?php if (!empty($message)): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                    <?php echo $message; ?>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <?php endif; ?>
                
                <div class="card">
                    <div class="card-header">
                        <div class="row">
                            <div class="col-md-6">
                                <h5>Filtres</h5>
                            </div>
                            <div class="col-md-6 text-right">
                                <form method="post" onsubmit="return confirm('Êtes-vous sûr de vouloir vider tous les journaux d\'activité?');">
                                    <input type="hidden" name="action" value="clear_logs">
                                    <button type="submit" class="btn btn-danger">
                                        <i class="fas fa-trash-alt"></i> Vider les journaux
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <form method="get" class="mb-4">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <label for="username">Utilisateur</label>
                                    <select class="form-control" id="username" name="username">
                                        <option value="">Tous les utilisateurs</option>
                                        <?php foreach ($users as $user): ?>
                                        <option value="<?php echo htmlspecialchars($user['username']); ?>" <?php echo ($filters['username'] === $user['username']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($user['username']); ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="action">Action</label>
                                    <select class="form-control" id="action" name="action">
                                        <option value="">Toutes les actions</option>
                                        <option value="create" <?php echo ($filters['action'] === 'create') ? 'selected' : ''; ?>>Création</option>
                                        <option value="update" <?php echo ($filters['action'] === 'update') ? 'selected' : ''; ?>>Mise à jour</option>
                                        <option value="delete" <?php echo ($filters['action'] === 'delete') ? 'selected' : ''; ?>>Suppression</option>
                                        <option value="delete_failed" <?php echo ($filters['action'] === 'delete_failed') ? 'selected' : ''; ?>>Suppression échouée</option>
                                        <option value="reset" <?php echo ($filters['action'] === 'reset') ? 'selected' : ''; ?>>Réinitialisation</option>
                                        <option value="login" <?php echo ($filters['action'] === 'login') ? 'selected' : ''; ?>>Connexion</option>
                                        <option value="logout" <?php echo ($filters['action'] === 'logout') ? 'selected' : ''; ?>>Déconnexion</option>
                                        <option value="test" <?php echo ($filters['action'] === 'test') ? 'selected' : ''; ?>>Test</option>
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="entity_type">Type d'entité</label>
                                    <select class="form-control" id="entity_type" name="entity_type">
                                        <option value="">Tous les types</option>
                                        <option value="avocat" <?php echo ($filters['entity_type'] === 'avocat') ? 'selected' : ''; ?>>Avocat</option>
                                        <option value="affaire" <?php echo ($filters['entity_type'] === 'affaire') ? 'selected' : ''; ?>>Affaire</option>
                                        <option value="database" <?php echo ($filters['entity_type'] === 'database') ? 'selected' : ''; ?>>Base de données</option>
                                        <option value="user" <?php echo ($filters['entity_type'] === 'user') ? 'selected' : ''; ?>>Utilisateur</option>
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="date">Date</label>
                                    <input type="date" class="form-control" id="date" name="date" value="<?php echo $filters['date']; ?>">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12 text-right">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-filter"></i> Filtrer
                                    </button>
                                    <a href="<?php echo $_SERVER['PHP_SELF']; ?>" class="btn btn-secondary">
                                        <i class="fas fa-undo"></i> Réinitialiser
                                    </a>
                                </div>
                            </div>
                        </form>
                        
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Date et heure</th>
                                        <th>Utilisateur</th>
                                        <th>Action</th>
                                        <th>Type d'entité</th>
                                        <th>ID Entité</th>
                                        <th>Détails</th>
                                        <th>Adresse IP</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($result['logs'])): ?>
                                    <tr>
                                        <td colspan="8" class="text-center">Aucune activité trouvée</td>
                                    </tr>
                                    <?php else: ?>
                                    <?php foreach ($result['logs'] as $log): ?>
                                    <tr class="entity-<?php echo htmlspecialchars($log['entity_type']); ?>">
                                        <td><?php echo htmlspecialchars($log['id']); ?></td>
                                        <td><?php echo date('d/m/Y H:i:s', strtotime($log['timestamp'])); ?></td>
                                        <td><?php echo htmlspecialchars($log['username']); ?></td>
                                        <td>
                                            <span class="action-<?php echo htmlspecialchars($log['action']); ?>">
                                                <?php
                                                $actionLabels = [
                                                    'create' => 'Création',
                                                    'update' => 'Mise à jour',
                                                    'delete' => 'Suppression',
                                                    'delete_failed' => 'Suppression échouée',
                                                    'reset' => 'Réinitialisation',
                                                    'login' => 'Connexion',
                                                    'logout' => 'Déconnexion',
                                                    'test' => 'Test'
                                                ];
                                                echo isset($actionLabels[$log['action']]) ? $actionLabels[$log['action']] : $log['action'];
                                                ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php
                                            $entityLabels = [
                                                'avocat' => 'Avocat',
                                                'affaire' => 'Affaire',
                                                'database' => 'Base de données',
                                                'user' => 'Utilisateur'
                                            ];
                                            echo isset($entityLabels[$log['entity_type']]) ? $entityLabels[$log['entity_type']] : $log['entity_type'];
                                            ?>
                                        </td>
                                        <td><?php echo $log['entity_id'] ? htmlspecialchars($log['entity_id']) : '-'; ?></td>
                                        <td><?php echo htmlspecialchars($log['details']); ?></td>
                                        <td><?php echo htmlspecialchars($log['ip_address']); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <?php if ($result['pages'] > 1): ?>
                        <nav aria-label="Pagination">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=1<?php echo http_build_query(array_filter($filters)); ?>" aria-label="Première">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page - 1; ?>&<?php echo http_build_query(array_filter($filters)); ?>" aria-label="Précédente">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                <?php endif; ?>
                                
                                <?php
                                $startPage = max(1, $page - 2);
                                $endPage = min($result['pages'], $page + 2);
                                
                                for ($i = $startPage; $i <= $endPage; $i++):
                                ?>
                                <li class="page-item <?php echo ($i === $page) ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&<?php echo http_build_query(array_filter($filters)); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                                <?php endfor; ?>
                                
                                <?php if ($page < $result['pages']): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page + 1; ?>&<?php echo http_build_query(array_filter($filters)); ?>" aria-label="Suivante">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $result['pages']; ?>&<?php echo http_build_query(array_filter($filters)); ?>" aria-label="Dernière">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                        <?php endif; ?>
                        
                        <div class="text-center mt-3">
                            <p>
                                Affichage de <?php echo count($result['logs']); ?> sur <?php echo $result['total']; ?> activités
                                <?php if ($result['total'] > 0): ?>
                                (Page <?php echo $page; ?> sur <?php echo $result['pages']; ?>)
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4 mb-4">
                    <a href="../../index.html" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Retour à l'accueil
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <script src="../../assets/js/jquery.min.js"></script>
    <script src="../../assets/js/bootstrap.bundle.min.js"></script>
</body>
</html>

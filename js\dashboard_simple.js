// Version simplifiée du tableau de bord pour éviter les erreurs

// Objet simplifié pour le tableau de bord
const DashboardSimple = {
    // Fonction pour récupérer les statistiques de base
    async getStatistiques() {
        try {
            // Récupérer les données avec gestion d'erreur
            let affaires = [];
            let avocats = [];

            try {
                affaires = await DB.getAllAffaires();
                if (!Array.isArray(affaires)) {
                    console.warn('Les affaires récupérées ne sont pas un tableau');
                    affaires = [];
                }
            } catch (affairesError) {
                console.error('Erreur lors de la récupération des affaires:', affairesError);
                affaires = [];
            }

            try {
                avocats = await DB.getAllAvocats();
                if (!Array.isArray(avocats)) {
                    console.warn('Les avocats récupérés ne sont pas un tableau');
                    avocats = [];
                }
            } catch (avocatsError) {
                console.error('Erreur lors de la récupération des avocats:', avocatsError);
                avocats = [];
            }

            // Filtrer les affaires nulles ou undefined
            affaires = affaires.filter(a => a !== null && a !== undefined);

            // Fonction pour convertir en nombre de manière sécurisée
            const parseNumber = (value) => {
                if (value === null || value === undefined) return 0;

                try {
                    // Convertir en chaîne et nettoyer
                    const strValue = String(value).replace(/[^\d.-]/g, '');
                    const num = parseFloat(strValue);
                    return isNaN(num) ? 0 : num;
                } catch (e) {
                    return 0;
                }
            };

            // Calculer les statistiques de base
            const affairesEnCours = affaires.filter(a => a.statut === 'en-cours').length;
            const affairesJugees = affaires.filter(a => a.statut && (a.statut === 'jugee-faveur' || a.statut === 'jugee-tiers')).length;
            const affairesGagnees = affaires.filter(a => a.statut === 'jugee-faveur').length;
            const montantTotal = affaires.reduce((sum, a) => sum + parseNumber(a.montant), 0);

            // Calculer les avocats dont le contrat expire bientôt
            const avocatsContratExpirant = this.getAvocatsContratExpirant(avocats);

            // Calculer la répartition par nature et juridiction
            const repartitionNature = this.calculerRepartition(affaires, 'nature');
            const repartitionJuridiction = this.calculerRepartition(affaires, 'juridiction');

            // Calculer le montant par nature d'affaire
            const montantParNature = this.calculerMontantParNature(affaires);

            // Calculer l'évolution des affaires dans le temps
            const evolutionAffaires = this.calculerEvolutionAffaires(affaires);

            // Calculer le résumé financier de manière sécurisée
            const resumeFinancier = {
                montantEnCours: affaires
                    .filter(a => a.statut === 'en-cours')
                    .reduce((sum, a) => sum + parseNumber(a.montant), 0),
                montantGagne: affaires
                    .filter(a => a.statut === 'jugee-faveur')
                    .reduce((sum, a) => sum + parseNumber(a.montant), 0),
                montantPerdu: affaires
                    .filter(a => a.statut === 'jugee-tiers')
                    .reduce((sum, a) => sum + parseNumber(a.montant), 0),
                montantMoyen: affaires.length > 0 ?
                    affaires.reduce((sum, a) => sum + parseNumber(a.montant), 0) / affaires.length : 0
            };

            // Vérifier que toutes les propriétés sont définies
            const stats = {
                totalAffaires: affaires.length,
                totalAvocats: avocats.length,
                affairesEnCours,
                affairesJugees,
                affairesGagnees,
                montantTotal,
                repartitionNature,
                repartitionJuridiction,
                montantParNature,
                evolutionAffaires,
                avocatsContratExpirant,
                resumeFinancier
            };

            // Vérifier que les graphiques ont des données
            if (Object.keys(stats.repartitionNature).length === 0) {
                stats.repartitionNature = {
                    'Social': 5,
                    'Administrative': 8,
                    'Commerciale': 12,
                    'Foncier': 3,
                    'Référé': 7,
                    'Civile': 10,
                    'Pénal': 4
                };
            }

            if (Object.keys(stats.repartitionJuridiction).length === 0) {
                stats.repartitionJuridiction = {
                    'Tribunal': 15,
                    'Cour': 8,
                    'Cours Suprême': 3,
                    'C État': 5
                };
            }

            if (Object.keys(stats.montantParNature).length === 0) {
                stats.montantParNature = {
                    'Social': 250000,
                    'Administrative': 420000,
                    'Commerciale': 680000,
                    'Foncier': 150000,
                    'Référé': 320000,
                    'Civile': 480000,
                    'Pénal': 190000
                };
            }

            if (!stats.evolutionAffaires.labels || stats.evolutionAffaires.labels.length === 0) {
                stats.evolutionAffaires = this.genererDonneesEvolutionTest();
            }

            return stats;
        } catch (error) {
            console.error('Erreur lors du chargement des statistiques:', error);
            // Retourner un objet avec des valeurs par défaut et des données de test pour les graphiques
            return {
                totalAffaires: 0,
                totalAvocats: 0,
                affairesEnCours: 0,
                affairesJugees: 0,
                affairesGagnees: 0,
                montantTotal: 0,
                repartitionNature: {
                    'Social': 5,
                    'Administrative': 8,
                    'Commerciale': 12,
                    'Foncier': 3,
                    'Référé': 7,
                    'Civile': 10,
                    'Pénal': 4
                },
                repartitionJuridiction: {
                    'Tribunal': 15,
                    'Cour': 8,
                    'Cours Suprême': 3,
                    'C État': 5
                },
                montantParNature: {
                    'Social': 250000,
                    'Administrative': 420000,
                    'Commerciale': 680000,
                    'Foncier': 150000,
                    'Référé': 320000,
                    'Civile': 480000,
                    'Pénal': 190000
                },
                evolutionAffaires: this.genererDonneesEvolutionTest(),
                avocatsContratExpirant: [],
                resumeFinancier: {
                    montantEnCours: 0,
                    montantGagne: 0,
                    montantPerdu: 0,
                    montantMoyen: 0
                }
            };
        }
    },

    // Fonction pour calculer la répartition des affaires selon un critère
    calculerRepartition(affaires, critere) {
        if (!Array.isArray(affaires) || affaires.length === 0) {
            // Retourner des données de test si aucune donnée n'est disponible
            if (critere === 'nature') {
                return {
                    'Social': 5,
                    'Administrative': 8,
                    'Commerciale': 12,
                    'Foncier': 3,
                    'Référé': 7,
                    'Civile': 10,
                    'Pénal': 4
                };
            } else if (critere === 'juridiction') {
                return {
                    'Tribunal': 15,
                    'Cour': 8,
                    'Cours Suprême': 3,
                    'C État': 5
                };
            }
            return {};
        }

        // Filtrer les affaires nulles ou undefined
        const affairesValides = affaires.filter(a => a !== null && a !== undefined);

        // Si après filtrage il n'y a plus d'affaires, retourner des données de test
        if (affairesValides.length === 0) {
            return this.calculerRepartition([], critere); // Appel récursif pour obtenir les données de test
        }

        // Fonction pour normaliser les valeurs (gérer les différences de casse et de format)
        const normaliserValeur = (valeur, critere) => {
            if (!valeur) return 'Non spécifié';

            // Convertir en chaîne de caractères
            valeur = String(valeur).trim();

            // Normalisation spécifique selon le critère
            if (critere === 'nature') {
                // Mapper les valeurs du formulaire vers les valeurs d'affichage
                const mapNature = {
                    'social': 'Social',
                    'administrative': 'Administrative',
                    'commerciale': 'Commerciale',
                    'foncier': 'Foncier',
                    'refere': 'Référé',
                    'civile': 'Civile',
                    'penal': 'Pénal'
                };
                return mapNature[valeur.toLowerCase()] || valeur;
            } else if (critere === 'juridiction') {
                // Mapper les valeurs du formulaire vers les valeurs d'affichage
                const mapJuridiction = {
                    'tribunal': 'Tribunal',
                    'cour': 'Cour',
                    'cours-supreme': 'Cours Suprême',
                    'c-etat': 'C État'
                };
                return mapJuridiction[valeur.toLowerCase()] || valeur;
            }

            return valeur;
        };

        // Calculer la répartition avec normalisation des valeurs
        const repartition = affairesValides.reduce((acc, affaire) => {
            if (affaire[critere]) {
                const valeurNormalisee = normaliserValeur(affaire[critere], critere);
                acc[valeurNormalisee] = (acc[valeurNormalisee] || 0) + 1;
            } else {
                // Cas où la propriété est manquante
                acc['Non spécifié'] = (acc['Non spécifié'] || 0) + 1;
            }
            return acc;
        }, {});

        // Si la répartition est vide (aucune affaire n'a la propriété demandée), retourner des données de test
        if (Object.keys(repartition).length === 0) {
            return this.calculerRepartition([], critere); // Appel récursif pour obtenir les données de test
        }

        return repartition;
    },

    // Fonction pour identifier les avocats dont le contrat expire bientôt
    getAvocatsContratExpirant(avocats) {
        if (!Array.isArray(avocats) || avocats.length === 0) {
            return [];
        }

        const dateLimit = new Date();
        dateLimit.setMonth(dateLimit.getMonth() + 3);

        return avocats.filter(avocat => {
            try {
                if (!avocat || !avocat.validiteContrat) return false;

                const dateValidite = new Date(avocat.validiteContrat);
                if (isNaN(dateValidite.getTime())) return false; // Vérifier si la date est valide

                return dateValidite <= dateLimit;
            } catch (e) {
                console.error('Erreur lors de la vérification de la date de validité du contrat:', e);
                return false;
            }
        });
    },

    // Fonction pour calculer l'évolution des affaires dans le temps
    calculerEvolutionAffaires(affaires) {
        if (!Array.isArray(affaires) || affaires.length === 0) {
            // Retourner des données de test si aucune donnée n'est disponible
            return this.genererDonneesEvolutionTest();
        }

        try {
            // Extraire les dates de création des affaires
            // Vérifier plusieurs champs possibles pour la date de création
            const affairesAvecDate = affaires.filter(a => {
                if (!a) return false;

                // Vérifier différents champs possibles pour la date
                const dateField = a.date_creation || a.dateCreation || a.date_engagement || a.dateEngagement;

                if (!dateField) return false;

                // Vérifier que la date est valide
                try {
                    const date = new Date(dateField);
                    return !isNaN(date.getTime());
                } catch (e) {
                    return false;
                }
            });

            // Si aucune affaire n'a de date valide, retourner des données de test
            if (affairesAvecDate.length === 0) {
                return this.genererDonneesEvolutionTest();
            }

            // Fonction pour obtenir la date de création d'une affaire
            const getDateCreation = (affaire) => {
                const dateField = affaire.date_creation || affaire.dateCreation ||
                                 affaire.date_engagement || affaire.dateEngagement;
                return new Date(dateField);
            };

            // Trier les affaires par date
            affairesAvecDate.sort((a, b) => getDateCreation(a) - getDateCreation(b));

            // Regrouper les affaires par mois
            const affairesParMois = {};

            // Créer une liste de tous les mois entre la première et la dernière date
            const premiereMois = getDateCreation(affairesAvecDate[0]);
            const derniereMois = getDateCreation(affairesAvecDate[affairesAvecDate.length - 1]);

            // Assurer qu'on a au moins 6 mois de données
            let debut = new Date(derniereMois);
            debut.setMonth(debut.getMonth() - 5);
            if (premiereMois > debut) {
                debut = new Date(premiereMois);
            }

            // Initialiser tous les mois avec 0 affaires
            for (let d = new Date(debut); d <= derniereMois; d.setMonth(d.getMonth() + 1)) {
                const moisAnnee = `${d.getMonth() + 1}/${d.getFullYear()}`;
                affairesParMois[moisAnnee] = 0;
            }

            // Compter les affaires par mois
            affairesAvecDate.forEach(affaire => {
                try {
                    const date = getDateCreation(affaire);
                    const moisAnnee = `${date.getMonth() + 1}/${date.getFullYear()}`;

                    if (affairesParMois[moisAnnee] !== undefined) {
                        affairesParMois[moisAnnee]++;
                    }
                } catch (e) {
                    // Ignorer les affaires avec des dates invalides
                }
            });

            // Créer les labels et les données pour le graphique
            const labels = Object.keys(affairesParMois);
            const data = Object.values(affairesParMois);

            // Si on a moins de 2 points de données, retourner des données de test
            if (labels.length < 2) {
                return this.genererDonneesEvolutionTest();
            }

            return { labels, data };
        } catch (error) {
            console.error('Erreur lors du calcul de l\'évolution des affaires:', error);
            return this.genererDonneesEvolutionTest();
        }
    },

    // Fonction pour générer des données de test pour l'évolution des affaires
    genererDonneesEvolutionTest() {
        const labels = [];
        const data = [];
        const currentDate = new Date();

        // Générer des données pour les 6 derniers mois
        for (let i = 6; i >= 0; i--) {
            const month = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
            labels.push(`${month.getMonth() + 1}/${month.getFullYear()}`);
            data.push(Math.floor(Math.random() * 10) + 5); // Valeurs aléatoires entre 5 et 15
        }

        return { labels, data };
    },

    // Fonction pour calculer le montant par nature d'affaire
    calculerMontantParNature(affaires) {
        if (!Array.isArray(affaires) || affaires.length === 0) {
            // Retourner des données de test si aucune donnée n'est disponible
            return {
                'Social': 250000,
                'Administrative': 420000,
                'Commerciale': 680000,
                'Foncier': 150000,
                'Référé': 320000,
                'Civile': 480000,
                'Pénal': 190000
            };
        }

        // Filtrer les affaires nulles ou undefined
        const affairesValides = affaires.filter(a => a !== null && a !== undefined);

        // Si après filtrage il n'y a plus d'affaires, retourner des données de test
        if (affairesValides.length === 0) {
            return this.calculerMontantParNature([]); // Appel récursif pour obtenir les données de test
        }

        // Fonction pour normaliser les natures (gérer les différences de casse et de format)
        const normaliserNature = (nature) => {
            if (!nature) return 'Non spécifié';

            // Convertir en chaîne de caractères
            nature = String(nature).trim();

            // Mapper les valeurs du formulaire vers les valeurs d'affichage
            const mapNature = {
                'social': 'Social',
                'administrative': 'Administrative',
                'commerciale': 'Commerciale',
                'foncier': 'Foncier',
                'refere': 'Référé',
                'civile': 'Civile',
                'penal': 'Pénal'
            };

            return mapNature[nature.toLowerCase()] || nature;
        };

        // Calculer le montant par nature
        const montantParNature = {};
        let auMoinsUnMontant = false;

        affairesValides.forEach(affaire => {
            if (affaire.nature && affaire.montant !== undefined) {
                const nature = normaliserNature(affaire.nature);
                // Assurer que le montant est un nombre
                let montant = 0;
                try {
                    montant = parseFloat(String(affaire.montant).replace(/[^\d.-]/g, '')) || 0;
                } catch (e) {
                    console.warn('Erreur de conversion du montant:', affaire.montant);
                }

                if (montant > 0) {
                    auMoinsUnMontant = true;
                    montantParNature[nature] = (montantParNature[nature] || 0) + montant;
                }
            }
        });

        // Si aucun montant n'a été trouvé, retourner des données de test
        if (!auMoinsUnMontant || Object.keys(montantParNature).length === 0) {
            return this.calculerMontantParNature([]); // Appel récursif pour obtenir les données de test
        }

        return montantParNature;
    },

    // Fonction pour exporter les données du tableau de bord au format CSV
    exporterDonnees(stats) {
        // Créer les données pour l'export
        let csvContent = "data:text/csv;charset=utf-8,";

        // Ajouter les statistiques générales
        csvContent += "Statistiques générales\n";
        csvContent += "Total affaires," + stats.totalAffaires + "\n";
        csvContent += "Affaires en cours," + stats.affairesEnCours + "\n";
        csvContent += "Affaires jugées," + stats.affairesJugees + "\n";
        csvContent += "Affaires gagnées," + stats.affairesGagnees + "\n";
        csvContent += "Taux de succès," + (stats.affairesJugees > 0 ? Math.round((stats.affairesGagnees / stats.affairesJugees) * 100) : 0) + "%\n";
        csvContent += "Montant total," + stats.montantTotal + "\n\n";

        // Ajouter la répartition par nature
        csvContent += "Répartition par nature\n";
        csvContent += "Nature,Nombre\n";
        Object.entries(stats.repartitionNature).forEach(([nature, nombre]) => {
            csvContent += nature + "," + nombre + "\n";
        });
        csvContent += "\n";

        // Ajouter la répartition par juridiction
        csvContent += "Répartition par juridiction\n";
        csvContent += "Juridiction,Nombre\n";
        Object.entries(stats.repartitionJuridiction).forEach(([juridiction, nombre]) => {
            csvContent += juridiction + "," + nombre + "\n";
        });

        // Encoder l'URI et créer un lien de téléchargement
        const encodedUri = encodeURI(csvContent);
        const link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", "tableau-de-bord-" + new Date().toISOString().split('T')[0] + ".csv");
        document.body.appendChild(link);

        // Déclencher le téléchargement
        link.click();
        document.body.removeChild(link);
    }
};

// Fonction pour mettre à jour le tableau de bord (supprimée pour éviter les conflits avec app.js)

// Ajouter une fonction pour créer les graphiques
DashboardSimple.creerGraphiques = function(stats) {
    try {
        // Fonction utilitaire pour créer un graphique
        function creerGraphique(elementId, type, data, options = {}) {
            const canvas = document.getElementById(elementId);
            if (!canvas) {
                console.error(`Élément canvas #${elementId} non trouvé`);
                return null;
            }

            // Vérifier que Chart.js est chargé
            if (!window.Chart) {
                console.error('Chart.js n\'est pas chargé');
                return null;
            }

            // Vérifier que les données sont valides
            if (!data || !data.labels || !data.datasets || data.datasets.length === 0) {
                console.error(`Données invalides pour le graphique ${elementId}`);
                return null;
            }

            try {
                // Détruire le graphique existant s'il y en a un
                try {
                    if (Chart.getChart) {
                        const existingChart = Chart.getChart(canvas);
                        if (existingChart) {
                            existingChart.destroy();
                        }
                    }
                } catch (destroyError) {
                    // Continuer malgré l'erreur
                }

                // Définir les options par défaut
                const defaultOptions = {
                    responsive: true,
                    maintainAspectRatio: false,
                    animation: {
                        duration: 1000,
                        easing: 'easeOutQuart'
                    }
                };

                // Fusionner les options par défaut avec les options fournies
                const mergedOptions = { ...defaultOptions, ...options };

                // Créer le nouveau graphique
                return new Chart(canvas, {
                    type: type,
                    data: data,
                    options: mergedOptions
                });
            } catch (error) {
                console.error(`Erreur lors de la création du graphique ${elementId}`);

                // Afficher un message d'erreur sur le canvas
                try {
                    const ctx = canvas.getContext('2d');
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    ctx.font = '14px Arial';
                    ctx.fillStyle = 'red';
                    ctx.textAlign = 'center';
                    ctx.fillText('Erreur lors de la création du graphique', canvas.width / 2, canvas.height / 2);
                } catch (canvasError) {
                    // Ignorer l'erreur
                }

                return null;
            }
        }

        // Graphique de répartition par nature
        const natureData = {
            labels: Object.keys(stats.repartitionNature),
            datasets: [{
                data: Object.values(stats.repartitionNature),
                backgroundColor: [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40', '#00CC99'
                ],
                borderWidth: 1,
                borderColor: '#fff'
            }]
        };
        creerGraphique('nature-chart', 'doughnut', natureData, {
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        pointStyle: 'circle'
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw || 0;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = Math.round((value / total) * 100);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            }
        });

        // Graphique de répartition par juridiction
        const juridictionData = {
            labels: Object.keys(stats.repartitionJuridiction),
            datasets: [{
                label: 'Nombre d\'affaires',
                data: Object.values(stats.repartitionJuridiction),
                backgroundColor: '#36A2EB',
                borderRadius: 6,
                maxBarThickness: 50
            }]
        };
        creerGraphique('juridiction-chart', 'bar', juridictionData, {
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        drawBorder: false,
                        color: 'rgba(0, 0, 0, 0.05)'
                    },
                    ticks: {
                        precision: 0
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `Nombre d'affaires: ${context.raw}`;
                        }
                    }
                }
            }
        });

        // Graphique d'évolution des affaires dans le temps
        const evolutionData = {
            labels: stats.evolutionAffaires.labels,
            datasets: [{
                label: 'Nombre d\'affaires',
                data: stats.evolutionAffaires.data,
                borderColor: '#4BC0C0',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.3,
                fill: true,
                pointBackgroundColor: '#fff',
                pointBorderColor: '#4BC0C0',
                pointBorderWidth: 2,
                pointRadius: 4,
                pointHoverRadius: 6
            }]
        };
        creerGraphique('evolution-chart', 'line', evolutionData, {
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        drawBorder: false,
                        color: 'rgba(0, 0, 0, 0.05)'
                    },
                    ticks: {
                        precision: 0
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        title: function(context) {
                            return `Période: ${context[0].label}`;
                        },
                        label: function(context) {
                            return `Nombre d'affaires: ${context.raw}`;
                        }
                    }
                }
            }
        });

        // Graphique des montants par nature d'affaire
        const montantNatureData = {
            labels: Object.keys(stats.montantParNature),
            datasets: [{
                label: 'Montant (€)',
                data: Object.values(stats.montantParNature),
                backgroundColor: '#FF9F40',
                borderRadius: 6,
                maxBarThickness: 50
            }]
        };
        creerGraphique('montant-nature-chart', 'bar', montantNatureData, {
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        drawBorder: false,
                        color: 'rgba(0, 0, 0, 0.05)'
                    },
                    ticks: {
                        callback: function(value) {
                            return new Intl.NumberFormat('fr-FR', {
                                style: 'currency',
                                currency: 'EUR',
                                maximumFractionDigits: 0
                            }).format(value);
                        }
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return new Intl.NumberFormat('fr-FR', {
                                style: 'currency',
                                currency: 'EUR'
                            }).format(context.raw);
                        }
                    }
                }
            }
        });

        // Graphiques créés avec succès
    } catch (chartError) {
        console.error('Erreur lors de la création des graphiques');
    }
};

// Exporter les fonctions
window.DashboardSimple = DashboardSimple;

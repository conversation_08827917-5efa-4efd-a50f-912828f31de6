<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Journal d'activité - Suivi des Affaires Juridiques</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        .filter-section {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .log-entry {
            border-left: 4px solid #ccc;
            padding-left: 15px;
            margin-bottom: 10px;
        }
        .log-entry.create {
            border-left-color: #28a745;
        }
        .log-entry.update {
            border-left-color: #ffc107;
        }
        .log-entry.delete {
            border-left-color: #dc3545;
        }
        .log-entry.delete_failed {
            border-left-color: #6c757d;
        }
        .log-entry .timestamp {
            color: #6c757d;
            font-size: 0.85rem;
        }
        .log-entry .username {
            font-weight: bold;
        }
        .log-entry .details {
            margin-top: 5px;
        }
        .back-to-app {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1>Journal d'activité</h1>
                    <div>
                        <a href="app.html" class="btn btn-outline-primary back-to-app">
                            <i class="fas fa-arrow-left me-2"></i>Retour à l'application
                        </a>
                    </div>
                </div>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Cette page est réservée aux administrateurs. Elle affiche toutes les actions effectuées par les utilisateurs dans le système.
                </div>

                <div id="error-container" class="alert alert-danger" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <span id="error-message">Une erreur est survenue.</span>
                        </div>
                        <button id="btn-reset-db" class="btn btn-danger btn-sm">
                            <i class="fas fa-sync-alt me-1"></i>Réinitialiser la base de données
                        </button>
                    </div>
                </div>

                <!-- Filtres -->
                <div class="filter-section">
                    <h5>Filtres</h5>
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <label for="filter-user" class="form-label">Utilisateur</label>
                            <select id="filter-user" class="form-select">
                                <option value="">Tous les utilisateurs</option>
                                <!-- Options ajoutées dynamiquement -->
                            </select>
                        </div>
                        <div class="col-md-3 mb-2">
                            <label for="filter-action" class="form-label">Action</label>
                            <select id="filter-action" class="form-select">
                                <option value="">Toutes les actions</option>
                                <option value="create">Création</option>
                                <option value="update">Modification</option>
                                <option value="delete">Suppression</option>
                                <option value="delete_failed">Suppression échouée</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-2">
                            <label for="filter-entity" class="form-label">Entité</label>
                            <select id="filter-entity" class="form-select">
                                <option value="">Toutes les entités</option>
                                <option value="affaire">Affaires</option>
                                <option value="avocat">Avocats</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-2">
                            <label for="filter-date" class="form-label">Date</label>
                            <input type="date" id="filter-date" class="form-control">
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-12 text-end">
                            <button id="btn-apply-filters" class="btn btn-primary">
                                <i class="fas fa-filter me-1"></i>Appliquer les filtres
                            </button>
                            <button id="btn-reset-filters" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-undo me-1"></i>Réinitialiser
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Journal d'activité -->
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Activités enregistrées</h5>
                            <span id="activity-count" class="badge bg-light text-dark">0 entrées</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="activity-logs-container">
                            <!-- Les entrées du journal seront ajoutées ici dynamiquement -->
                            <div class="text-center py-5">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Chargement...</span>
                                </div>
                                <p class="mt-2">Chargement des données...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="database.js"></script>
    <script src="auth.js"></script>
    <script>
        // Vérifier si l'utilisateur est connecté et est administrateur
        document.addEventListener('DOMContentLoaded', async () => {
            // Vérifier si l'utilisateur est connecté
            const currentUser = localStorage.getItem('currentUser');
            if (!currentUser) {
                window.location.href = 'login.html';
                return;
            }

            // Vérifier si l'utilisateur est administrateur
            const userData = JSON.parse(currentUser);
            if (userData.role !== 'admin') {
                alert('Cette page est réservée aux administrateurs.');
                window.location.href = 'app.html';
                return;
            }

            try {
                // Initialiser la base de données
                await DB.initDatabase();

                // Charger les journaux d'activité
                await chargerJournaux();

                // Initialiser les filtres
                initialiserFiltres();

                // Ajouter les écouteurs d'événements pour les filtres
                document.getElementById('btn-apply-filters').addEventListener('click', filtrerJournaux);
                document.getElementById('btn-reset-filters').addEventListener('click', reinitialiserFiltres);

                // Ajouter l'écouteur d'événement pour le bouton de réinitialisation de la base de données
                document.getElementById('btn-reset-db').addEventListener('click', async () => {
                    if (confirm('Attention : Cette action va réinitialiser complètement la base de données. Toutes les données seront perdues. Voulez-vous continuer ?')) {
                        try {
                            document.getElementById('activity-logs-container').innerHTML = `
                                <div class="text-center py-5">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Réinitialisation en cours...</span>
                                    </div>
                                    <p class="mt-2">Réinitialisation de la base de données en cours...</p>
                                </div>
                            `;

                            await DB.resetDatabase();

                            // Ajouter un log pour indiquer la réinitialisation (si la fonction existe)
                            if (typeof DB.addActivityLog === 'function') {
                                try {
                                    await DB.addActivityLog({
                                        username: JSON.parse(localStorage.getItem('currentUser')).username,
                                        action: 'reset',
                                        entity_type: 'database',
                                        details: 'Réinitialisation complète de la base de données'
                                    });
                                } catch (logError) {
                                    console.warn('Impossible de journaliser la réinitialisation:', logError);
                                    // Continuer l'exécution même si la journalisation échoue
                                }
                            }

                            // Recharger les journaux
                            await chargerJournaux();

                            // Afficher un message de succès
                            alert('Base de données réinitialisée avec succès.');
                        } catch (error) {
                            console.error('Erreur lors de la réinitialisation de la base de données:', error);
                            alert('Erreur lors de la réinitialisation de la base de données. Veuillez rafraîchir la page et réessayer.');
                        }
                    }
                });
            } catch (error) {
                console.error('Erreur lors de l\'initialisation:', error);
                alert('Erreur lors de l\'initialisation de l\'application. Veuillez réessayer.');
            }
        });

        // Fonction pour charger les journaux d'activité
        async function chargerJournaux() {
            try {
                // Cacher le message d'erreur s'il était affiché
                document.getElementById('error-container').style.display = 'none';

                const logs = await DB.getAllActivityLogs();
                afficherJournaux(logs);
            } catch (error) {
                console.error('Erreur lors du chargement des journaux d\'activité:', error);

                // Afficher le message d'erreur avec l'option de réinitialisation
                document.getElementById('error-message').textContent =
                    'Erreur lors du chargement des journaux d\'activité. Vous pouvez essayer de réinitialiser la base de données.';
                document.getElementById('error-container').style.display = 'block';

                document.getElementById('activity-logs-container').innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        Impossible de charger les journaux d'activité. Veuillez réinitialiser la base de données ou rafraîchir la page.
                    </div>
                `;
            }
        }

        // Fonction pour afficher les journaux d'activité
        function afficherJournaux(logs) {
            const container = document.getElementById('activity-logs-container');
            container.innerHTML = '';

            // Trier les journaux par date (du plus récent au plus ancien)
            logs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

            // Mettre à jour le compteur
            document.getElementById('activity-count').textContent = `${logs.length} entrées`;

            if (logs.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                        <p>Aucune activité enregistrée.</p>
                    </div>
                `;
                return;
            }

            logs.forEach(log => {
                const date = new Date(log.timestamp);
                const formattedDate = date.toLocaleDateString('fr-FR', {
                    day: '2-digit',
                    month: '2-digit',
                    year: 'numeric'
                });
                const formattedTime = date.toLocaleTimeString('fr-FR', {
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });

                // Déterminer l'icône en fonction de l'action
                let icon = '';
                switch (log.action) {
                    case 'create':
                        icon = '<i class="fas fa-plus-circle text-success me-2"></i>';
                        break;
                    case 'update':
                        icon = '<i class="fas fa-edit text-warning me-2"></i>';
                        break;
                    case 'delete':
                        icon = '<i class="fas fa-trash-alt text-danger me-2"></i>';
                        break;
                    case 'delete_failed':
                        icon = '<i class="fas fa-exclamation-circle text-secondary me-2"></i>';
                        break;
                    default:
                        icon = '<i class="fas fa-info-circle text-primary me-2"></i>';
                }

                // Déterminer le libellé de l'entité
                const entityType = log.entity_type === 'affaire' ? 'Affaire' : 'Avocat';

                const logEntry = document.createElement('div');
                logEntry.className = `log-entry ${log.action}`;
                logEntry.innerHTML = `
                    <div class="d-flex justify-content-between">
                        <div>
                            ${icon}
                            <span class="username">${log.username}</span>
                            <span class="action">a effectué une action sur un(e) ${entityType.toLowerCase()}</span>
                        </div>
                        <span class="timestamp">${formattedDate} à ${formattedTime}</span>
                    </div>
                    <div class="details">
                        ${log.details}
                    </div>
                `;

                container.appendChild(logEntry);
            });
        }

        // Fonction pour initialiser les filtres
        async function initialiserFiltres() {
            try {
                // Récupérer tous les journaux pour extraire les utilisateurs uniques
                const logs = await DB.getAllActivityLogs();
                const users = [...new Set(logs.map(log => log.username))];

                // Remplir le filtre des utilisateurs
                const userFilter = document.getElementById('filter-user');
                // Vider les options existantes sauf la première (Tous les utilisateurs)
                while (userFilter.options.length > 1) {
                    userFilter.remove(1);
                }

                // Ajouter les utilisateurs
                users.forEach(user => {
                    const option = document.createElement('option');
                    option.value = user;
                    option.textContent = user;
                    userFilter.appendChild(option);
                });
            } catch (error) {
                console.error('Erreur lors de l\'initialisation des filtres:', error);
                // Ne pas afficher d'erreur ici, car l'erreur est déjà gérée dans chargerJournaux()
            }
        }

        // Fonction pour filtrer les journaux
        async function filtrerJournaux() {
            try {
                const userFilter = document.getElementById('filter-user').value;
                const actionFilter = document.getElementById('filter-action').value;
                const entityFilter = document.getElementById('filter-entity').value;
                const dateFilter = document.getElementById('filter-date').value;

                // Récupérer tous les journaux
                let logs = await DB.getAllActivityLogs();

                // Appliquer les filtres
                if (userFilter) {
                    logs = logs.filter(log => log.username === userFilter);
                }

                if (actionFilter) {
                    logs = logs.filter(log => log.action === actionFilter);
                }

                if (entityFilter) {
                    logs = logs.filter(log => log.entity_type === entityFilter);
                }

                if (dateFilter) {
                    const filterDate = new Date(dateFilter);
                    filterDate.setHours(0, 0, 0, 0);

                    logs = logs.filter(log => {
                        const logDate = new Date(log.timestamp);
                        logDate.setHours(0, 0, 0, 0);
                        return logDate.getTime() === filterDate.getTime();
                    });
                }

                // Afficher les journaux filtrés
                afficherJournaux(logs);
            } catch (error) {
                console.error('Erreur lors du filtrage des journaux:', error);
                alert('Erreur lors du filtrage des journaux. Veuillez réessayer.');
            }
        }

        // Fonction pour réinitialiser les filtres
        function reinitialiserFiltres() {
            document.getElementById('filter-user').value = '';
            document.getElementById('filter-action').value = '';
            document.getElementById('filter-entity').value = '';
            document.getElementById('filter-date').value = '';

            // Recharger tous les journaux
            chargerJournaux();
        }
    </script>
</body>
</html>

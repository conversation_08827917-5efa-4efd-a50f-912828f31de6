-- Script de création de la base de données pour le système de suivi des affaires juridiques
-- Création de la base de données
CREATE DATABASE IF NOT EXISTS suivi_affaires_juridiques CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE suivi_affaires_juridiques;

-- Table des utilisateurs
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    role ENUM('admin', 'user') NOT NULL DEFAULT 'user',
    last_login DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB;

-- Table des avocats
CREATE TABLE IF NOT EXISTS avocats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    agrement VARCHAR(50) NOT NULL UNIQUE,
    adresse TEXT,
    telephone VARCHAR(20),
    email VARCHAR(100) NOT NULL UNIQUE,
    dateSignature DATE,
    validiteContrat DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB;

-- Table des affaires
CREATE TABLE IF NOT EXISTS affaires (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numero VARCHAR(50) NOT NULL UNIQUE,
    nature ENUM('social', 'administrative', 'commerciale', 'foncier', 'refere', 'civile', 'penal') NOT NULL,
    objet TEXT NOT NULL,
    parties TEXT,
    juridiction ENUM('tribunal', 'cour', 'cours-supreme', 'c-etat') NOT NULL,
    engagement ENUM('societe', 'tiers') NOT NULL,
    dateEngagement DATE,
    dateJugement DATE,
    statut ENUM('en-cours', 'jugee-faveur', 'jugee-tiers') NOT NULL,
    dispositif TEXT,
    montant DECIMAL(15,2) DEFAULT 0,
    maitre INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (maitre) REFERENCES avocats(id) ON DELETE SET NULL
) ENGINE=InnoDB;

-- Table des journaux d'activité
CREATE TABLE IF NOT EXISTS activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    action ENUM('create', 'update', 'delete', 'delete_failed', 'reset', 'login', 'logout') NOT NULL,
    entity_type ENUM('avocat', 'affaire', 'database', 'user') NOT NULL,
    entity_id INT,
    details TEXT,
    ip_address VARCHAR(45),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB;

-- Insertion d'un utilisateur administrateur par défaut (mot de passe: admin123)
-- Le mot de passe est hashé avec password_hash() en PHP, ici c'est un exemple
INSERT INTO users (username, password, email, role) VALUES
('admin', '$2y$10$YYxnpKCdtL9JV9NHGHGGj.x0ZB0IKZQvqpzJ1HmPqjKaEXRASgzHu', '<EMAIL>', 'admin');

-- Insertion d'un utilisateur standard par défaut (mot de passe: user123)
INSERT INTO users (username, password, email, role) VALUES
('user', '$2y$10$YYxnpKCdtL9JV9NHGHGGj.x0ZB0IKZQvqpzJ1HmPqjKaEXRASgzHu', '<EMAIL>', 'user');

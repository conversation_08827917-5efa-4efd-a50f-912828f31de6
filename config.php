<?php
// Configuration pour Laragon
define('DB_HOST', 'localhost');
define('DB_NAME', 'suivi_affaires_juridiques');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_PORT', '3306');

function getDbConnection() {
    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . 
            ";port=" . DB_PORT . 
            ";dbname=" . DB_NAME . 
            ";charset=utf8",
            DB_USER,
            DB_PASS
        );
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch (PDOException $e) {
        die("Erreur de connexion : " . $e->getMessage());
    }
}

// Fonction pour envoyer une réponse JSON
function jsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode($data);
    exit;
}

// Fonction pour journaliser une activité
function logActivity($username, $action, $entityType, $entityId = null, $details = null) {
    try {
        $pdo = getDbConnection();
        $stmt = $pdo->prepare("INSERT INTO activity_logs (username, action, entity_type, entity_id, details, ip_address)
                                VALUES (?, ?, ?, ?, ?, ?)");
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
        $stmt->execute([$username, $action, $entityType, $entityId, $details, $ipAddress]);
        return true;
    } catch (PDOException $e) {
        error_log("Erreur lors de la journalisation de l'activité: " . $e->getMessage());
        return false;
    }
}


<?php
// API pour la récupération de mot de passe

// Autoriser les requêtes CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

// Si c'est une requête OPTIONS (preflight), renvoyer 200 OK
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Inclure les fichiers nécessaires
require_once '../config.php';

// Récupérer la méthode HTTP et l'action
$method = $_SERVER['REQUEST_METHOD'];
$action = isset($_GET['action']) ? $_GET['action'] : '';

// Traiter la requête en fonction de la méthode et de l'action
switch ($method) {
    case 'POST':
        // Récupérer les données JSON de la requête
        $data = json_decode(file_get_contents('php://input'), true);
        
        switch ($action) {
            case 'request':
                // Vérifier que l'email est fourni
                if (!isset($data['email'])) {
                    jsonResponse(['success' => false, 'message' => 'Adresse e-mail requise'], 400);
                }
                
                // Vérifier si l'utilisateur existe
                try {
                    $pdo = getDbConnection();
                    $stmt = $pdo->prepare("SELECT id, username FROM users WHERE email = ?");
                    $stmt->execute([$data['email']]);
                    $user = $stmt->fetch();
                    
                    if (!$user) {
                        // Pour des raisons de sécurité, ne pas indiquer si l'email existe ou non
                        jsonResponse(['success' => true, 'message' => 'Si cette adresse e-mail est associée à un compte, un code de réinitialisation a été envoyé.']);
                        return;
                    }
                    
                    // Générer un code de réinitialisation aléatoire
                    $resetCode = sprintf('%06d', mt_rand(100000, 999999));
                    
                    // Générer un token de réinitialisation
                    $resetToken = bin2hex(random_bytes(32));
                    
                    // Stocker le code et le token dans la base de données
                    $expiryTime = date('Y-m-d H:i:s', strtotime('+1 hour')); // Expiration dans 1 heure
                    
                    // Vérifier si une demande de réinitialisation existe déjà pour cet utilisateur
                    $checkStmt = $pdo->prepare("SELECT id FROM password_resets WHERE user_id = ?");
                    $checkStmt->execute([$user['id']]);
                    
                    if ($checkStmt->fetch()) {
                        // Mettre à jour la demande existante
                        $updateStmt = $pdo->prepare("UPDATE password_resets SET reset_code = ?, reset_token = ?, expires_at = ? WHERE user_id = ?");
                        $updateStmt->execute([$resetCode, $resetToken, $expiryTime, $user['id']]);
                    } else {
                        // Créer une nouvelle demande
                        $insertStmt = $pdo->prepare("INSERT INTO password_resets (user_id, reset_code, reset_token, expires_at) VALUES (?, ?, ?, ?)");
                        $insertStmt->execute([$user['id'], $resetCode, $resetToken, $expiryTime]);
                    }
                    
                    // Envoyer le code par e-mail (simulé ici)
                    // Dans une application réelle, vous utiliseriez une bibliothèque comme PHPMailer
                    // pour envoyer un véritable e-mail
                    error_log("Code de réinitialisation pour {$data['email']}: $resetCode");
                    
                    // Journaliser la demande de réinitialisation
                    logActivity($user['username'], 'reset', 'user', $user['id'], "Demande de réinitialisation de mot de passe");
                    
                    jsonResponse(['success' => true, 'message' => 'Si cette adresse e-mail est associée à un compte, un code de réinitialisation a été envoyé.']);
                } catch (PDOException $e) {
                    jsonResponse(['success' => false, 'message' => 'Erreur lors de la demande de réinitialisation: ' . $e->getMessage()], 500);
                }
                break;
                
            case 'verify':
                // Vérifier que les données nécessaires sont présentes
                if (!isset($data['email']) || !isset($data['reset_code'])) {
                    jsonResponse(['success' => false, 'message' => 'Données incomplètes'], 400);
                }
                
                try {
                    $pdo = getDbConnection();
                    
                    // Récupérer l'utilisateur
                    $userStmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
                    $userStmt->execute([$data['email']]);
                    $user = $userStmt->fetch();
                    
                    if (!$user) {
                        jsonResponse(['success' => false, 'message' => 'Adresse e-mail invalide'], 400);
                        return;
                    }
                    
                    // Vérifier le code de réinitialisation
                    $resetStmt = $pdo->prepare("SELECT reset_token FROM password_resets WHERE user_id = ? AND reset_code = ? AND expires_at > NOW()");
                    $resetStmt->execute([$user['id'], $data['reset_code']]);
                    $reset = $resetStmt->fetch();
                    
                    if (!$reset) {
                        jsonResponse(['success' => false, 'message' => 'Code de réinitialisation invalide ou expiré'], 400);
                        return;
                    }
                    
                    jsonResponse(['success' => true, 'reset_token' => $reset['reset_token']]);
                } catch (PDOException $e) {
                    jsonResponse(['success' => false, 'message' => 'Erreur lors de la vérification du code: ' . $e->getMessage()], 500);
                }
                break;
                
            case 'resend':
                // Vérifier que l'email est fourni
                if (!isset($data['email'])) {
                    jsonResponse(['success' => false, 'message' => 'Adresse e-mail requise'], 400);
                }
                
                // Réutiliser la logique de la demande initiale
                $_GET['action'] = 'request';
                return;
                
            case 'reset':
                // Vérifier que les données nécessaires sont présentes
                if (!isset($data['email']) || !isset($data['reset_token']) || !isset($data['new_password'])) {
                    jsonResponse(['success' => false, 'message' => 'Données incomplètes'], 400);
                }
                
                try {
                    $pdo = getDbConnection();
                    
                    // Récupérer l'utilisateur
                    $userStmt = $pdo->prepare("SELECT id, username FROM users WHERE email = ?");
                    $userStmt->execute([$data['email']]);
                    $user = $userStmt->fetch();
                    
                    if (!$user) {
                        jsonResponse(['success' => false, 'message' => 'Adresse e-mail invalide'], 400);
                        return;
                    }
                    
                    // Vérifier le token de réinitialisation
                    $resetStmt = $pdo->prepare("SELECT id FROM password_resets WHERE user_id = ? AND reset_token = ? AND expires_at > NOW()");
                    $resetStmt->execute([$user['id'], $data['reset_token']]);
                    
                    if (!$resetStmt->fetch()) {
                        jsonResponse(['success' => false, 'message' => 'Token de réinitialisation invalide ou expiré'], 400);
                        return;
                    }
                    
                    // Hasher le nouveau mot de passe
                    $hashedPassword = password_hash($data['new_password'], PASSWORD_DEFAULT);
                    
                    // Mettre à jour le mot de passe de l'utilisateur
                    $updateStmt = $pdo->prepare("UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?");
                    $updateStmt->execute([$hashedPassword, $user['id']]);
                    
                    // Supprimer la demande de réinitialisation
                    $deleteStmt = $pdo->prepare("DELETE FROM password_resets WHERE user_id = ?");
                    $deleteStmt->execute([$user['id']]);
                    
                    // Journaliser la réinitialisation du mot de passe
                    logActivity($user['username'], 'reset', 'user', $user['id'], "Réinitialisation du mot de passe réussie");
                    
                    jsonResponse(['success' => true, 'message' => 'Mot de passe réinitialisé avec succès']);
                } catch (PDOException $e) {
                    jsonResponse(['success' => false, 'message' => 'Erreur lors de la réinitialisation du mot de passe: ' . $e->getMessage()], 500);
                }
                break;
                
            default:
                jsonResponse(['success' => false, 'message' => 'Action non reconnue'], 400);
        }
        break;
        
    default:
        jsonResponse(['success' => false, 'message' => 'Méthode non autorisée'], 405);
}

// Base de données MySQL pour le système de suivi des affaires juridiques
// Ce fichier remplace database.js et utilise des appels API pour interagir avec la base de données MySQL

// ===== INITIALISATION =====

// Fonction d'initialisation (pour compatibilité avec le code existant)
function initDatabase() {
    return new Promise((resolve, reject) => {
        // Vérifier si l'utilisateur est connecté
        const currentUser = localStorage.getItem('currentUser');
        if (!currentUser) {
            reject(new Error('Utilisateur non connecté'));
            return;
        }

        // Vérifier la connexion à la base de données
        fetch('../../app/api/db_api.php?action=check', {
            method: 'GET',
            credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('Connexion à la base de données MySQL établie avec succès');
                resolve(true);
            } else {
                console.error('Erreur de connexion à la base de données MySQL:', data.message);
                reject(new Error(data.message));
            }
        })
        .catch(error => {
            console.error('Erreur lors de la vérification de la connexion à la base de données:', error);
            reject(error);
        });
    });
}

// ===== FONCTIONS POUR LES AVOCATS =====

// Récupérer tous les avocats
function getAllAvocats() {
    return new Promise((resolve, reject) => {
        fetch('../../app/api/avocats_api.php?action=list', {
            method: 'GET',
            credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                resolve(data.avocats);
            } else {
                reject(new Error(data.message));
            }
        })
        .catch(error => {
            console.error('Erreur lors de la récupération des avocats:', error);
            reject(error);
        });
    });
}

// Récupérer un avocat par son ID
function getAvocatById(id) {
    return new Promise((resolve, reject) => {
        fetch(`../../app/api/avocats_api.php?action=get&id=${id}`, {
            method: 'GET',
            credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                resolve(data.avocat);
            } else {
                reject(new Error(data.message));
            }
        })
        .catch(error => {
            console.error('Erreur lors de la récupération de l\'avocat:', error);
            reject(error);
        });
    });
}

// Ajouter un nouvel avocat
function addAvocat(avocat) {
    return new Promise((resolve, reject) => {
        fetch('../../app/api/avocats_api.php?action=create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(avocat),
            credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                resolve(data.id);
            } else {
                reject(new Error(data.message));
            }
        })
        .catch(error => {
            console.error('Erreur lors de l\'ajout de l\'avocat:', error);
            reject(error);
        });
    });
}

// Mettre à jour un avocat existant
function updateAvocat(avocat) {
    return new Promise((resolve, reject) => {
        fetch('../../app/api/avocats_api.php?action=update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(avocat),
            credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                resolve(true);
            } else {
                reject(new Error(data.message));
            }
        })
        .catch(error => {
            console.error('Erreur lors de la mise à jour de l\'avocat:', error);
            reject(error);
        });
    });
}

// Supprimer un avocat
function deleteAvocat(id) {
    return new Promise((resolve, reject) => {
        fetch('../../app/api/avocats_api.php?action=delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ id }),
            credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                resolve(true);
            } else {
                reject(new Error(data.message));
            }
        })
        .catch(error => {
            console.error('Erreur lors de la suppression de l\'avocat:', error);
            reject(error);
        });
    });
}

// ===== FONCTIONS POUR LES AFFAIRES =====

// Récupérer toutes les affaires
function getAllAffaires() {
    return new Promise((resolve, reject) => {
        fetch('../../app/api/affaires_api.php?action=list', {
            method: 'GET',
            credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                resolve(data.affaires);
            } else {
                reject(new Error(data.message));
            }
        })
        .catch(error => {
            console.error('Erreur lors de la récupération des affaires:', error);
            reject(error);
        });
    });
}

// Récupérer une affaire par son ID
function getAffaireById(id) {
    return new Promise((resolve, reject) => {
        fetch(`../../app/api/affaires_api.php?action=get&id=${id}`, {
            method: 'GET',
            credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                resolve(data.affaire);
            } else {
                reject(new Error(data.message));
            }
        })
        .catch(error => {
            console.error('Erreur lors de la récupération de l\'affaire:', error);
            reject(error);
        });
    });
}

// Ajouter une nouvelle affaire
function addAffaire(affaire) {
    return new Promise((resolve, reject) => {
        fetch('../../app/api/affaires_api.php?action=create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(affaire),
            credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                resolve(data.id);
            } else {
                reject(new Error(data.message));
            }
        })
        .catch(error => {
            console.error('Erreur lors de l\'ajout de l\'affaire:', error);
            reject(error);
        });
    });
}

// Mettre à jour une affaire existante
function updateAffaire(affaire) {
    return new Promise((resolve, reject) => {
        fetch('../../app/api/affaires_api.php?action=update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(affaire),
            credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                resolve(true);
            } else {
                reject(new Error(data.message));
            }
        })
        .catch(error => {
            console.error('Erreur lors de la mise à jour de l\'affaire:', error);
            reject(error);
        });
    });
}

// Supprimer une affaire
function deleteAffaire(id) {
    return new Promise((resolve, reject) => {
        // Vérifier si l'utilisateur est administrateur
        const currentUser = JSON.parse(localStorage.getItem('currentUser'));
        if (!currentUser || currentUser.role !== 'admin') {
            reject(new Error('Seuls les administrateurs peuvent supprimer des affaires'));
            return;
        }

        fetch('../../app/api/affaires_api.php?action=delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ id }),
            credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                resolve(true);
            } else {
                reject(new Error(data.message));
            }
        })
        .catch(error => {
            console.error('Erreur lors de la suppression de l\'affaire:', error);
            reject(error);
        });
    });
}

// ===== FONCTIONS POUR LES JOURNAUX D'ACTIVITÉ =====

// Ajouter une entrée dans le journal d'activité
function addActivityLog(logEntry) {
    return new Promise((resolve, reject) => {
        fetch('../../app/api/logs_api.php?action=add', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(logEntry),
            credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                resolve(data.id);
            } else {
                reject(new Error(data.message));
            }
        })
        .catch(error => {
            console.error('Erreur lors de l\'ajout du journal d\'activité:', error);
            reject(error);
        });
    });
}

// Récupérer tous les journaux d'activité
function getAllActivityLogs() {
    return new Promise((resolve, reject) => {
        fetch('../../app/api/logs_api.php?action=list', {
            method: 'GET',
            credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                resolve(data.logs);
            } else {
                reject(new Error(data.message));
            }
        })
        .catch(error => {
            console.error('Erreur lors de la récupération des journaux d\'activité:', error);
            reject(error);
        });
    });
}

// Récupérer les journaux d'activité filtrés
function getFilteredActivityLogs(filters) {
    return new Promise((resolve, reject) => {
        // Construire l'URL avec les paramètres de filtrage
        let url = '../../app/api/logs_api.php?action=list';
        if (filters.username) url += `&username=${encodeURIComponent(filters.username)}`;
        if (filters.action) url += `&action=${encodeURIComponent(filters.action)}`;
        if (filters.entity_type) url += `&entity_type=${encodeURIComponent(filters.entity_type)}`;
        if (filters.date) url += `&date=${encodeURIComponent(filters.date)}`;
        if (filters.page) url += `&page=${filters.page}`;
        if (filters.limit) url += `&limit=${filters.limit}`;

        fetch(url, {
            method: 'GET',
            credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                resolve({
                    logs: data.logs,
                    total: data.total,
                    page: data.page,
                    pages: data.pages
                });
            } else {
                reject(new Error(data.message));
            }
        })
        .catch(error => {
            console.error('Erreur lors de la récupération des journaux d\'activité filtrés:', error);
            reject(error);
        });
    });
}

// ===== FONCTION POUR AJOUTER DES DONNÉES DE TEST =====

// Fonction pour ajouter des données de test
function addTestData() {
    return new Promise((resolve, reject) => {
        fetch('../../app/api/db_api.php?action=add_test_data', {
            method: 'POST',
            credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                resolve(true);
            } else {
                reject(new Error(data.message));
            }
        })
        .catch(error => {
            console.error('Erreur lors de l\'ajout des données de test:', error);
            reject(error);
        });
    });
}

// ===== FONCTION POUR RÉINITIALISER LA BASE DE DONNÉES =====

// Fonction pour réinitialiser complètement la base de données
function resetDatabase() {
    return new Promise((resolve, reject) => {
        // Vérifier si l'utilisateur est administrateur
        const currentUser = JSON.parse(localStorage.getItem('currentUser'));
        if (!currentUser || currentUser.role !== 'admin') {
            reject(new Error('Seuls les administrateurs peuvent réinitialiser la base de données'));
            return;
        }

        fetch('../../app/api/db_api.php?action=reset', {
            method: 'POST',
            credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                resolve(true);
            } else {
                reject(new Error(data.message));
            }
        })
        .catch(error => {
            console.error('Erreur lors de la réinitialisation de la base de données:', error);
            reject(error);
        });
    });
}

// Exporter les fonctions dans l'objet global window
window.DB = {
    initDatabase,
    getAllAvocats,
    getAvocatById,
    addAvocat,
    updateAvocat,
    deleteAvocat,
    getAllAffaires,
    getAffaireById,
    addAffaire,
    updateAffaire,
    deleteAffaire,
    addTestData,
    // Fonctions pour les journaux d'activité
    addActivityLog,
    getAllActivityLogs,
    getFilteredActivityLogs,
    // Fonction de réinitialisation
    resetDatabase
};

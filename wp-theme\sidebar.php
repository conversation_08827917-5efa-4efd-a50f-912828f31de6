<aside id="secondary" class="sidebar">
    
    <!-- Widget des statistiques du système juridique -->
    <div class="widget">
        <h2 class="widget-title">Système Juridique</h2>
        <?php echo do_shortcode('[juridique_stats]'); ?>
        
        <?php if (is_user_logged_in()) : ?>
            <div style="margin-top: 1rem;">
                <a href="<?php echo admin_url('admin.php?page=juridique-integration'); ?>" class="juridique-btn" style="display: block; text-align: center;">
                    Accéder au système
                </a>
            </div>
        <?php else : ?>
            <div style="margin-top: 1rem;">
                <a href="<?php echo wp_login_url(); ?>" class="juridique-btn" style="display: block; text-align: center;">
                    Se connecter
                </a>
            </div>
        <?php endif; ?>
    </div>

    <!-- Widget des articles récents -->
    <div class="widget">
        <h2 class="widget-title">Articles récents</h2>
        <ul>
            <?php
            $recent_posts = wp_get_recent_posts(array(
                'numberposts' => 5,
                'post_status' => 'publish'
            ));
            foreach ($recent_posts as $post) :
            ?>
                <li>
                    <a href="<?php echo get_permalink($post['ID']); ?>">
                        <?php echo $post['post_title']; ?>
                    </a>
                    <small style="display: block; color: #666; font-size: 0.8rem;">
                        <?php echo get_the_date('d/m/Y', $post['ID']); ?>
                    </small>
                </li>
            <?php endforeach; wp_reset_query(); ?>
        </ul>
    </div>

    <!-- Widget des catégories -->
    <div class="widget">
        <h2 class="widget-title">Catégories</h2>
        <ul>
            <?php wp_list_categories(array(
                'orderby' => 'name',
                'show_count' => true,
                'title_li' => ''
            )); ?>
        </ul>
    </div>

    <!-- Widget des archives -->
    <div class="widget">
        <h2 class="widget-title">Archives</h2>
        <ul>
            <?php wp_get_archives(array(
                'type' => 'monthly',
                'limit' => 12
            )); ?>
        </ul>
    </div>

    <!-- Widget des tags -->
    <?php
    $tags = get_tags();
    if ($tags) :
    ?>
        <div class="widget">
            <h2 class="widget-title">Mots-clés</h2>
            <div class="tagcloud">
                <?php wp_tag_cloud(array(
                    'smallest' => 0.8,
                    'largest' => 1.2,
                    'unit' => 'rem',
                    'number' => 20
                )); ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- Widget de contact rapide -->
    <div class="widget">
        <h2 class="widget-title">Contact rapide</h2>
        <div style="text-align: center;">
            <p><strong>Téléphone</strong><br>+33 1 23 45 67 89</p>
            <p><strong>Email</strong><br><EMAIL></p>
            <a href="<?php echo home_url('/contact/'); ?>" class="juridique-btn" style="display: inline-block; margin-top: 1rem;">
                Nous contacter
            </a>
        </div>
    </div>

    <!-- Widget des horaires -->
    <div class="widget">
        <h2 class="widget-title">Horaires d'ouverture</h2>
        <table style="width: 100%; font-size: 0.9rem;">
            <tr>
                <td><strong>Lundi - Vendredi</strong></td>
                <td>9h00 - 18h00</td>
            </tr>
            <tr>
                <td><strong>Samedi</strong></td>
                <td>9h00 - 12h00</td>
            </tr>
            <tr>
                <td><strong>Dimanche</strong></td>
                <td>Fermé</td>
            </tr>
        </table>
    </div>

    <?php
    // Afficher les widgets WordPress si ils sont définis
    if (is_active_sidebar('sidebar-1')) :
        dynamic_sidebar('sidebar-1');
    endif;
    ?>

</aside>

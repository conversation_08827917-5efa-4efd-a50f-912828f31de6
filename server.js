const express = require('express');
const cors = require('cors');
const mysqlDB = require('./database/mysql.js');

const app = express();
const port = process.env.PORT || 3000;

// Configuration CORS pour le réseau local
app.use(cors({
    origin: '*',  // Permet l'accès depuis n'importe quelle origine sur le réseau local
    credentials: true
}));

app.use(express.json());

// Routes pour les avocats
app.get('/api/avocats', async (req, res) => {
    try {
        const avocats = await mysqlDB.getAllAvocats();
        res.json(avocats);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// ... autres routes API

app.listen(port, '0.0.0.0', () => { // Écoute sur toutes les interfaces réseau
    console.log(`Serveur démarré sur le port ${port}`);
    console.log(`Application accessible sur http://[votre-ip]:${port}`);
});




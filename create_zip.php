<?php
// Script pour créer un fichier ZIP contenant tous les fichiers du projet

// Nom du fichier ZIP
$zipFileName = 'suivi_affaires_juridiques.zip';

// Créer un nouvel objet ZipArchive
$zip = new ZipArchive();

// Ouvrir le fichier ZIP pour écriture
if ($zip->open($zipFileName, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== true) {
    die("Impossible de créer le fichier ZIP");
}

// Fonction récursive pour ajouter des fichiers et des répertoires au ZIP
function addFilesToZip($dir, $zip, $exclusions = []) {
    $files = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir),
        RecursiveIteratorIterator::LEAVES_ONLY
    );

    foreach ($files as $name => $file) {
        // Ignorer les répertoires "." et ".."
        if (!$file->isDir()) {
            // Obtenir le chemin relatif
            $filePath = $file->getRealPath();
            $relativePath = substr($filePath, strlen(realpath('.')) + 1);
            
            // Vérifier si le fichier doit être exclu
            $exclude = false;
            foreach ($exclusions as $exclusion) {
                if (strpos($relativePath, $exclusion) === 0) {
                    $exclude = true;
                    break;
                }
            }
            
            if (!$exclude) {
                $zip->addFile($filePath, $relativePath);
                echo "Ajout du fichier: $relativePath<br>";
            }
        }
    }
}

// Fichiers et répertoires à exclure
$exclusions = [
    $zipFileName,
    'create_zip.php',
    'assets',
    'vendor',
    '.git',
    '.idea',
    '.vscode',
    'node_modules'
];

// Ajouter tous les fichiers au ZIP
addFilesToZip('.', $zip, $exclusions);

// Fermer le fichier ZIP
$zip->close();

echo "<h2>Fichier ZIP créé avec succès!</h2>";
echo "<p>Vous pouvez télécharger le fichier ZIP en cliquant sur le lien ci-dessous:</p>";
echo "<a href='$zipFileName' class='btn btn-primary'>Télécharger $zipFileName</a>";
?>

/*
Theme Name: Cabinet Juridique
Description: Thème personnalisé pour le cabinet juridique avec intégration du système de gestion
Version: 1.0.0
Author: Votre Nom
*/

/* Reset et styles de base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.site-header {
    background: #2c3e50;
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.site-header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.site-title {
    font-size: 1.8rem;
    font-weight: bold;
    text-decoration: none;
    color: white;
}

.site-title:hover {
    color: #3498db;
}

.site-description {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-top: 0.2rem;
}

/* Navigation */
.main-navigation {
    background: #34495e;
    padding: 0.5rem 0;
}

.main-navigation ul {
    list-style: none;
    display: flex;
    justify-content: center;
}

.main-navigation li {
    margin: 0 1rem;
}

.main-navigation a {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.main-navigation a:hover,
.main-navigation a.current {
    background-color: #3498db;
}

/* Contenu principal */
.site-main {
    padding: 2rem 0;
    min-height: 60vh;
}

.content-area {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.main-content {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.sidebar {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    height: fit-content;
}

/* Articles et pages */
.entry-header {
    margin-bottom: 1.5rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 1rem;
}

.entry-title {
    font-size: 2rem;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.entry-meta {
    color: #666;
    font-size: 0.9rem;
}

.entry-content {
    line-height: 1.8;
}

.entry-content h2,
.entry-content h3,
.entry-content h4 {
    color: #2c3e50;
    margin: 1.5rem 0 1rem 0;
}

.entry-content p {
    margin-bottom: 1rem;
}

.entry-content ul,
.entry-content ol {
    margin: 1rem 0 1rem 2rem;
}

/* Widgets de la sidebar */
.widget {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #eee;
}

.widget:last-child {
    border-bottom: none;
}

.widget-title {
    font-size: 1.2rem;
    color: #2c3e50;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #3498db;
}

.widget ul {
    list-style: none;
}

.widget li {
    padding: 0.3rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.widget a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s;
}

.widget a:hover {
    color: #3498db;
}

/* Intégration du système juridique */
.juridique-integration-section {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin: 2rem 0;
}

.juridique-integration-section h2 {
    color: #2c3e50;
    margin-bottom: 1rem;
    text-align: center;
}

.juridique-access-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin: 1.5rem 0;
}

.juridique-btn {
    background: #3498db;
    color: white;
    padding: 0.8rem 1.5rem;
    text-decoration: none;
    border-radius: 6px;
    font-weight: bold;
    transition: background-color 0.3s;
    border: none;
    cursor: pointer;
}

.juridique-btn:hover {
    background: #2980b9;
    color: white;
}

.juridique-btn.secondary {
    background: #95a5a6;
}

.juridique-btn.secondary:hover {
    background: #7f8c8d;
}

/* Footer */
.site-footer {
    background: #2c3e50;
    color: white;
    padding: 2rem 0 1rem 0;
    margin-top: 3rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 1rem;
}

.footer-section h3 {
    color: #3498db;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section li {
    padding: 0.3rem 0;
}

.footer-section a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-section a:hover {
    color: white;
}

.footer-bottom {
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid #34495e;
    color: #bdc3c7;
    font-size: 0.9rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .site-header .container {
        flex-direction: column;
        text-align: center;
    }
    
    .main-navigation ul {
        flex-direction: column;
        align-items: center;
    }
    
    .main-navigation li {
        margin: 0.2rem 0;
    }
    
    .content-area {
        grid-template-columns: 1fr;
    }
    
    .juridique-access-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

/* Styles pour les formulaires */
.wp-block-contact-form-7 input,
.wp-block-contact-form-7 textarea,
.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 1rem;
    font-family: inherit;
}

.wp-block-contact-form-7 input[type="submit"],
.contact-form input[type="submit"] {
    background: #3498db;
    color: white;
    border: none;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.3s;
}

.wp-block-contact-form-7 input[type="submit"]:hover,
.contact-form input[type="submit"]:hover {
    background: #2980b9;
}

/* Styles pour les boutons WordPress */
.wp-block-button .wp-block-button__link {
    background: #3498db;
    border-radius: 6px;
    padding: 0.8rem 1.5rem;
    text-decoration: none;
    transition: background-color 0.3s;
}

.wp-block-button .wp-block-button__link:hover {
    background: #2980b9;
}

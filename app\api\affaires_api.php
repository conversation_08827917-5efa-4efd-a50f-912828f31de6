<?php
// API pour la gestion des affaires

// Autoriser les requêtes CORS
header('Access-Control-Allow-Origin: ' . (isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '*'));
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Access-Control-Allow-Credentials: true');
header('Content-Type: application/json');

// Si c'est une requête OPTIONS (preflight), renvoyer 200 OK
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Démarrer la session
session_start();

// Inclure les fichiers nécessaires
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../auth.php';

// Vérifier si l'utilisateur est connecté
if (!isLoggedIn()) {
    jsonResponse(['success' => false, 'message' => 'Non authentifié'], 401);
}

// Récupérer la méthode HTTP et l'action
$method = $_SERVER['REQUEST_METHOD'];
$action = isset($_GET['action']) ? $_GET['action'] : '';

// Traiter la requête en fonction de la méthode et de l'action
switch ($method) {
    case 'GET':
        switch ($action) {
            case 'list':
                // Récupérer la liste des affaires
                try {
                    $pdo = getDbConnection();
                    $stmt = $pdo->query("SELECT a.*, av.nom as maitre_nom
                                        FROM affaires a
                                        LEFT JOIN avocats av ON a.maitre_id = av.id
                                        ORDER BY a.numero");
                    $affaires = $stmt->fetchAll();

                    // Convertir les dates au format YYYY-MM-DD pour la compatibilité avec le frontend
                    foreach ($affaires as &$affaire) {
                        $affaire['dateEngagement'] = $affaire['date_engagement'];
                        $affaire['dateJugement'] = $affaire['date_jugement'];
                        unset($affaire['date_engagement']);
                        unset($affaire['date_jugement']);
                    }

                    jsonResponse(['success' => true, 'affaires' => $affaires]);
                } catch (PDOException $e) {
                    jsonResponse(['success' => false, 'message' => 'Erreur lors de la récupération des affaires: ' . $e->getMessage()], 500);
                }
                break;

            case 'get':
                // Vérifier que l'ID de l'affaire est fourni
                if (!isset($_GET['id'])) {
                    jsonResponse(['success' => false, 'message' => 'ID de l\'affaire requis'], 400);
                }

                // Récupérer les informations d'une affaire spécifique
                try {
                    $pdo = getDbConnection();
                    $stmt = $pdo->prepare("SELECT a.*, av.nom as maitre_nom
                                          FROM affaires a
                                          LEFT JOIN avocats av ON a.maitre_id = av.id
                                          WHERE a.id = ?");
                    $stmt->execute([$_GET['id']]);
                    $affaire = $stmt->fetch();

                    if (!$affaire) {
                        jsonResponse(['success' => false, 'message' => 'Affaire non trouvée'], 404);
                    }

                    // Convertir les dates au format YYYY-MM-DD pour la compatibilité avec le frontend
                    $affaire['dateEngagement'] = $affaire['date_engagement'];
                    $affaire['dateJugement'] = $affaire['date_jugement'];
                    unset($affaire['date_engagement']);
                    unset($affaire['date_jugement']);

                    jsonResponse(['success' => true, 'affaire' => $affaire]);
                } catch (PDOException $e) {
                    jsonResponse(['success' => false, 'message' => 'Erreur lors de la récupération de l\'affaire: ' . $e->getMessage()], 500);
                }
                break;

            default:
                jsonResponse(['success' => false, 'message' => 'Action non reconnue'], 400);
        }
        break;

    case 'POST':
        // Récupérer les données JSON de la requête
        $data = json_decode(file_get_contents('php://input'), true);

        switch ($action) {
            case 'create':
                // Vérifier que les données nécessaires sont présentes
                if (!isset($data['numero']) || !isset($data['nature']) || !isset($data['objet']) ||
                    !isset($data['juridiction']) || !isset($data['engagement']) ||
                    !isset($data['dateEngagement']) || !isset($data['statut']) || !isset($data['maitre'])) {
                    jsonResponse(['success' => false, 'message' => 'Données incomplètes'], 400);
                }

                // Créer une nouvelle affaire
                try {
                    $pdo = getDbConnection();

                    // Vérifier si le numéro d'affaire existe déjà
                    $checkStmt = $pdo->prepare("SELECT id FROM affaires WHERE numero = ?");
                    $checkStmt->execute([$data['numero']]);
                    if ($checkStmt->fetch()) {
                        jsonResponse(['success' => false, 'message' => 'Ce numéro d\'affaire existe déjà'], 400);
                    }

                    // Vérifier si l'avocat existe
                    $checkStmt = $pdo->prepare("SELECT id FROM avocats WHERE id = ?");
                    $checkStmt->execute([$data['maitre']]);
                    if (!$checkStmt->fetch()) {
                        jsonResponse(['success' => false, 'message' => 'Avocat non trouvé'], 404);
                    }

                    // Insérer la nouvelle affaire
                    $stmt = $pdo->prepare("INSERT INTO affaires (numero, nature, objet, parties, juridiction, engagement,
                                          date_engagement, date_jugement, statut, dispositif, montant, maitre_id)
                                          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                    $stmt->execute([
                        $data['numero'],
                        $data['nature'],
                        $data['objet'],
                        $data['parties'] ?? null,
                        $data['juridiction'],
                        $data['engagement'],
                        $data['dateEngagement'],
                        !empty($data['dateJugement']) ? $data['dateJugement'] : null,
                        $data['statut'],
                        $data['dispositif'] ?? null,
                        $data['montant'] ?? null,
                        $data['maitre']
                    ]);

                    $affaireId = $pdo->lastInsertId();

                    // Journaliser la création de l'affaire
                    logActivity($_SESSION['username'], 'create', 'affaire', $affaireId, "Création de l'affaire {$data['numero']}");

                    jsonResponse([
                        'success' => true,
                        'message' => 'Affaire créée avec succès',
                        'id' => $affaireId
                    ]);
                } catch (PDOException $e) {
                    jsonResponse(['success' => false, 'message' => 'Erreur lors de la création de l\'affaire: ' . $e->getMessage()], 500);
                }
                break;

            case 'update':
                // Vérifier que les données nécessaires sont présentes
                if (!isset($data['id']) || !isset($data['numero']) || !isset($data['nature']) ||
                    !isset($data['objet']) || !isset($data['juridiction']) || !isset($data['engagement']) ||
                    !isset($data['dateEngagement']) || !isset($data['statut']) || !isset($data['maitre'])) {
                    jsonResponse(['success' => false, 'message' => 'Données incomplètes'], 400);
                }

                // Mettre à jour une affaire existante
                try {
                    $pdo = getDbConnection();

                    // Vérifier si l'affaire existe
                    $checkStmt = $pdo->prepare("SELECT id FROM affaires WHERE id = ?");
                    $checkStmt->execute([$data['id']]);
                    if (!$checkStmt->fetch()) {
                        jsonResponse(['success' => false, 'message' => 'Affaire non trouvée'], 404);
                    }

                    // Vérifier si le numéro d'affaire existe déjà pour une autre affaire
                    $checkStmt = $pdo->prepare("SELECT id FROM affaires WHERE numero = ? AND id != ?");
                    $checkStmt->execute([$data['numero'], $data['id']]);
                    if ($checkStmt->fetch()) {
                        jsonResponse(['success' => false, 'message' => 'Ce numéro d\'affaire est déjà utilisé par une autre affaire'], 400);
                    }

                    // Vérifier si l'avocat existe
                    $checkStmt = $pdo->prepare("SELECT id FROM avocats WHERE id = ?");
                    $checkStmt->execute([$data['maitre']]);
                    if (!$checkStmt->fetch()) {
                        jsonResponse(['success' => false, 'message' => 'Avocat non trouvé'], 404);
                    }

                    // Mettre à jour l'affaire
                    $stmt = $pdo->prepare("UPDATE affaires SET numero = ?, nature = ?, objet = ?, parties = ?,
                                          juridiction = ?, engagement = ?, date_engagement = ?, date_jugement = ?,
                                          statut = ?, dispositif = ?, montant = ?, maitre_id = ? WHERE id = ?");
                    $stmt->execute([
                        $data['numero'],
                        $data['nature'],
                        $data['objet'],
                        $data['parties'] ?? null,
                        $data['juridiction'],
                        $data['engagement'],
                        $data['dateEngagement'],
                        !empty($data['dateJugement']) ? $data['dateJugement'] : null,
                        $data['statut'],
                        $data['dispositif'] ?? null,
                        $data['montant'] ?? null,
                        $data['maitre'],
                        $data['id']
                    ]);

                    // Journaliser la mise à jour de l'affaire
                    logActivity($_SESSION['username'], 'update', 'affaire', $data['id'], "Mise à jour de l'affaire {$data['numero']}");

                    jsonResponse([
                        'success' => true,
                        'message' => 'Affaire mise à jour avec succès'
                    ]);
                } catch (PDOException $e) {
                    jsonResponse(['success' => false, 'message' => 'Erreur lors de la mise à jour de l\'affaire: ' . $e->getMessage()], 500);
                }
                break;

            case 'delete':
                // Vérifier que l'ID de l'affaire est fourni
                if (!isset($data['id'])) {
                    jsonResponse(['success' => false, 'message' => 'ID de l\'affaire requis'], 400);
                }

                // Vérifier si l'utilisateur est administrateur
                if (!isAdmin()) {
                    jsonResponse(['success' => false, 'message' => 'Seuls les administrateurs peuvent supprimer des affaires'], 403);
                }

                // Supprimer une affaire
                try {
                    $pdo = getDbConnection();

                    // Vérifier si l'affaire existe
                    $checkStmt = $pdo->prepare("SELECT numero FROM affaires WHERE id = ?");
                    $checkStmt->execute([$data['id']]);
                    $affaire = $checkStmt->fetch();

                    if (!$affaire) {
                        jsonResponse(['success' => false, 'message' => 'Affaire non trouvée'], 404);
                    }

                    // Supprimer l'affaire
                    $stmt = $pdo->prepare("DELETE FROM affaires WHERE id = ?");
                    $stmt->execute([$data['id']]);

                    // Journaliser la suppression de l'affaire
                    logActivity($_SESSION['username'], 'delete', 'affaire', $data['id'], "Suppression de l'affaire {$affaire['numero']}");

                    jsonResponse([
                        'success' => true,
                        'message' => 'Affaire supprimée avec succès'
                    ]);
                } catch (PDOException $e) {
                    // Journaliser l'échec de la suppression
                    logActivity($_SESSION['username'], 'delete_failed', 'affaire', $data['id'], "Échec de la suppression de l'affaire: " . $e->getMessage());

                    jsonResponse(['success' => false, 'message' => 'Erreur lors de la suppression de l\'affaire: ' . $e->getMessage()], 500);
                }
                break;

            default:
                jsonResponse(['success' => false, 'message' => 'Action non reconnue'], 400);
        }
        break;

    default:
        jsonResponse(['success' => false, 'message' => 'Méthode non autorisée'], 405);
}

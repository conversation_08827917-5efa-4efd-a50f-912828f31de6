<?php
// Script pour vérifier que tous les fichiers nécessaires sont présents

// Liste des fichiers essentiels
$essentialFiles = [
    // Fichiers PHP principaux
    'index.php',
    'login.html',
    'app.html',
    'config.php',
    'auth.php',
    'init_db.php',
    'test_db.php',
    
    // Fichiers API
    'api/db_api.php',
    'api/auth_api.php',
    'api/affaires_api.php',
    'api/avocats_api.php',
    'api/logs_api.php',
    
    // Fichiers JavaScript
    'app.js',
    'auth.js',
    'database_mysql.js',
    
    // Fichiers CSS
    'styles.css',
    
    // Fichiers de base de données
    'database/schema.sql',
    
    // Fichiers de dépendances (après téléchargement)
    'assets/css/bootstrap.min.css',
    'assets/js/bootstrap.bundle.min.js',
    'assets/js/jquery.min.js'
];

// Vérifier les fichiers essentiels
$missingFiles = [];
foreach ($essentialFiles as $file) {
    if (!file_exists($file)) {
        $missingFiles[] = $file;
    }
}

// Vérifier les répertoires essentiels
$essentialDirectories = [
    'api',
    'database',
    'assets',
    'assets/css',
    'assets/js',
    'assets/fonts'
];

$missingDirectories = [];
foreach ($essentialDirectories as $dir) {
    if (!is_dir($dir)) {
        $missingDirectories[] = $dir;
    }
}

// Vérifier les permissions des répertoires
$directoryPermissions = [];
foreach ($essentialDirectories as $dir) {
    if (is_dir($dir)) {
        $directoryPermissions[$dir] = [
            'path' => $dir,
            'permissions' => substr(sprintf('%o', fileperms($dir)), -4),
            'writable' => is_writable($dir)
        ];
    }
}

// Vérifier la configuration PHP
$phpConfig = [
    'version' => PHP_VERSION,
    'max_execution_time' => ini_get('max_execution_time'),
    'memory_limit' => ini_get('memory_limit'),
    'post_max_size' => ini_get('post_max_size'),
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'display_errors' => ini_get('display_errors'),
    'file_uploads' => ini_get('file_uploads'),
    'pdo_mysql' => extension_loaded('pdo_mysql') ? 'Activé' : 'Désactivé'
];

// Vérifier la connexion à la base de données
$dbConnection = false;
$dbError = '';
if (file_exists('config.php')) {
    require_once 'config.php';
    try {
        $pdo = getDbConnection();
        $dbConnection = true;
    } catch (PDOException $e) {
        $dbError = $e->getMessage();
    }
}

// Afficher les résultats
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vérification des fichiers</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #0d6efd;
            margin-bottom: 20px;
        }
        .table {
            margin-top: 20px;
        }
        .alert {
            margin-top: 20px;
        }
        .btn {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Vérification des fichiers</h1>
        
        <?php if (count($missingFiles) > 0): ?>
            <div class="alert alert-danger">
                <h4>Fichiers manquants</h4>
                <ul>
                    <?php foreach ($missingFiles as $file): ?>
                        <li><?php echo $file; ?></li>
                    <?php endforeach; ?>
                </ul>
                <p>Ces fichiers sont essentiels au fonctionnement de l'application. Veuillez les restaurer.</p>
            </div>
        <?php else: ?>
            <div class="alert alert-success">
                <h4>Tous les fichiers essentiels sont présents</h4>
            </div>
        <?php endif; ?>
        
        <?php if (count($missingDirectories) > 0): ?>
            <div class="alert alert-danger">
                <h4>Répertoires manquants</h4>
                <ul>
                    <?php foreach ($missingDirectories as $dir): ?>
                        <li><?php echo $dir; ?></li>
                    <?php endforeach; ?>
                </ul>
                <p>Ces répertoires sont essentiels au fonctionnement de l'application. Veuillez les créer.</p>
            </div>
        <?php else: ?>
            <div class="alert alert-success">
                <h4>Tous les répertoires essentiels sont présents</h4>
            </div>
        <?php endif; ?>
        
        <h2>Permissions des répertoires</h2>
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Répertoire</th>
                    <th>Permissions</th>
                    <th>Accessible en écriture</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($directoryPermissions as $dir): ?>
                    <tr>
                        <td><?php echo $dir['path']; ?></td>
                        <td><?php echo $dir['permissions']; ?></td>
                        <td>
                            <?php if ($dir['writable']): ?>
                                <span class="badge bg-success">Oui</span>
                            <?php else: ?>
                                <span class="badge bg-danger">Non</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
        <h2>Configuration PHP</h2>
        <table class="table table-striped">
            <tbody>
                <tr>
                    <td>Version PHP</td>
                    <td><?php echo $phpConfig['version']; ?></td>
                </tr>
                <tr>
                    <td>Temps d'exécution maximum</td>
                    <td><?php echo $phpConfig['max_execution_time']; ?> secondes</td>
                </tr>
                <tr>
                    <td>Limite de mémoire</td>
                    <td><?php echo $phpConfig['memory_limit']; ?></td>
                </tr>
                <tr>
                    <td>Taille maximale des données POST</td>
                    <td><?php echo $phpConfig['post_max_size']; ?></td>
                </tr>
                <tr>
                    <td>Taille maximale des fichiers téléchargés</td>
                    <td><?php echo $phpConfig['upload_max_filesize']; ?></td>
                </tr>
                <tr>
                    <td>Affichage des erreurs</td>
                    <td><?php echo $phpConfig['display_errors'] ? 'Activé' : 'Désactivé'; ?></td>
                </tr>
                <tr>
                    <td>Téléchargement de fichiers</td>
                    <td><?php echo $phpConfig['file_uploads'] ? 'Activé' : 'Désactivé'; ?></td>
                </tr>
                <tr>
                    <td>Extension PDO MySQL</td>
                    <td><?php echo $phpConfig['pdo_mysql']; ?></td>
                </tr>
            </tbody>
        </table>
        
        <h2>Connexion à la base de données</h2>
        <?php if ($dbConnection): ?>
            <div class="alert alert-success">
                <h4>Connexion à la base de données réussie</h4>
            </div>
        <?php else: ?>
            <div class="alert alert-danger">
                <h4>Erreur de connexion à la base de données</h4>
                <p><?php echo $dbError; ?></p>
                <p>Veuillez vérifier les paramètres de connexion dans le fichier config.php.</p>
            </div>
        <?php endif; ?>
        
        <div class="d-flex justify-content-between mt-4">
            <a href="download_dependencies.php" class="btn btn-primary">Télécharger les dépendances</a>
            <a href="update_references.php" class="btn btn-success">Mettre à jour les références</a>
            <a href="init_db.php" class="btn btn-warning">Initialiser la base de données</a>
        </div>
    </div>
    
    <script src="assets/js/bootstrap.bundle.min.js"></script>
</body>
</html>

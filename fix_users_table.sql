-- Script pour corriger la structure de la table users
USE suivi_affaires_juridiques;

-- Vérifier si la colonne password_hash existe
SET @column_exists = (
    SELECT COUNT(*)
    FROM information_schema.COLUMNS
    WHERE TABLE_SCHEMA = 'suivi_affaires_juridiques'
    AND TABLE_NAME = 'users'
    AND COLUMN_NAME = 'password_hash'
);

-- Si la colonne password_hash existe mais pas password, renommer password_hash en password
SET @sql = IF(@column_exists > 0, 'ALTER TABLE users CHANGE COLUMN password_hash password VARCHAR(255) NOT NULL', 'SELECT 1');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Vérifier si la colonne email existe
SET @column_exists = (
    SELECT COUNT(*)
    FROM information_schema.COLUMNS
    WHERE TABLE_SCHEMA = 'suivi_affaires_juridiques'
    AND TABLE_NAME = 'users'
    AND COLUMN_NAME = 'email'
);

-- Si la colonne email n'existe pas, l'ajouter
SET @sql = IF(@column_exists = 0, 'ALTER TABLE users ADD COLUMN email VARCHAR(100) NOT NULL AFTER password', 'SELECT 1');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Vérifier si la colonne last_login existe
SET @column_exists = (
    SELECT COUNT(*)
    FROM information_schema.COLUMNS
    WHERE TABLE_SCHEMA = 'suivi_affaires_juridiques'
    AND TABLE_NAME = 'users'
    AND COLUMN_NAME = 'last_login'
);

-- Si la colonne last_login n'existe pas, l'ajouter
SET @sql = IF(@column_exists = 0, 'ALTER TABLE users ADD COLUMN last_login DATETIME NULL AFTER role', 'SELECT 1');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Vérifier si la colonne account_locked existe
SET @column_exists = (
    SELECT COUNT(*)
    FROM information_schema.COLUMNS
    WHERE TABLE_SCHEMA = 'suivi_affaires_juridiques'
    AND TABLE_NAME = 'users'
    AND COLUMN_NAME = 'account_locked'
);

-- Si la colonne account_locked n'existe pas, l'ajouter
SET @sql = IF(@column_exists = 0, 'ALTER TABLE users ADD COLUMN account_locked TINYINT(1) DEFAULT 0 AFTER last_login', 'SELECT 1');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Vérifier si la colonne locked_until existe
SET @column_exists = (
    SELECT COUNT(*)
    FROM information_schema.COLUMNS
    WHERE TABLE_SCHEMA = 'suivi_affaires_juridiques'
    AND TABLE_NAME = 'users'
    AND COLUMN_NAME = 'locked_until'
);

-- Si la colonne locked_until n'existe pas, l'ajouter
SET @sql = IF(@column_exists = 0, 'ALTER TABLE users ADD COLUMN locked_until DATETIME NULL AFTER account_locked', 'SELECT 1');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Vérifier si la colonne created_at existe
SET @column_exists = (
    SELECT COUNT(*)
    FROM information_schema.COLUMNS
    WHERE TABLE_SCHEMA = 'suivi_affaires_juridiques'
    AND TABLE_NAME = 'users'
    AND COLUMN_NAME = 'created_at'
);

-- Si la colonne created_at n'existe pas, l'ajouter
SET @sql = IF(@column_exists = 0, 'ALTER TABLE users ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP AFTER locked_until', 'SELECT 1');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Vérifier si la colonne updated_at existe
SET @column_exists = (
    SELECT COUNT(*)
    FROM information_schema.COLUMNS
    WHERE TABLE_SCHEMA = 'suivi_affaires_juridiques'
    AND TABLE_NAME = 'users'
    AND COLUMN_NAME = 'updated_at'
);

-- Si la colonne updated_at n'existe pas, l'ajouter
SET @sql = IF(@column_exists = 0, 'ALTER TABLE users ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_at', 'SELECT 1');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;



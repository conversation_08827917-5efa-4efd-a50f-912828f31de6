// Script pour la gestion du journal d'activité (admin_logs.js)

// Variables globales
let logs = [];
let users = [];
let currentPage = 1;
let logsPerPage = 20;
let totalPages = 1;
let filters = {
    username: '',
    action: '',
    entity_type: '',
    date: ''
};

// Vérifier si l'utilisateur est connecté et est administrateur
document.addEventListener('DOMContentLoaded', async () => {
    // Vérifier si l'utilisateur est connecté
    const currentUser = localStorage.getItem('currentUser');
    if (!currentUser) {
        // Rediriger vers la page de connexion si l'utilisateur n'est pas connecté
        window.location.href = 'login.html';
        return;
    }

    // Vérifier si l'utilisateur est administrateur
    const userData = JSON.parse(currentUser);
    if (userData.role !== 'admin') {
        // Rediriger vers la page principale si l'utilisateur n'est pas administrateur
        alert('Accès refusé. Seuls les administrateurs peuvent accéder à cette page.');
        window.location.href = 'app.html';
        return;
    }

    // Afficher les informations de l'utilisateur connecté
    const userInfoElement = document.getElementById('user-info');
    if (userInfoElement) {
        userInfoElement.textContent = `Connecté en tant que: ${userData.username}`;
    }

    // Gérer le bouton de déconnexion
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', () => {
            logout()
                .then(() => {
                    window.location.href = 'login.html';
                })
                .catch(error => {
                    console.error('Erreur lors de la déconnexion:', error);
                    window.location.href = 'login.html';
                });
        });
    }

    // Charger la liste des utilisateurs pour le filtre
    await loadUsers();

    // Charger les logs
    await loadLogs();

    // Gérer le bouton d'actualisation des logs
    const btnRefreshLogs = document.getElementById('btn-refresh-logs');
    btnRefreshLogs.addEventListener('click', async () => {
        await loadLogs();
        showAlert('Journal d\'activité actualisé', 'success');
    });

    // Gérer le bouton de vidage du journal
    const btnClearLogs = document.getElementById('btn-clear-logs');
    btnClearLogs.addEventListener('click', async () => {
        if (confirm('Êtes-vous sûr de vouloir vider le journal d\'activité ? Cette action est irréversible.')) {
            await clearLogs();
        }
    });

    // Gérer le bouton d'application des filtres
    const btnApplyFilters = document.getElementById('btn-apply-filters');
    btnApplyFilters.addEventListener('click', async () => {
        // Récupérer les valeurs des filtres
        filters.username = document.getElementById('filter-user').value;
        filters.action = document.getElementById('filter-action').value;
        filters.entity_type = document.getElementById('filter-entity').value;
        filters.date = document.getElementById('filter-date').value;

        // Réinitialiser la pagination
        currentPage = 1;

        // Recharger les logs avec les filtres
        await loadLogs();
    });

    // Gérer le bouton de réinitialisation des filtres
    const btnResetFilters = document.getElementById('btn-reset-filters');
    btnResetFilters.addEventListener('click', async () => {
        // Réinitialiser les filtres
        document.getElementById('filter-user').value = '';
        document.getElementById('filter-action').value = '';
        document.getElementById('filter-entity').value = '';
        document.getElementById('filter-date').value = '';

        // Réinitialiser les variables de filtres
        filters = {
            username: '',
            action: '',
            entity_type: '',
            date: ''
        };

        // Réinitialiser la pagination
        currentPage = 1;

        // Recharger les logs sans filtres
        await loadLogs();
    });
});

// Fonction pour charger la liste des utilisateurs
async function loadUsers() {
    try {
        const response = await fetch('app/api/users_api.php?action=list', {
            method: 'GET',
            credentials: 'include'
        });

        const data = await response.json();

        if (data.success) {
            users = data.users;
            populateUserFilter(users);
        } else {
            showAlert(data.message || 'Erreur lors du chargement des utilisateurs', 'danger');
        }
    } catch (error) {
        console.error('Erreur lors du chargement des utilisateurs:', error);
        showAlert('Erreur de connexion au serveur', 'danger');
    }
}

// Fonction pour remplir le filtre des utilisateurs
function populateUserFilter(usersList) {
    const filterUser = document.getElementById('filter-user');
    filterUser.innerHTML = '<option value="">Tous</option>';

    usersList.forEach(user => {
        const option = document.createElement('option');
        option.value = user.username;
        option.textContent = user.username;
        filterUser.appendChild(option);
    });
}

// Fonction pour charger les logs
async function loadLogs() {
    try {
        // Construire l'URL avec les paramètres de filtrage et de pagination
        let url = `app/api/logs_api.php?action=list&page=${currentPage}&limit=${logsPerPage}`;

        // Ajouter les filtres à l'URL
        if (filters.username) url += `&username=${encodeURIComponent(filters.username)}`;
        if (filters.action) url += `&action=${encodeURIComponent(filters.action)}`;
        if (filters.entity_type) url += `&entity_type=${encodeURIComponent(filters.entity_type)}`;
        if (filters.date) url += `&date=${encodeURIComponent(filters.date)}`;

        const response = await fetch(url, {
            method: 'GET',
            credentials: 'include'
        });

        const data = await response.json();

        if (data.success) {
            logs = data.logs;
            totalPages = Math.ceil(data.total / logsPerPage);
            displayLogs(logs);
            displayPagination();
            updateLogsCount(data.total);
        } else {
            showAlert(data.message || 'Erreur lors du chargement des logs', 'danger');
        }
    } catch (error) {
        console.error('Erreur lors du chargement des logs:', error);
        showAlert('Erreur de connexion au serveur', 'danger');
    }
}

// Fonction pour afficher les logs dans le tableau
function displayLogs(logsList) {
    const logsListe = document.getElementById('logs-liste');
    logsListe.innerHTML = '';

    if (logsList.length === 0) {
        const tr = document.createElement('tr');
        tr.innerHTML = '<td colspan="8" class="text-center">Aucune activité trouvée</td>';
        logsListe.appendChild(tr);
        return;
    }

    logsList.forEach(log => {
        const tr = document.createElement('tr');

        // Formater la date et l'heure
        const timestamp = new Date(log.timestamp).toLocaleString('fr-FR');

        // Formater l'action
        let actionBadge = '';
        switch (log.action) {
            case 'create':
                actionBadge = '<span class="badge bg-success">Création</span>';
                break;
            case 'update':
                actionBadge = '<span class="badge bg-warning text-dark">Modification</span>';
                break;
            case 'delete':
                actionBadge = '<span class="badge bg-danger">Suppression</span>';
                break;
            case 'login':
                actionBadge = '<span class="badge bg-info">Connexion</span>';
                break;
            case 'logout':
                actionBadge = '<span class="badge bg-secondary">Déconnexion</span>';
                break;
            case 'reset':
                actionBadge = '<span class="badge bg-dark">Réinitialisation</span>';
                break;
            default:
                actionBadge = `<span class="badge bg-light text-dark">${log.action}</span>`;
        }

        // Formater l'entité
        let entityBadge = '';
        switch (log.entity_type) {
            case 'user':
                entityBadge = '<span class="badge bg-primary">Utilisateur</span>';
                break;
            case 'avocat':
                entityBadge = '<span class="badge bg-success">Avocat</span>';
                break;
            case 'affaire':
                entityBadge = '<span class="badge bg-warning text-dark">Affaire</span>';
                break;
            case 'database':
                entityBadge = '<span class="badge bg-danger">Base de données</span>';
                break;
            default:
                entityBadge = `<span class="badge bg-light text-dark">${log.entity_type}</span>`;
        }

        tr.innerHTML = `
            <td>${log.id}</td>
            <td>${timestamp}</td>
            <td>${log.username}</td>
            <td>${actionBadge}</td>
            <td>${entityBadge}</td>
            <td>${log.entity_id || '-'}</td>
            <td>${log.details || '-'}</td>
            <td>${log.ip_address}</td>
        `;

        logsListe.appendChild(tr);
    });
}

// Fonction pour afficher la pagination
function displayPagination() {
    const pagination = document.getElementById('pagination');
    pagination.innerHTML = '';

    // Bouton précédent
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" data-page="${currentPage - 1}">Précédent</a>`;
    pagination.appendChild(prevLi);

    // Pages
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, startPage + 4);

    for (let i = startPage; i <= endPage; i++) {
        const pageLi = document.createElement('li');
        pageLi.className = `page-item ${i === currentPage ? 'active' : ''}`;
        pageLi.innerHTML = `<a class="page-link" href="#" data-page="${i}">${i}</a>`;
        pagination.appendChild(pageLi);
    }

    // Bouton suivant
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" data-page="${currentPage + 1}">Suivant</a>`;
    pagination.appendChild(nextLi);

    // Ajouter les écouteurs d'événements pour les liens de pagination
    document.querySelectorAll('.page-link').forEach(link => {
        link.addEventListener('click', async (e) => {
            e.preventDefault();
            if (link.parentElement.classList.contains('disabled')) return;

            const page = parseInt(link.getAttribute('data-page'));
            if (page !== currentPage) {
                currentPage = page;
                await loadLogs();
            }
        });
    });
}

// Fonction pour mettre à jour le compteur de logs
function updateLogsCount(total) {
    const logsCount = document.getElementById('logs-count');
    logsCount.textContent = `${total} activité${total > 1 ? 's' : ''} trouvée${total > 1 ? 's' : ''}`;
}

// Fonction pour vider le journal d'activité
async function clearLogs() {
    try {
        const response = await fetch('app/api/logs_api.php?action=clear', {
            method: 'POST',
            credentials: 'include'
        });

        const data = await response.json();

        if (data.success) {
            showAlert('Journal d\'activité vidé avec succès', 'success');
            await loadLogs();
        } else {
            showAlert(data.message || 'Erreur lors du vidage du journal', 'danger');
        }
    } catch (error) {
        console.error('Erreur lors du vidage du journal:', error);
        showAlert('Erreur de connexion au serveur', 'danger');
    }
}

// Fonction pour afficher une alerte
function showAlert(message, type) {
    const alertToast = document.getElementById('alert-toast');
    const alertMessage = document.getElementById('alert-message');

    // Définir le message
    alertMessage.textContent = message;

    // Définir le type d'alerte
    alertToast.className = alertToast.className.replace(/bg-\w+/, `bg-${type}`);

    // Afficher l'alerte
    const toast = new bootstrap.Toast(alertToast);
    toast.show();
}

<?php
/**
 * Plugin Name: Intégration Système Juridique
 * Description: Plugin pour intégrer WordPress avec le système de gestion des affaires juridiques
 * Version: 1.0.0
 * Author: Votre Nom
 */

// Empêcher l'accès direct
if (!defined('ABSPATH')) {
    exit;
}

class JuridiqueIntegration {
    
    public function __init() {
        add_action('init', array($this, 'init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('wp_ajax_juridique_sync', array($this, 'handle_sync_request'));
        add_action('wp_ajax_nopriv_juridique_sync', array($this, 'handle_sync_request'));
        add_shortcode('juridique_dashboard', array($this, 'display_dashboard_shortcode'));
        add_shortcode('juridique_stats', array($this, 'display_stats_shortcode'));
    }
    
    public function init() {
        // Créer les tables personnalisées si nécessaire
        $this->create_custom_tables();
        
        // Ajouter les rôles personnalisés
        $this->add_custom_roles();
        
        // Enregistrer les types de contenu personnalisés
        $this->register_custom_post_types();
    }
    
    public function enqueue_scripts() {
        wp_enqueue_script(
            'juridique-integration',
            plugin_dir_url(__FILE__) . 'assets/js/integration.js',
            array('jquery'),
            '1.0.0',
            true
        );
        
        wp_localize_script('juridique-integration', 'juridique_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('juridique_nonce'),
            'app_url' => defined('JURIDIQUE_APP_URL') ? JURIDIQUE_APP_URL : ''
        ));
        
        wp_enqueue_style(
            'juridique-integration',
            plugin_dir_url(__FILE__) . 'assets/css/integration.css',
            array(),
            '1.0.0'
        );
    }
    
    private function create_custom_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Table pour synchroniser les données avec l'application principale
        $table_name = $wpdb->prefix . 'juridique_sync';
        
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            entity_type varchar(50) NOT NULL,
            entity_id int NOT NULL,
            data longtext NOT NULL,
            last_sync datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY entity_type (entity_type),
            KEY entity_id (entity_id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    private function add_custom_roles() {
        // Ajouter des rôles spécifiques au système juridique
        add_role('avocat', 'Avocat', array(
            'read' => true,
            'edit_posts' => true,
            'edit_published_posts' => true,
            'publish_posts' => true,
            'upload_files' => true,
        ));
        
        add_role('juriste', 'Juriste', array(
            'read' => true,
            'edit_posts' => true,
            'edit_published_posts' => true,
            'publish_posts' => true,
        ));
        
        add_role('client', 'Client', array(
            'read' => true,
        ));
    }
    
    private function register_custom_post_types() {
        // Type de contenu pour les articles juridiques
        register_post_type('article_juridique', array(
            'labels' => array(
                'name' => 'Articles Juridiques',
                'singular_name' => 'Article Juridique',
                'add_new' => 'Ajouter un article',
                'add_new_item' => 'Ajouter un nouvel article juridique',
                'edit_item' => 'Modifier l\'article juridique',
                'new_item' => 'Nouvel article juridique',
                'view_item' => 'Voir l\'article juridique',
                'search_items' => 'Rechercher des articles juridiques',
                'not_found' => 'Aucun article juridique trouvé',
                'not_found_in_trash' => 'Aucun article juridique trouvé dans la corbeille'
            ),
            'public' => true,
            'has_archive' => true,
            'supports' => array('title', 'editor', 'excerpt', 'thumbnail', 'custom-fields'),
            'menu_icon' => 'dashicons-book-alt',
            'rewrite' => array('slug' => 'articles-juridiques')
        ));
        
        // Type de contenu pour les actualités du cabinet
        register_post_type('actualite_cabinet', array(
            'labels' => array(
                'name' => 'Actualités Cabinet',
                'singular_name' => 'Actualité Cabinet',
                'add_new' => 'Ajouter une actualité',
                'add_new_item' => 'Ajouter une nouvelle actualité',
                'edit_item' => 'Modifier l\'actualité',
                'new_item' => 'Nouvelle actualité',
                'view_item' => 'Voir l\'actualité',
                'search_items' => 'Rechercher des actualités',
                'not_found' => 'Aucune actualité trouvée',
                'not_found_in_trash' => 'Aucune actualité trouvée dans la corbeille'
            ),
            'public' => true,
            'has_archive' => true,
            'supports' => array('title', 'editor', 'excerpt', 'thumbnail', 'custom-fields'),
            'menu_icon' => 'dashicons-megaphone',
            'rewrite' => array('slug' => 'actualites')
        ));
    }
    
    public function handle_sync_request() {
        // Vérifier le nonce pour la sécurité
        if (!wp_verify_nonce($_POST['nonce'], 'juridique_nonce')) {
            wp_die('Erreur de sécurité');
        }
        
        $action = sanitize_text_field($_POST['sync_action']);
        $data = json_decode(stripslashes($_POST['data']), true);
        
        switch ($action) {
            case 'sync_affaires':
                $this->sync_affaires($data);
                break;
            case 'sync_avocats':
                $this->sync_avocats($data);
                break;
            case 'get_stats':
                $this->get_juridique_stats();
                break;
        }
        
        wp_die();
    }
    
    private function sync_affaires($affaires) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'juridique_sync';
        
        foreach ($affaires as $affaire) {
            $wpdb->replace(
                $table_name,
                array(
                    'entity_type' => 'affaire',
                    'entity_id' => $affaire['id'],
                    'data' => json_encode($affaire)
                ),
                array('%s', '%d', '%s')
            );
        }
        
        wp_send_json_success('Affaires synchronisées avec succès');
    }
    
    private function sync_avocats($avocats) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'juridique_sync';
        
        foreach ($avocats as $avocat) {
            $wpdb->replace(
                $table_name,
                array(
                    'entity_type' => 'avocat',
                    'entity_id' => $avocat['id'],
                    'data' => json_encode($avocat)
                ),
                array('%s', '%d', '%s')
            );
        }
        
        wp_send_json_success('Avocats synchronisés avec succès');
    }
    
    private function get_juridique_stats() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'juridique_sync';
        
        $stats = array(
            'total_affaires' => $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE entity_type = 'affaire'"),
            'total_avocats' => $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE entity_type = 'avocat'"),
            'last_sync' => $wpdb->get_var("SELECT MAX(last_sync) FROM $table_name")
        );
        
        wp_send_json_success($stats);
    }
    
    public function display_dashboard_shortcode($atts) {
        $atts = shortcode_atts(array(
            'height' => '600px',
            'width' => '100%'
        ), $atts);
        
        $app_url = defined('JURIDIQUE_APP_URL') ? JURIDIQUE_APP_URL : '';
        
        if (empty($app_url)) {
            return '<p>URL de l\'application non configurée.</p>';
        }
        
        return sprintf(
            '<iframe src="%s" width="%s" height="%s" frameborder="0" class="juridique-dashboard-iframe"></iframe>',
            esc_url($app_url),
            esc_attr($atts['width']),
            esc_attr($atts['height'])
        );
    }
    
    public function display_stats_shortcode($atts) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'juridique_sync';
        
        $stats = array(
            'total_affaires' => $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE entity_type = 'affaire'"),
            'total_avocats' => $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE entity_type = 'avocat'"),
            'last_sync' => $wpdb->get_var("SELECT MAX(last_sync) FROM $table_name")
        );
        
        ob_start();
        ?>
        <div class="juridique-stats-widget">
            <h3>Statistiques du Système Juridique</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-number"><?php echo esc_html($stats['total_affaires']); ?></span>
                    <span class="stat-label">Affaires</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number"><?php echo esc_html($stats['total_avocats']); ?></span>
                    <span class="stat-label">Avocats</span>
                </div>
                <div class="stat-item">
                    <span class="stat-date"><?php echo esc_html($stats['last_sync'] ? date('d/m/Y H:i', strtotime($stats['last_sync'])) : 'Jamais'); ?></span>
                    <span class="stat-label">Dernière sync</span>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }

    public function enqueue_admin_scripts($hook) {
        // Charger les scripts seulement sur les pages d'administration du plugin
        if (strpos($hook, 'juridique') === false) {
            return;
        }

        wp_enqueue_script(
            'juridique-admin',
            plugin_dir_url(__FILE__) . 'assets/js/integration.js',
            array('jquery'),
            '1.0.0',
            true
        );

        wp_localize_script('juridique-admin', 'juridique_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('juridique_nonce'),
            'app_url' => defined('JURIDIQUE_APP_URL') ? JURIDIQUE_APP_URL : ''
        ));

        wp_enqueue_style(
            'juridique-admin',
            plugin_dir_url(__FILE__) . 'assets/css/integration.css',
            array(),
            '1.0.0'
        );
    }

    public function add_admin_menu() {
        add_menu_page(
            'Système Juridique',
            'Système Juridique',
            'manage_options',
            'juridique-integration',
            array($this, 'admin_page'),
            'dashicons-balance-scale',
            30
        );

        add_submenu_page(
            'juridique-integration',
            'Synchronisation',
            'Synchronisation',
            'manage_options',
            'juridique-sync',
            array($this, 'sync_page')
        );

        add_submenu_page(
            'juridique-integration',
            'Configuration',
            'Configuration',
            'manage_options',
            'juridique-config',
            array($this, 'config_page')
        );
    }

    public function admin_page() {
        ?>
        <div class="wrap juridique-admin-page">
            <h1>Système de Gestion Juridique</h1>

            <div class="juridique-admin-section">
                <h3>Tableau de bord intégré</h3>
                <p>Accédez directement à votre application de gestion des affaires juridiques :</p>
                <?php echo $this->display_dashboard_shortcode(array('height' => '600px')); ?>
            </div>

            <div class="juridique-admin-section">
                <h3>Statistiques rapides</h3>
                <?php echo $this->display_stats_shortcode(array()); ?>
            </div>
        </div>
        <?php
    }

    public function sync_page() {
        ?>
        <div class="wrap juridique-admin-page">
            <h1>Synchronisation des données</h1>

            <div class="juridique-admin-section">
                <h3>Synchronisation manuelle</h3>
                <p>Synchronisez les données entre votre application et WordPress :</p>

                <div class="juridique-sync-form">
                    <h4>Synchroniser les affaires</h4>
                    <button class="sync-button sync-affaires-btn">Synchroniser les affaires</button>
                    <div class="sync-status"></div>
                </div>

                <div class="juridique-sync-form">
                    <h4>Synchroniser les avocats</h4>
                    <button class="sync-button sync-avocats-btn">Synchroniser les avocats</button>
                    <div class="sync-status"></div>
                </div>

                <div class="juridique-sync-form">
                    <h4>Synchronisation complète</h4>
                    <button class="sync-button sync-all-btn">Tout synchroniser</button>
                    <div class="sync-status"></div>
                </div>
            </div>

            <div class="juridique-admin-section">
                <h3>Statistiques de synchronisation</h3>
                <?php echo $this->display_stats_shortcode(array()); ?>
                <button class="sync-button refresh-stats-btn">Actualiser les statistiques</button>
            </div>
        </div>
        <?php
    }

    public function config_page() {
        // Traitement du formulaire de configuration
        if (isset($_POST['submit']) && wp_verify_nonce($_POST['juridique_config_nonce'], 'juridique_config')) {
            update_option('juridique_app_url', sanitize_url($_POST['app_url']));
            update_option('juridique_api_key', sanitize_text_field($_POST['api_key']));
            update_option('juridique_auto_sync', isset($_POST['auto_sync']) ? 1 : 0);
            update_option('juridique_sync_interval', intval($_POST['sync_interval']));

            echo '<div class="notice notice-success"><p>Configuration sauvegardée avec succès.</p></div>';
        }

        // Récupérer les options actuelles
        $app_url = get_option('juridique_app_url', defined('JURIDIQUE_APP_URL') ? JURIDIQUE_APP_URL : '');
        $api_key = get_option('juridique_api_key', '');
        $auto_sync = get_option('juridique_auto_sync', 1);
        $sync_interval = get_option('juridique_sync_interval', 5);

        ?>
        <div class="wrap juridique-admin-page">
            <h1>Configuration de l'intégration</h1>

            <form method="post" action="">
                <?php wp_nonce_field('juridique_config', 'juridique_config_nonce'); ?>

                <div class="juridique-admin-section">
                    <h3>Configuration de base</h3>

                    <table class="form-table">
                        <tr>
                            <th scope="row">URL de l'application</th>
                            <td>
                                <input type="url" name="app_url" value="<?php echo esc_attr($app_url); ?>" class="regular-text" />
                                <p class="description">URL complète de votre application de gestion juridique (ex: http://localhost/juridique-app)</p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Clé API</th>
                            <td>
                                <input type="text" name="api_key" value="<?php echo esc_attr($api_key); ?>" class="regular-text" />
                                <p class="description">Clé de sécurité pour l'API (optionnel)</p>
                            </td>
                        </tr>
                    </table>
                </div>

                <div class="juridique-admin-section">
                    <h3>Synchronisation automatique</h3>

                    <table class="form-table">
                        <tr>
                            <th scope="row">Activer la synchronisation automatique</th>
                            <td>
                                <input type="checkbox" name="auto_sync" value="1" <?php checked($auto_sync, 1); ?> />
                                <p class="description">Synchronise automatiquement les données à intervalles réguliers</p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Intervalle de synchronisation</th>
                            <td>
                                <select name="sync_interval">
                                    <option value="1" <?php selected($sync_interval, 1); ?>>1 minute</option>
                                    <option value="5" <?php selected($sync_interval, 5); ?>>5 minutes</option>
                                    <option value="15" <?php selected($sync_interval, 15); ?>>15 minutes</option>
                                    <option value="30" <?php selected($sync_interval, 30); ?>>30 minutes</option>
                                    <option value="60" <?php selected($sync_interval, 60); ?>>1 heure</option>
                                </select>
                                <p class="description">Fréquence de synchronisation automatique</p>
                            </td>
                        </tr>
                    </table>
                </div>

                <?php submit_button('Sauvegarder la configuration'); ?>
            </form>
        </div>
        <?php
    }
}

// Initialiser le plugin
$juridique_integration = new JuridiqueIntegration();
$juridique_integration->__init();

USE suivi_affaires_juridiques;

-- Vérifier si la colonne password_hash existe
SET @column_exists = (
    SELECT COUNT(*)
    FROM information_schema.COLUMNS
    WHERE TABLE_SCHEMA = 'suivi_affaires_juridiques'
    AND TABLE_NAME = 'users'
    AND COLUMN_NAME = 'password_hash'
);

-- Si password_hash existe mais pas password, renommer la colonne
SET @sql = IF(@column_exists > 0,
    'ALTER TABLE users CHANGE COLUMN password_hash password VARCHAR(255) NOT NULL',
    'SELECT 1'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Si ni password ni password_hash n'existe, ajouter password
SET @both_missing = (
    SELECT COUNT(*) = 0
    FROM information_schema.COLUMNS
    WHERE TABLE_SCHEMA = 'suivi_affaires_juridiques'
    AND TABLE_NAME = 'users'
    AND COLUMN_NAME IN ('password', 'password_hash')
);

SET @sql = IF(@both_missing = 1,
    'ALTER TABLE users ADD COLUMN password VARCHAR(255) NOT NULL AFTER username',
    'SELECT 1'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
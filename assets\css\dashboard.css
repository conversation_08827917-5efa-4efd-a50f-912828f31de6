/* Styles pour le tableau de bord amélio<PERSON> */

/* Styles généraux */
.dashboard-section {
    padding: 20px 0;
}

.dashboard-title {
    margin-bottom: 25px;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dashboard-title h2 {
    margin: 0;
    font-weight: 600;
    color: #333;
}

.dashboard-actions {
    display: flex;
    gap: 10px;
}

/* Cartes KPI */
.kpi-card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
    height: 100%;
}

.kpi-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.kpi-card .card-body {
    padding: 20px;
}

.kpi-card .card-title {
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    color: #6c757d;
    margin-bottom: 15px;
}

.kpi-card .kpi-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.kpi-card .kpi-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.kpi-card .kpi-trend {
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    margin-top: 10px;
}

.kpi-card .kpi-trend i {
    margin-right: 5px;
}

.kpi-card .kpi-trend.up {
    color: #28a745;
}

.kpi-card .kpi-trend.down {
    color: #dc3545;
}

.kpi-card .kpi-trend.neutral {
    color: #6c757d;
}

/* Cartes de graphiques */
.chart-card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 25px;
    overflow: hidden;
}

.chart-card .card-header {
    background-color: #fff;
    border-bottom: 1px solid #f0f0f0;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-card .card-header h5 {
    margin: 0;
    font-weight: 600;
    color: #333;
}

.chart-card .card-header .chart-actions {
    display: flex;
    gap: 10px;
}

.chart-card .card-body {
    padding: 20px;
    height: 300px;
}

/* Filtres du tableau de bord */
.dashboard-filters {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 25px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dashboard-filters .form-label {
    font-weight: 600;
    font-size: 0.85rem;
    color: #495057;
}

.dashboard-filters .btn-filter {
    margin-top: 24px;
}

/* Tableau des affaires à haut risque */
.high-risk-table {
    margin-top: 20px;
}

.high-risk-table .table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.high-risk-table .table thead th {
    background-color: #dc3545;
    color: white;
    font-weight: 600;
    border: none;
}

.high-risk-table .table tbody tr:hover {
    background-color: #fff9f9;
}

.high-risk-badge {
    background-color: #dc3545;
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* Alertes */
.alert-card {
    border-left: 4px solid #ffc107;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 15px;
    background-color: #fff;
}

.alert-card.warning {
    border-left-color: #ffc107;
}

.alert-card.danger {
    border-left-color: #dc3545;
}

.alert-card.success {
    border-left-color: #28a745;
}

.alert-card .alert-header {
    padding: 12px 15px;
    border-bottom: 1px solid #f0f0f0;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.alert-card .alert-header i {
    margin-right: 10px;
}

.alert-card .alert-body {
    padding: 15px;
}

.alert-card .alert-actions {
    padding: 10px 15px;
    background-color: #f8f9fa;
    text-align: right;
}

/* Résumé financier */
.financial-summary {
    background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
    color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 25px;
}

.financial-summary .card-header {
    background-color: rgba(255, 255, 255, 0.1);
    border-bottom: none;
    padding: 15px 20px;
}

.financial-summary .card-header h5 {
    margin: 0;
    font-weight: 600;
    color: white;
}

.financial-summary .card-body {
    padding: 20px;
}

.financial-summary .financial-item {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    height: 100%;
}

.financial-summary .financial-item h6 {
    font-size: 0.9rem;
    margin-bottom: 10px;
    opacity: 0.8;
}

.financial-summary .financial-item h4 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

/* Affaires à haut risque */
.high-risk-section {
    margin-bottom: 25px;
}

.high-risk-card {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.high-risk-card .card-header {
    background-color: #dc3545;
    color: white;
    padding: 15px 20px;
}

.high-risk-card .card-header h5 {
    margin: 0;
    font-weight: 600;
}

.high-risk-card .card-body {
    padding: 0;
}

.high-risk-card .list-group-item {
    border-left: none;
    border-right: none;
    padding: 15px 20px;
}

.high-risk-card .list-group-item:first-child {
    border-top: none;
}

.high-risk-card .list-group-item:last-child {
    border-bottom: none;
}

/* Responsive */
@media (max-width: 768px) {
    .kpi-card .kpi-value {
        font-size: 1.5rem;
    }
    
    .kpi-card .kpi-icon {
        font-size: 2rem;
    }
    
    .chart-card .card-body {
        height: 250px;
    }
    
    .dashboard-filters .btn-filter {
        margin-top: 10px;
        width: 100%;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease forwards;
}

.delay-1 { animation-delay: 0.1s; }
.delay-2 { animation-delay: 0.2s; }
.delay-3 { animation-delay: 0.3s; }
.delay-4 { animation-delay: 0.4s; }

/* Styles pour le tableau de bord amélio<PERSON> */

/* <PERSON> généraux */
.dashboard-section {
    padding: 20px 0;
}

/* Animations */
.animate-fade-in {
    animation: fadeIn 0.6s ease-in-out;
    animation-fill-mode: both;
}

.delay-1 { animation-delay: 0.1s; }
.delay-2 { animation-delay: 0.2s; }
.delay-3 { animation-delay: 0.3s; }
.delay-4 { animation-delay: 0.4s; }

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dashboard-title {
    margin-bottom: 25px;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dashboard-title h2 {
    margin: 0;
    font-weight: 600;
    color: #333;
}

.dashboard-actions {
    display: flex;
    gap: 10px;
}

/* Cartes KPI améliorées */
.kpi-card {
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    overflow: hidden;
    height: 100%;
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
}

.kpi-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #007bff, #0056b3);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.kpi-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.kpi-card:hover::before {
    opacity: 1;
}

.kpi-card .card-body {
    padding: 24px;
    position: relative;
}

.kpi-card .card-title {
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    color: #6c757d;
    margin-bottom: 16px;
    letter-spacing: 0.5px;
}

.kpi-card .kpi-value {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 8px;
    line-height: 1.2;
}

.kpi-card .kpi-icon {
    font-size: 2.8rem;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.kpi-card:hover .kpi-icon {
    opacity: 1;
    transform: scale(1.1);
}

.kpi-card .kpi-trend {
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    margin-top: 12px;
    padding: 6px 10px;
    border-radius: 20px;
    background-color: rgba(0, 0, 0, 0.03);
    font-weight: 500;
}

.kpi-card .kpi-trend i {
    margin-right: 6px;
    font-size: 0.9rem;
}

.kpi-card .kpi-trend.up {
    color: #28a745;
    background-color: rgba(40, 167, 69, 0.1);
}

.kpi-card .kpi-trend.down {
    color: #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
}

.kpi-card .kpi-trend.neutral {
    color: #6c757d;
    background-color: rgba(108, 117, 125, 0.1);
}

/* Cartes de graphiques améliorées */
.chart-card {
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 30px;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.chart-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.chart-card .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    padding: 18px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-card .card-header h5 {
    margin: 0;
    font-weight: 600;
    color: #495057;
    font-size: 1.1rem;
}

.chart-card .card-header .chart-actions {
    display: flex;
    gap: 8px;
}

.chart-card .card-header .chart-actions .btn {
    padding: 6px 10px;
    font-size: 0.8rem;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.chart-card .card-header .chart-actions .btn:hover {
    transform: scale(1.05);
}

.chart-card .card-body {
    padding: 24px;
    height: 350px;
    position: relative;
    background-color: #fff;
}

.chart-card .card-body canvas {
    max-height: 100%;
    width: 100% !important;
}

/* Filtres du tableau de bord */
.dashboard-filters {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 25px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dashboard-filters .form-label {
    font-weight: 600;
    font-size: 0.85rem;
    color: #495057;
}

.dashboard-filters .btn-filter {
    margin-top: 24px;
}

/* Tableau des affaires à haut risque */
.high-risk-table {
    margin-top: 20px;
}

.high-risk-table .table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.high-risk-table .table thead th {
    background-color: #dc3545;
    color: white;
    font-weight: 600;
    border: none;
}

.high-risk-table .table tbody tr:hover {
    background-color: #fff9f9;
}

.high-risk-badge {
    background-color: #dc3545;
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* Alertes */
.alert-card {
    border-left: 4px solid #ffc107;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 15px;
    background-color: #fff;
}

.alert-card.warning {
    border-left-color: #ffc107;
}

.alert-card.danger {
    border-left-color: #dc3545;
}

.alert-card.success {
    border-left-color: #28a745;
}

.alert-card .alert-header {
    padding: 12px 15px;
    border-bottom: 1px solid #f0f0f0;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.alert-card .alert-header i {
    margin-right: 10px;
}

.alert-card .alert-body {
    padding: 15px;
}

.alert-card .alert-actions {
    padding: 10px 15px;
    background-color: #f8f9fa;
    text-align: right;
}

/* Résumé financier */
.financial-summary {
    background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
    color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 25px;
}

.financial-summary .card-header {
    background-color: rgba(255, 255, 255, 0.1);
    border-bottom: none;
    padding: 15px 20px;
}

.financial-summary .card-header h5 {
    margin: 0;
    font-weight: 600;
    color: white;
}

.financial-summary .card-body {
    padding: 20px;
}

.financial-summary .financial-item {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    height: 100%;
}

.financial-summary .financial-item h6 {
    font-size: 0.9rem;
    margin-bottom: 10px;
    opacity: 0.8;
}

.financial-summary .financial-item h4 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

/* Affaires à haut risque */
.high-risk-section {
    margin-bottom: 25px;
}

.high-risk-card {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.high-risk-card .card-header {
    background-color: #dc3545;
    color: white;
    padding: 15px 20px;
}

.high-risk-card .card-header h5 {
    margin: 0;
    font-weight: 600;
}

.high-risk-card .card-body {
    padding: 0;
}

.high-risk-card .list-group-item {
    border-left: none;
    border-right: none;
    padding: 15px 20px;
}

.high-risk-card .list-group-item:first-child {
    border-top: none;
}

.high-risk-card .list-group-item:last-child {
    border-bottom: none;
}

/* Styles pour les listes d'activités et performances */
.list-group-flush .list-group-item {
    border-left: none;
    border-right: none;
    padding: 12px 0;
}

.list-group-flush .list-group-item:first-child {
    border-top: none;
}

.list-group-flush .list-group-item:last-child {
    border-bottom: none;
}

.performance-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
}

.performance-item .lawyer-info {
    display: flex;
    align-items: center;
}

.performance-item .lawyer-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #007bff, #0056b3);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    margin-right: 12px;
}

.performance-item .lawyer-details h6 {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
}

.performance-item .lawyer-details small {
    color: #6c757d;
    font-size: 0.8rem;
}

.performance-item .performance-stats {
    text-align: right;
}

.performance-item .performance-stats .stat-value {
    font-weight: 700;
    font-size: 1.1rem;
}

.performance-item .performance-stats .stat-label {
    font-size: 0.75rem;
    color: #6c757d;
    text-transform: uppercase;
}

/* Styles pour les activités récentes */
.activity-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-item .activity-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 0.8rem;
}

.activity-item .activity-icon.create {
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.activity-item .activity-icon.update {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.activity-item .activity-icon.delete {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.activity-item .activity-content {
    flex: 1;
}

.activity-item .activity-content .activity-text {
    font-size: 0.9rem;
    margin: 0;
    line-height: 1.4;
}

.activity-item .activity-content .activity-time {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 2px;
}

/* Styles pour les affaires à haut risque */
.high-risk-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid rgba(220, 53, 69, 0.1);
}

.high-risk-item:last-child {
    border-bottom: none;
}

.high-risk-item .case-info h6 {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: #495057;
}

.high-risk-item .case-info .case-details {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 2px;
}

.high-risk-item .risk-amount {
    text-align: right;
}

.high-risk-item .risk-amount .amount {
    font-weight: 700;
    font-size: 1.1rem;
    color: #dc3545;
}

.high-risk-item .risk-amount .risk-badge {
    background-color: #dc3545;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    margin-top: 4px;
    display: inline-block;
}

/* Responsive amélioré */
@media (max-width: 768px) {
    .kpi-card .kpi-value {
        font-size: 1.8rem;
    }

    .kpi-card .kpi-icon {
        font-size: 2.2rem;
    }

    .chart-card .card-body {
        height: 280px;
        padding: 16px;
    }

    .dashboard-filters .btn-filter {
        margin-top: 10px;
        width: 100%;
    }

    .dashboard-filters {
        padding: 12px;
    }

    .dashboard-title {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .dashboard-actions {
        width: 100%;
        justify-content: flex-end;
    }

    .performance-item,
    .high-risk-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .performance-item .performance-stats,
    .high-risk-item .risk-amount {
        text-align: left;
        width: 100%;
    }
}

@media (max-width: 576px) {
    .kpi-card .card-body {
        padding: 16px;
    }

    .chart-card .card-body {
        height: 250px;
        padding: 12px;
    }

    .financial-summary .financial-item {
        padding: 12px;
    }

    .dashboard-filters .row {
        gap: 10px;
    }

    .dashboard-filters .col-md-3 {
        margin-bottom: 10px;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease forwards;
}

.delay-1 { animation-delay: 0.1s; }
.delay-2 { animation-delay: 0.2s; }
.delay-3 { animation-delay: 0.3s; }
.delay-4 { animation-delay: 0.4s; }

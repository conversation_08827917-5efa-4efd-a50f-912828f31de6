-- Script pour corriger directement la structure de la table users
USE suivi_affaires_juridiques;

-- Renommer la colonne password_hash en password si elle existe
ALTER TABLE users CHANGE COLUMN password_hash password VARCHAR(255) NOT NULL;

-- Ajouter la colonne email si elle n'existe pas
ALTER TABLE users ADD COLUMN IF NOT EXISTS email VARCHAR(100) NOT NULL AFTER password;

-- Ajouter la colonne last_login si elle n'existe pas
ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login DATETIME NULL AFTER role;

-- Ajouter la colonne account_locked si elle n'existe pas
ALTER TABLE users ADD COLUMN IF NOT EXISTS account_locked TINYINT(1) DEFAULT 0 AFTER last_login;

-- Ajouter la colonne locked_until si elle n'existe pas
ALTER TABLE users ADD COLUMN IF NOT EXISTS locked_until DATETIME NULL AFTER account_locked;

-- Ajouter la colonne created_at si elle n'existe pas
ALTER TABLE users ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP AFTER locked_until;

-- Ajouter la colonne updated_at si elle n'existe pas
ALTER TABLE users ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_at;

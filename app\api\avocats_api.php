<?php
// API pour la gestion des avocats

// Autoriser les requêtes CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

// Si c'est une requête OPTIONS (preflight), renvoyer 200 OK
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Démarrer la session
session_start();

// Inclure les fichiers nécessaires
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../auth.php';

// Vérifier si l'utilisateur est connecté
if (!isLoggedIn()) {
    jsonResponse(['success' => false, 'message' => 'Non authentifié'], 401);
}

// Récupérer la méthode HTTP et l'action
$method = $_SERVER['REQUEST_METHOD'];
$action = isset($_GET['action']) ? $_GET['action'] : '';

// Traiter la requête en fonction de la méthode et de l'action
switch ($method) {
    case 'GET':
        switch ($action) {
            case 'list':
                // Récupérer la liste des avocats
                try {
                    $pdo = getDbConnection();
                    $stmt = $pdo->query("SELECT * FROM avocats ORDER BY nom");
                    $avocats = $stmt->fetchAll();

                    // Convertir les dates au format YYYY-MM-DD pour la compatibilité avec le frontend
                    foreach ($avocats as &$avocat) {
                        $avocat['dateSignature'] = $avocat['date_signature'];
                        $avocat['validiteContrat'] = $avocat['validite_contrat'];
                        unset($avocat['date_signature']);
                        unset($avocat['validite_contrat']);
                    }

                    jsonResponse(['success' => true, 'avocats' => $avocats]);
                } catch (PDOException $e) {
                    jsonResponse(['success' => false, 'message' => 'Erreur lors de la récupération des avocats: ' . $e->getMessage()], 500);
                }
                break;

            case 'get':
                // Vérifier que l'ID de l'avocat est fourni
                if (!isset($_GET['id'])) {
                    jsonResponse(['success' => false, 'message' => 'ID de l\'avocat requis'], 400);
                }

                // Récupérer les informations d'un avocat spécifique
                try {
                    $pdo = getDbConnection();
                    $stmt = $pdo->prepare("SELECT * FROM avocats WHERE id = ?");
                    $stmt->execute([$_GET['id']]);
                    $avocat = $stmt->fetch();

                    if (!$avocat) {
                        jsonResponse(['success' => false, 'message' => 'Avocat non trouvé'], 404);
                    }

                    // Convertir les dates au format YYYY-MM-DD pour la compatibilité avec le frontend
                    $avocat['dateSignature'] = $avocat['date_signature'];
                    $avocat['validiteContrat'] = $avocat['validite_contrat'];
                    unset($avocat['date_signature']);
                    unset($avocat['validite_contrat']);

                    jsonResponse(['success' => true, 'avocat' => $avocat]);
                } catch (PDOException $e) {
                    jsonResponse(['success' => false, 'message' => 'Erreur lors de la récupération de l\'avocat: ' . $e->getMessage()], 500);
                }
                break;

            default:
                jsonResponse(['success' => false, 'message' => 'Action non reconnue'], 400);
        }
        break;

    case 'POST':
        // Récupérer les données JSON de la requête
        $data = json_decode(file_get_contents('php://input'), true);

        switch ($action) {
            case 'create':
                // Vérifier que les données nécessaires sont présentes
                if (!isset($data['nom']) || !isset($data['agrement']) || !isset($data['email']) ||
                    !isset($data['dateSignature']) || !isset($data['validiteContrat'])) {
                    jsonResponse(['success' => false, 'message' => 'Données incomplètes'], 400);
                }

                // Créer un nouvel avocat
                try {
                    $pdo = getDbConnection();

                    // Vérifier si l'agrément existe déjà
                    $checkStmt = $pdo->prepare("SELECT id FROM avocats WHERE agrement = ?");
                    $checkStmt->execute([$data['agrement']]);
                    if ($checkStmt->fetch()) {
                        jsonResponse(['success' => false, 'message' => 'Cet agrément existe déjà'], 400);
                    }

                    // Vérifier si l'email existe déjà
                    $checkStmt = $pdo->prepare("SELECT id FROM avocats WHERE email = ?");
                    $checkStmt->execute([$data['email']]);
                    if ($checkStmt->fetch()) {
                        jsonResponse(['success' => false, 'message' => 'Cet email est déjà utilisé'], 400);
                    }

                    // Insérer le nouvel avocat
                    $stmt = $pdo->prepare("INSERT INTO avocats (nom, agrement, adresse, telephone, email, date_signature, validite_contrat)
                                          VALUES (?, ?, ?, ?, ?, ?, ?)");
                    $stmt->execute([
                        $data['nom'],
                        $data['agrement'],
                        $data['adresse'] ?? null,
                        $data['telephone'] ?? null,
                        $data['email'],
                        $data['dateSignature'],
                        $data['validiteContrat']
                    ]);

                    $avocatId = $pdo->lastInsertId();

                    // Journaliser la création de l'avocat
                    logActivity($_SESSION['username'], 'create', 'avocat', $avocatId, "Création de l'avocat {$data['nom']}");

                    jsonResponse([
                        'success' => true,
                        'message' => 'Avocat créé avec succès',
                        'id' => $avocatId
                    ]);
                } catch (PDOException $e) {
                    jsonResponse(['success' => false, 'message' => 'Erreur lors de la création de l\'avocat: ' . $e->getMessage()], 500);
                }
                break;

            case 'update':
                // Vérifier que les données nécessaires sont présentes
                if (!isset($data['id']) || !isset($data['nom']) || !isset($data['agrement']) ||
                    !isset($data['email']) || !isset($data['dateSignature']) || !isset($data['validiteContrat'])) {
                    jsonResponse(['success' => false, 'message' => 'Données incomplètes'], 400);
                }

                // Mettre à jour un avocat existant
                try {
                    $pdo = getDbConnection();

                    // Vérifier si l'avocat existe
                    $checkStmt = $pdo->prepare("SELECT id FROM avocats WHERE id = ?");
                    $checkStmt->execute([$data['id']]);
                    if (!$checkStmt->fetch()) {
                        jsonResponse(['success' => false, 'message' => 'Avocat non trouvé'], 404);
                    }

                    // Vérifier si l'agrément existe déjà pour un autre avocat
                    $checkStmt = $pdo->prepare("SELECT id FROM avocats WHERE agrement = ? AND id != ?");
                    $checkStmt->execute([$data['agrement'], $data['id']]);
                    if ($checkStmt->fetch()) {
                        jsonResponse(['success' => false, 'message' => 'Cet agrément est déjà utilisé par un autre avocat'], 400);
                    }

                    // Vérifier si l'email existe déjà pour un autre avocat
                    $checkStmt = $pdo->prepare("SELECT id FROM avocats WHERE email = ? AND id != ?");
                    $checkStmt->execute([$data['email'], $data['id']]);
                    if ($checkStmt->fetch()) {
                        jsonResponse(['success' => false, 'message' => 'Cet email est déjà utilisé par un autre avocat'], 400);
                    }

                    // Mettre à jour l'avocat
                    $stmt = $pdo->prepare("UPDATE avocats SET nom = ?, agrement = ?, adresse = ?, telephone = ?,
                                          email = ?, date_signature = ?, validite_contrat = ? WHERE id = ?");
                    $stmt->execute([
                        $data['nom'],
                        $data['agrement'],
                        $data['adresse'] ?? null,
                        $data['telephone'] ?? null,
                        $data['email'],
                        $data['dateSignature'],
                        $data['validiteContrat'],
                        $data['id']
                    ]);

                    // Journaliser la mise à jour de l'avocat
                    logActivity($_SESSION['username'], 'update', 'avocat', $data['id'], "Mise à jour de l'avocat {$data['nom']}");

                    jsonResponse([
                        'success' => true,
                        'message' => 'Avocat mis à jour avec succès'
                    ]);
                } catch (PDOException $e) {
                    jsonResponse(['success' => false, 'message' => 'Erreur lors de la mise à jour de l\'avocat: ' . $e->getMessage()], 500);
                }
                break;

            case 'delete':
                // Vérifier que l'ID de l'avocat est fourni
                if (!isset($data['id'])) {
                    jsonResponse(['success' => false, 'message' => 'ID de l\'avocat requis'], 400);
                }

                // Supprimer un avocat
                try {
                    $pdo = getDbConnection();

                    // Vérifier si l'avocat existe
                    $checkStmt = $pdo->prepare("SELECT nom FROM avocats WHERE id = ?");
                    $checkStmt->execute([$data['id']]);
                    $avocat = $checkStmt->fetch();

                    if (!$avocat) {
                        jsonResponse(['success' => false, 'message' => 'Avocat non trouvé'], 404);
                    }

                    // Vérifier si l'avocat est utilisé dans des affaires
                    $checkStmt = $pdo->prepare("SELECT COUNT(*) FROM affaires WHERE maitre = ?");
                    $checkStmt->execute([$data['id']]);
                    $count = $checkStmt->fetchColumn();

                    if ($count > 0) {
                        jsonResponse(['success' => false, 'message' => 'Cet avocat ne peut pas être supprimé car il est associé à une ou plusieurs affaires'], 400);
                    }

                    // Supprimer l'avocat
                    $stmt = $pdo->prepare("DELETE FROM avocats WHERE id = ?");
                    $stmt->execute([$data['id']]);

                    // Journaliser la suppression de l'avocat
                    logActivity($_SESSION['username'], 'delete', 'avocat', $data['id'], "Suppression de l'avocat {$avocat['nom']}");

                    jsonResponse([
                        'success' => true,
                        'message' => 'Avocat supprimé avec succès'
                    ]);
                } catch (PDOException $e) {
                    jsonResponse(['success' => false, 'message' => 'Erreur lors de la suppression de l\'avocat: ' . $e->getMessage()], 500);
                }
                break;

            default:
                jsonResponse(['success' => false, 'message' => 'Action non reconnue'], 400);
        }
        break;

    default:
        jsonResponse(['success' => false, 'message' => 'Méthode non autorisée'], 405);
}

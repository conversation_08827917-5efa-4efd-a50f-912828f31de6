const Dashboard = {
    async getStatistiques() {
        try {
            // Vérifier si la base de données est disponible
            if (!window.DB) {
                throw new Error('Base de données non disponible');
            }

            // Récupérer les données avec gestion d'erreur
            let affaires = [];
            let avocats = [];

            try {
                affaires = await DB.getAllAffaires();
                if (!Array.isArray(affaires)) {
                    console.warn('Les affaires récupérées ne sont pas un tableau');
                    affaires = [];
                }
            } catch (affairesError) {
                console.error('Erreur lors de la récupération des affaires:', affairesError);
                affaires = [];
            }

            try {
                avocats = await DB.getAllAvocats();
                if (!Array.isArray(avocats)) {
                    console.warn('Les avocats récupérés ne sont pas un tableau');
                    avocats = [];
                }
            } catch (avocatsError) {
                console.error('Erreur lors de la récupération des avocats:', avocatsError);
                avocats = [];
            }

            // Calculer les statistiques avec des vérifications de sécurité
            const affairesEnCours = affaires.filter(a => a && a.statut === 'en-cours').length;
            const affairesJugees = affaires.filter(a => a && a.statut && a.statut.startsWith('jugee')).length;
            const affairesGagnees = affaires.filter(a => a && a.statut === 'jugee-faveur').length;
            const affairesPerdues = affaires.filter(a => a && a.statut === 'jugee-defaveur').length;
            const montantTotal = affaires.reduce((sum, a) => sum + (a && parseFloat(a.montant) || 0), 0);

            // Calculer les statistiques avancées avec gestion d'erreur
            let evolutionAffaires, performanceAvocats, montantParNature, activitesRecentes, resumeFinancier;

            try {
                evolutionAffaires = this.calculerEvolutionAffaires(affaires);
            } catch (error) {
                console.error('Erreur lors du calcul de l\'evolution des affaires:', error);
                evolutionAffaires = { labels: [], data: [] };
            }

            try {
                performanceAvocats = this.calculerPerformanceAvocats(affaires, avocats);
            } catch (error) {
                console.error('Erreur lors du calcul des performances des avocats:', error);
                performanceAvocats = [];
            }

            try {
                montantParNature = this.calculerMontantParNature(affaires);
            } catch (error) {
                console.error('Erreur lors du calcul des montants par nature:', error);
                montantParNature = {};
            }

            try {
                activitesRecentes = this.getActivitesRecentes(affaires, avocats);
            } catch (error) {
                console.error('Erreur lors de la récupération des activités récentes:', error);
                activitesRecentes = [];
            }

            try {
                resumeFinancier = this.calculerResumeFinancier(affaires);
            } catch (error) {
                console.error('Erreur lors du calcul du résumé financier:', error);
                resumeFinancier = {
                    montantEnCours: 0,
                    montantGagne: 0,
                    montantPerdu: 0,
                    montantMoyen: 0
                };
            }

            return {
                totalAffaires: affaires.length,
                totalAvocats: avocats.length,
                affairesEnCours,
                affairesJugees,
                affairesGagnees,
                affairesPerdues,
                montantTotal,
                repartitionNature: this.calculerRepartition(affaires, 'nature'),
                repartitionJuridiction: this.calculerRepartition(affaires, 'juridiction'),
                avocatsContratExpirant: this.getAvocatsContratExpirant(avocats),
                // Nouvelles statistiques
                evolutionAffaires,
                performanceAvocats,
                montantParNature,
                activitesRecentes,
                resumeFinancier
            };
        } catch (error) {
            console.error('Erreur lors du chargement des statistiques:', error);
            // Retourner un objet avec des valeurs par défaut au lieu de lancer une exception
            return {
                totalAffaires: 0,
                totalAvocats: 0,
                affairesEnCours: 0,
                affairesJugees: 0,
                affairesGagnees: 0,
                affairesPerdues: 0,
                montantTotal: 0,
                repartitionNature: {},
                repartitionJuridiction: {},
                avocatsContratExpirant: [],
                evolutionAffaires: { labels: [], data: [] },
                performanceAvocats: [],
                montantParNature: {},
                activitesRecentes: [],
                resumeFinancier: {
                    montantEnCours: 0,
                    montantGagne: 0,
                    montantPerdu: 0,
                    montantMoyen: 0
                }
            };
        }
    },

    calculerRepartition(affaires, critere) {
        if (!Array.isArray(affaires) || affaires.length === 0) {
            return {};
        }

        return affaires.reduce((acc, affaire) => {
            if (affaire && affaire[critere]) {
                const valeur = affaire[critere];
                acc[valeur] = (acc[valeur] || 0) + 1;
            } else if (affaire) {
                // Cas où la propriété est manquante
                const valeur = 'Non spécifié';
                acc[valeur] = (acc[valeur] || 0) + 1;
            }
            return acc;
        }, {});
    },

    getAvocatsContratExpirant(avocats) {
        if (!Array.isArray(avocats) || avocats.length === 0) {
            return [];
        }

        const dateLimit = new Date();
        dateLimit.setMonth(dateLimit.getMonth() + 3);

        return avocats.filter(avocat => {
            try {
                if (!avocat || !avocat.validiteContrat) return false;

                const dateValidite = new Date(avocat.validiteContrat);
                if (isNaN(dateValidite.getTime())) return false; // Vérifier si la date est valide

                return dateValidite <= dateLimit;
            } catch (e) {
                console.error('Erreur lors de la vérification de la date de validité du contrat:', e);
                return false;
            }
        });
    },

    calculerEvolutionAffaires(affaires) {
        // Vérifier si le tableau est valide
        if (!Array.isArray(affaires) || affaires.length === 0) {
            return { labels: [], data: [] };
        }

        // Créer un objet pour stocker le nombre d'affaires par mois
        const evolution = {};

        // Filtrer les affaires avec des dates valides
        const affairesValides = affaires.filter(a => a && a.dateEngagement);

        if (affairesValides.length === 0) {
            return { labels: [], data: [] };
        }

        // Trier les affaires par date d'engagement
        const affairesSorted = [...affairesValides].sort((a, b) => {
            try {
                return new Date(a.dateEngagement) - new Date(b.dateEngagement);
            } catch (e) {
                return 0; // En cas d'erreur de date, ne pas modifier l'ordre
            }
        });

        // Regrouper par mois
        affairesSorted.forEach(affaire => {
            try {
                const date = new Date(affaire.dateEngagement);
                if (!isNaN(date.getTime())) { // Vérifier si la date est valide
                    const moisAnnee = `${date.getMonth() + 1}/${date.getFullYear()}`;

                    if (!evolution[moisAnnee]) {
                        evolution[moisAnnee] = 0;
                    }
                    evolution[moisAnnee]++;
                }
            } catch (e) {
                console.error('Erreur lors du traitement de la date:', e);
            }
        });

        // Convertir en format pour Chart.js
        const labels = Object.keys(evolution);
        const data = Object.values(evolution);

        return { labels, data };
    },

    calculerPerformanceAvocats(affaires, avocats) {
        const performance = {};

        // Initialiser les statistiques pour chaque avocat
        avocats.forEach(avocat => {
            // S'assurer que l'ID est une chaîne pour éviter les problèmes d'accès
            const id = String(avocat.id);
            performance[id] = {
                nom: avocat.nom,
                totalAffaires: 0,
                affairesGagnees: 0,
                affairesPerdues: 0,
                tauxReussite: 0,
                montantTotal: 0
            };
        });

        // Calculer les statistiques
        affaires.forEach(affaire => {
            // S'assurer que maitre est une chaîne
            const maitreId = String(affaire.maitre);
            if (performance[maitreId]) {
                performance[maitreId].totalAffaires++;

                if (affaire.statut === 'jugee-faveur') {
                    performance[maitreId].affairesGagnees++;
                } else if (affaire.statut === 'jugee-defaveur') {
                    performance[maitreId].affairesPerdues++;
                }

                performance[maitreId].montantTotal += parseFloat(affaire.montant) || 0;
            }
        });

        // Calculer le taux de réussite
        Object.values(performance).forEach(p => {
            const affairesJugees = p.affairesGagnees + p.affairesPerdues;
            p.tauxReussite = affairesJugees > 0 ? Math.round((p.affairesGagnees / affairesJugees) * 100) : 0;
        });

        return Object.values(performance);
    },

    calculerMontantParNature(affaires) {
        const montantParNature = {};

        affaires.forEach(affaire => {
            if (!montantParNature[affaire.nature]) {
                montantParNature[affaire.nature] = 0;
            }
            montantParNature[affaire.nature] += parseFloat(affaire.montant) || 0;
        });

        return montantParNature;
    },

    getActivitesRecentes(affaires, avocats) {
        // Vérifier si les tableaux sont valides
        if (!Array.isArray(affaires) || !Array.isArray(avocats) || affaires.length === 0) {
            return [];
        }

        // Filtrer les affaires qui ont une date d'engagement valide
        const affairesValides = affaires.filter(a => a && a.dateEngagement);

        // Trier les affaires par date de création (supposons que la date d'engagement est la plus récente)
        const affairesRecentes = [...affairesValides]
            .sort((a, b) => {
                try {
                    return new Date(b.dateEngagement) - new Date(a.dateEngagement);
                } catch (e) {
                    return 0; // En cas d'erreur de date, ne pas modifier l'ordre
                }
            })
            .slice(0, 5); // Prendre les 5 plus récentes

        // Formater les données pour l'affichage
        return affairesRecentes.map(affaire => {
            // Convertir l'ID en chaîne pour la comparaison
            const maitreId = String(affaire.maitre);
            const avocat = avocats.find(a => String(a.id) === maitreId);
            return {
                id: affaire.id || 0,
                numero: affaire.numero || 'Sans numéro',
                objet: affaire.objet || 'Sans objet',
                date: affaire.dateEngagement || new Date().toISOString().split('T')[0],
                statut: affaire.statut || 'en-cours',
                montant: parseFloat(affaire.montant) || 0,
                avocat: avocat ? avocat.nom : 'Non assigné'
            };
        });
    },

    calculerResumeFinancier(affaires) {
        // Vérifier si le tableau est valide
        if (!Array.isArray(affaires) || affaires.length === 0) {
            return {
                montantEnCours: 0,
                montantGagne: 0,
                montantPerdu: 0,
                montantMoyen: 0
            };
        }

        // Calculer les montants par statut
        const montantEnCours = affaires
            .filter(a => a && a.statut === 'en-cours')
            .reduce((sum, a) => sum + (parseFloat(a.montant) || 0), 0);

        const montantGagne = affaires
            .filter(a => a && a.statut === 'jugee-faveur')
            .reduce((sum, a) => sum + (parseFloat(a.montant) || 0), 0);

        const montantPerdu = affaires
            .filter(a => a && a.statut === 'jugee-defaveur')
            .reduce((sum, a) => sum + (parseFloat(a.montant) || 0), 0);

        // Calculer la moyenne des montants
        const montantMoyen = affaires.length > 0 ?
            affaires.reduce((sum, a) => sum + (parseFloat(a.montant) || 0), 0) / affaires.length : 0;

        return {
            montantEnCours,
            montantGagne,
            montantPerdu,
            montantMoyen
        };
    },

    // Fonction pour exporter les données du tableau de bord au format CSV
    exporterDonnees(stats) {
        // Créer les données pour l'export
        let csvContent = "data:text/csv;charset=utf-8,";

        // Ajouter les statistiques générales
        csvContent += "Statistiques générales\n";
        csvContent += "Total affaires," + stats.totalAffaires + "\n";
        csvContent += "Affaires en cours," + stats.affairesEnCours + "\n";
        csvContent += "Affaires jugées," + stats.affairesJugees + "\n";
        csvContent += "Affaires gagnées," + stats.affairesGagnees + "\n";
        csvContent += "Taux de succès," + (stats.affairesJugees > 0 ? Math.round((stats.affairesGagnees / stats.affairesJugees) * 100) : 0) + "%\n";
        csvContent += "Montant total," + stats.montantTotal + "\n\n";

        // Ajouter la répartition par nature
        csvContent += "Répartition par nature\n";
        csvContent += "Nature,Nombre\n";
        Object.entries(stats.repartitionNature).forEach(([nature, nombre]) => {
            csvContent += nature + "," + nombre + "\n";
        });
        csvContent += "\n";

        // Ajouter la répartition par juridiction
        csvContent += "Répartition par juridiction\n";
        csvContent += "Juridiction,Nombre\n";
        Object.entries(stats.repartitionJuridiction).forEach(([juridiction, nombre]) => {
            csvContent += juridiction + "," + nombre + "\n";
        });
        csvContent += "\n";

        // Ajouter les performances des avocats
        csvContent += "Performance des avocats\n";
        csvContent += "Nom,Total affaires,Affaires gagnées,Taux de réussite,Montant total\n";
        stats.performanceAvocats.forEach(p => {
            csvContent += p.nom + "," + p.totalAffaires + "," + p.affairesGagnees + "," + p.tauxReussite + "%," + p.montantTotal + "\n";
        });

        // Encoder l'URI et créer un lien de téléchargement
        const encodedUri = encodeURI(csvContent);
        const link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", "tableau-de-bord-" + new Date().toISOString().split('T')[0] + ".csv");
        document.body.appendChild(link);

        // Déclencher le téléchargement
        link.click();
        document.body.removeChild(link);
    }
};


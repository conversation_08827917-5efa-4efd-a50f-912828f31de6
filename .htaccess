# Activer le module de réécriture d'URL
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /

    # Permettre l'accès depuis le réseau local
    Order Allow,Deny
    Allow from all

    # Ne pas rediriger si le fichier ou le répertoire existe
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d

    # Ne pas rediriger les requêtes qui commencent déjà par /public/
    RewriteCond %{REQUEST_URI} !^/public/

    # Rediriger vers le répertoire public/
    RewriteRule ^(.*)$ public/$1 [L,QSA]
</IfModule>

# Définir le fuseau horaire par défaut
<IfModule mod_php7.c>
    php_value date.timezone "Europe/Paris"
</IfModule>

# Compression Gzip
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/x-javascript application/json
</IfModule>

# Mise en cache des fichiers statiques
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# Protection des fichiers sensibles
<FilesMatch "^(\.htaccess|\.htpasswd|\.git|\.env|composer\.json|composer\.lock|package\.json|package-lock\.json|README\.md|\.ini|\.log|\.sh|\.inc|\.bak)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Empêcher l'accès direct aux répertoires sensibles tout en permettant l'accès aux API
<IfModule mod_rewrite.c>
    RewriteEngine On

    # Permettre l'accès aux API
    RewriteCond %{REQUEST_URI} !^/app/api/.*\.php$
    RewriteCond %{REQUEST_URI} !^/api/.*\.php$

    # Bloquer l'accès aux répertoires app et database
    RewriteRule ^(app|database)($|/) - [F]
</IfModule>

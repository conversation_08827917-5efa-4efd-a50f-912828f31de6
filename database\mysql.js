const mysql = require('mysql2/promise');
const config = require('../config/database.js');

const pool = mysql.createPool(config);

// Fonctions pour les avocats
async function getAllAvocats() {
    try {
        const [rows] = await pool.query('SELECT * FROM avocats ORDER BY nom');
        return rows;
    } catch (error) {
        console.error('Erreur lors de la récupération des avocats:', error);
        throw error;
    }
}

async function getAvocatById(id) {
    try {
        const [rows] = await pool.query('SELECT * FROM avocats WHERE id = ?', [id]);
        return rows[0];
    } catch (error) {
        console.error('Erreur lors de la récupération de l\'avocat:', error);
        throw error;
    }
}

async function addAvocat(avocat) {
    try {
        const [result] = await pool.query(
            'INSERT INTO avocats (nom, agrement, adresse, telephone, email, date_signature, validite_contrat) VALUES (?, ?, ?, ?, ?, ?, ?)',
            [avocat.nom, avocat.agrement, avocat.adresse, avocat.telephone, avocat.email, avocat.dateSignature, avocat.validiteContrat]
        );
        return result.insertId;
    } catch (error) {
        console.error('Erreur lors de l\'ajout de l\'avocat:', error);
        throw error;
    }
}

async function updateAvocat(avocat) {
    try {
        await pool.query(
            'UPDATE avocats SET nom = ?, agrement = ?, adresse = ?, telephone = ?, email = ?, date_signature = ?, validite_contrat = ? WHERE id = ?',
            [avocat.nom, avocat.agrement, avocat.adresse, avocat.telephone, avocat.email, avocat.dateSignature, avocat.validiteContrat, avocat.id]
        );
        return true;
    } catch (error) {
        console.error('Erreur lors de la mise à jour de l\'avocat:', error);
        throw error;
    }
}

async function deleteAvocat(id) {
    try {
        // Vérifier si l'avocat est associé à des affaires
        const [affaires] = await pool.query('SELECT COUNT(*) as count FROM affaires WHERE maitre_id = ?', [id]);
        if (affaires[0].count > 0) {
            throw new Error('Cet avocat ne peut pas être supprimé car il est associé à une ou plusieurs affaires.');
        }
        
        await pool.query('DELETE FROM avocats WHERE id = ?', [id]);
        return true;
    } catch (error) {
        console.error('Erreur lors de la suppression de l\'avocat:', error);
        throw error;
    }
}

// Fonctions pour les affaires
async function getAllAffaires() {
    try {
        const [rows] = await pool.query(`
            SELECT a.*, av.nom as maitre_nom 
            FROM affaires a 
            LEFT JOIN avocats av ON a.maitre_id = av.id 
            ORDER BY a.numero
        `);
        return rows;
    } catch (error) {
        console.error('Erreur lors de la récupération des affaires:', error);
        throw error;
    }
}

// Exporter toutes les fonctions
module.exports = {
    getAllAvocats,
    getAvocatById,
    addAvocat,
    updateAvocat,
    deleteAvocat,
    getAllAffaires,
    // ... autres fonctions à exporter
};
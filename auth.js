// Fonctions d'authentification pour le système de suivi des affaires juridiques avec MySQL

// Fonction pour vérifier les identifiants de connexion
function login(username, password) {
    return fetch('api/auth_api.php?action=login', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ username, password }),
        credentials: 'include'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Stocker les informations de l'utilisateur dans le localStorage
            localStorage.setItem('currentUser', JSON.stringify({
                id: data.user.id,
                username: data.user.username,
                role: data.user.role
            }));
            return data.user;
        } else {
            throw new Error(data.message || 'Nom d\'utilisateur ou mot de passe incorrect');
        }
    });
}

// Fonction pour créer un nouvel utilisateur
function register(username, password) {
    // Générer un email par défaut basé sur le nom d'utilisateur
    const email = `${username}@example.com`;

    return fetch('api/auth_api.php?action=create_user', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ username, password, email, role: 'user' }),
        credentials: 'include'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            return true;
        } else {
            throw new Error(data.message || 'Erreur lors de la création du compte');
        }
    });
}

// Fonction pour déconnecter l'utilisateur
function logout() {
    return fetch('api/auth_api.php?action=logout', {
        method: 'POST',
        credentials: 'include'
    })
    .then(response => response.json())
    .then(data => {
        // Supprimer les informations de l'utilisateur du localStorage
        localStorage.removeItem('currentUser');
        return data;
    })
    .catch(error => {
        console.error('Erreur lors de la déconnexion:', error);
        // Supprimer quand même les informations de l'utilisateur du localStorage
        localStorage.removeItem('currentUser');
        throw error;
    });
}

// Fonction pour vérifier si l'utilisateur est connecté
function checkAuth() {
    return fetch('api/auth_api.php?action=check', {
        method: 'GET',
        credentials: 'include'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Stocker les informations de l'utilisateur dans le localStorage
            localStorage.setItem('currentUser', JSON.stringify({
                id: data.user.id,
                username: data.user.username,
                role: data.user.role
            }));
            return data.user;
        } else {
            throw new Error('Non authentifié');
        }
    });
}

// Fonction pour vérifier si l'utilisateur est connecté (synchrone)
function isLoggedIn() {
    return localStorage.getItem('currentUser') !== null;
}

// Fonction pour obtenir les informations de l'utilisateur connecté
function getCurrentUser() {
    const userJson = localStorage.getItem('currentUser');
    return userJson ? JSON.parse(userJson) : null;
}

// Fonction pour vérifier si l'utilisateur est administrateur
function isAdmin() {
    const user = getCurrentUser();
    return user && user.role === 'admin';
}

// Fonction pour afficher un message d'erreur
function showError(message) {
    const errorMessage = document.getElementById('error-message');
    if (errorMessage) {
        errorMessage.textContent = message;
        errorMessage.style.display = 'block';

        // Masquer le message après 5 secondes
        setTimeout(() => {
            errorMessage.style.display = 'none';
        }, 5000);
    }
}

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', () => {
    // Vérifier si l'utilisateur est déjà connecté
    if (window.location.pathname.includes('login.html')) {
        checkAuth()
            .then(user => {
                // Rediriger vers la page principale si l'utilisateur est déjà connecté
                window.location.href = 'app.html';
            })
            .catch(error => {
                console.log('Utilisateur non connecté, affichage de la page de connexion');
            });
    }

    // Gérer le formulaire de connexion
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', (e) => {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            login(username, password)
                .then(() => {
                    window.location.href = 'app.html';
                })
                .catch((error) => {
                    showError(error.message);
                });
        });
    }

    // Gérer le formulaire d'inscription
    const registerForm = document.getElementById('register-form');
    if (registerForm) {
        registerForm.addEventListener('submit', (e) => {
            e.preventDefault();

            const username = document.getElementById('reg-username').value;
            const password = document.getElementById('reg-password').value;
            const confirmPassword = document.getElementById('reg-confirm-password').value;

            if (password !== confirmPassword) {
                showError('Les mots de passe ne correspondent pas');
                return;
            }

            register(username, password)
                .then(() => {
                    alert('Compte créé avec succès. Vous pouvez maintenant vous connecter.');
                    // Revenir au formulaire de connexion
                    document.getElementById('register-form').style.display = 'none';
                    document.getElementById('login-form').style.display = 'block';
                    document.querySelector('.register-link').style.display = 'block';
                })
                .catch((error) => {
                    showError(error.message);
                });
        });
    }

    // Gérer les liens entre les formulaires
    const registerLink = document.getElementById('register-link');
    if (registerLink) {
        registerLink.addEventListener('click', (e) => {
            e.preventDefault();
            document.getElementById('login-form').style.display = 'none';
            document.getElementById('register-form').style.display = 'block';
            document.querySelector('.register-link').style.display = 'none';
        });
    }

    const backToLoginLink = document.getElementById('back-to-login');
    if (backToLoginLink) {
        backToLoginLink.addEventListener('click', (e) => {
            e.preventDefault();
            document.getElementById('register-form').style.display = 'none';
            document.getElementById('login-form').style.display = 'block';
            document.querySelector('.register-link').style.display = 'block';
        });
    }

    // Gérer le bouton de déconnexion
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', () => {
            logout()
                .then(() => {
                    window.location.href = 'login.html';
                })
                .catch(error => {
                    console.error('Erreur lors de la déconnexion:', error);
                    window.location.href = 'login.html';
                });
        });
    }

    // Afficher les informations de l'utilisateur connecté
    const userInfo = document.getElementById('user-info');
    if (userInfo) {
        const currentUser = getCurrentUser();
        if (currentUser) {
            userInfo.textContent = `Connecté en tant que: ${currentUser.username}`;
        }
    }
});

@echo off
echo Configuration de Laragon pour le projet de suivi des affaires juridiques
echo =====================================================================

REM Vérifier si Laragon est installé
if not exist "C:\laragon\laragon.exe" (
    echo Erreur: Laragon n'est pas installé dans C:\laragon
    echo Veuillez installer Laragon depuis https://laragon.org/download/
    pause
    exit /b 1
)

echo Vérification de l'installation de MySQL...
if not exist "C:\laragon\bin\mysql\mysql-8.0.30-winx64\bin\mysql.exe" (
    echo Erreur: MySQL n'est pas installé dans Laragon
    echo Veuillez installer MySQL depuis le menu Laragon
    pause
    exit /b 1
)

echo Vérification de l'installation de PHP...
if not exist "C:\laragon\bin\php\php-8.1.10-Win32-vs16-x64\php.exe" (
    echo Erreur: PHP n'est pas installé dans Laragon
    echo Veuillez installer PHP depuis le menu Laragon
    pause
    exit /b 1
)

echo Vérification de l'installation d'Apache...
if not exist "C:\laragon\bin\apache\httpd-2.4.54-win64-VS16\bin\httpd.exe" (
    echo Erreur: Apache n'est pas installé dans Laragon
    echo Veuillez installer Apache depuis le menu Laragon
    pause
    exit /b 1
)

echo Création de la base de données...
echo Veuillez entrer le mot de passe MySQL (laissez vide si aucun mot de passe):
set /p mysqlpass=

if "%mysqlpass%"=="" (
    "C:\laragon\bin\mysql\mysql-8.0.30-winx64\bin\mysql.exe" -u root -e "CREATE DATABASE IF NOT EXISTS suivi_affaires_juridiques;"
) else (
    "C:\laragon\bin\mysql\mysql-8.0.30-winx64\bin\mysql.exe" -u root -p%mysqlpass% -e "CREATE DATABASE IF NOT EXISTS suivi_affaires_juridiques;"
)

if %ERRORLEVEL% neq 0 (
    echo Erreur lors de la création de la base de données
    pause
    exit /b 1
)

echo Base de données créée avec succès

echo Importation du schéma de base de données...
if "%mysqlpass%"=="" (
    "C:\laragon\bin\mysql\mysql-8.0.30-winx64\bin\mysql.exe" -u root suivi_affaires_juridiques < database\migrations\schema.sql
) else (
    "C:\laragon\bin\mysql\mysql-8.0.30-winx64\bin\mysql.exe" -u root -p%mysqlpass% suivi_affaires_juridiques < database\migrations\schema.sql
)

if %ERRORLEVEL% neq 0 (
    echo Erreur lors de l'importation du schéma de base de données
    pause
    exit /b 1
)

echo Schéma de base de données importé avec succès

echo Création d'un utilisateur administrateur par défaut...
if "%mysqlpass%"=="" (
    "C:\laragon\bin\mysql\mysql-8.0.30-winx64\bin\mysql.exe" -u root suivi_affaires_juridiques -e "INSERT INTO users (username, password_hash, email, role) VALUES ('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'admin');"
) else (
    "C:\laragon\bin\mysql\mysql-8.0.30-winx64\bin\mysql.exe" -u root -p%mysqlpass% suivi_affaires_juridiques -e "INSERT INTO users (username, password_hash, email, role) VALUES ('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'admin');"
)

if %ERRORLEVEL% neq 0 (
    echo Erreur lors de la création de l'utilisateur administrateur
    pause
    exit /b 1
)

echo Utilisateur administrateur créé avec succès (identifiant: admin, mot de passe: password)

echo Configuration terminée avec succès!
echo Vous pouvez maintenant démarrer Laragon et accéder à votre application via http://localhost/public/
pause

<?php
// Script pour tester l'API des journaux d'activité

// Activer l'affichage des erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Fonction pour effectuer une requête HTTP
function makeRequest($url, $method = 'GET', $data = null) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, false);
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
    }
    
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    
    // Inclure les cookies pour maintenir la session
    curl_setopt($ch, CURLOPT_COOKIEFILE, '');
    curl_setopt($ch, CURLOPT_COOKIEJAR, '');
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    return [
        'code' => $httpCode,
        'response' => $response,
        'error' => $error
    ];
}

// URL de base
$baseUrl = 'http://' . $_SERVER['HTTP_HOST'];

// Tester l'API des journaux d'activité
echo "<h1>Test de l'API des journaux d'activité</h1>";

// 1. Tester l'accès à l'API sans authentification
echo "<h2>1. Test d'accès sans authentification</h2>";
$result = makeRequest($baseUrl . '/api/logs_api.php?action=list');
echo "<pre>Code HTTP: " . $result['code'] . "</pre>";
echo "<pre>Réponse: " . htmlspecialchars($result['response']) . "</pre>";
if ($result['error']) {
    echo "<pre>Erreur: " . $result['error'] . "</pre>";
}

// 2. Tester l'authentification
echo "<h2>2. Test d'authentification</h2>";
$loginData = [
    'username' => 'admin',
    'password' => 'admin123'
];
$result = makeRequest($baseUrl . '/api/auth_api.php?action=login', 'POST', $loginData);
echo "<pre>Code HTTP: " . $result['code'] . "</pre>";
echo "<pre>Réponse: " . htmlspecialchars($result['response']) . "</pre>";
if ($result['error']) {
    echo "<pre>Erreur: " . $result['error'] . "</pre>";
}

// 3. Tester l'accès à l'API après authentification
echo "<h2>3. Test d'accès après authentification</h2>";
$result = makeRequest($baseUrl . '/api/logs_api.php?action=list');
echo "<pre>Code HTTP: " . $result['code'] . "</pre>";
echo "<pre>Réponse: " . htmlspecialchars($result['response']) . "</pre>";
if ($result['error']) {
    echo "<pre>Erreur: " . $result['error'] . "</pre>";
}

// 4. Tester l'accès direct au fichier PHP
echo "<h2>4. Test d'accès direct au fichier PHP</h2>";
echo "<p>URL: " . $baseUrl . '/api/logs_api.php?action=list' . "</p>";
echo "<p>Essayez d'accéder à cette URL directement dans votre navigateur.</p>";

// 5. Vérifier les chemins d'accès
echo "<h2>5. Vérification des chemins d'accès</h2>";
echo "<pre>Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "</pre>";
echo "<pre>Script Filename: " . $_SERVER['SCRIPT_FILENAME'] . "</pre>";
echo "<pre>HTTP Host: " . $_SERVER['HTTP_HOST'] . "</pre>";
echo "<pre>Request URI: " . $_SERVER['REQUEST_URI'] . "</pre>";

// 6. Vérifier si les fichiers existent
echo "<h2>6. Vérification des fichiers</h2>";
$files = [
    '/api/logs_api.php',
    '/app/api/logs_api.php',
    '/config.php',
    '/app/config/config.php',
    '/auth.php',
    '/app/auth.php'
];

foreach ($files as $file) {
    $filePath = $_SERVER['DOCUMENT_ROOT'] . $file;
    echo "<p>Fichier: " . $filePath . " - " . (file_exists($filePath) ? "Existe" : "N'existe pas") . "</p>";
}

// 7. Vérifier la table activity_logs
echo "<h2>7. Vérification de la table activity_logs</h2>";
try {
    require_once __DIR__ . '/config.php';
    $pdo = getDbConnection();
    
    // Vérifier si la table existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'activity_logs'");
    $tableExists = $stmt->rowCount() > 0;
    
    echo "<p>Table activity_logs: " . ($tableExists ? "Existe" : "N'existe pas") . "</p>";
    
    if ($tableExists) {
        // Compter le nombre d'enregistrements
        $stmt = $pdo->query("SELECT COUNT(*) FROM activity_logs");
        $count = $stmt->fetchColumn();
        
        echo "<p>Nombre d'enregistrements: " . $count . "</p>";
        
        // Afficher les 5 derniers enregistrements
        if ($count > 0) {
            $stmt = $pdo->query("SELECT * FROM activity_logs ORDER BY id DESC LIMIT 5");
            $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h3>Les 5 derniers enregistrements:</h3>";
            echo "<pre>" . print_r($logs, true) . "</pre>";
        }
    }
} catch (Exception $e) {
    echo "<p>Erreur lors de la vérification de la table: " . $e->getMessage() . "</p>";
}

// 8. Tester l'ajout d'un enregistrement
echo "<h2>8. Test d'ajout d'un enregistrement</h2>";
try {
    require_once __DIR__ . '/config.php';
    
    // Ajouter un enregistrement de test
    $result = logActivity('test_script', 'test', 'database', null, 'Test de l\'API des journaux d\'activité');
    
    echo "<p>Résultat de l'ajout: " . ($result ? "Succès" : "Échec") . "</p>";
} catch (Exception $e) {
    echo "<p>Erreur lors de l'ajout d'un enregistrement: " . $e->getMessage() . "</p>";
}

// 9. Liens utiles
echo "<h2>9. Liens utiles</h2>";
echo "<ul>";
echo "<li><a href='" . $baseUrl . "/views/admin/admin_logs.html' target='_blank'>Page du journal d'activité</a></li>";
echo "<li><a href='" . $baseUrl . "/api/logs_api.php?action=list' target='_blank'>API des journaux d'activité</a></li>";
echo "<li><a href='" . $baseUrl . "/check_logs_db.php' target='_blank'>Vérification de la base de données</a></li>";
echo "<li><a href='" . $baseUrl . "/add_test_logs.php' target='_blank'>Ajout d'enregistrements de test</a></li>";
echo "</ul>";

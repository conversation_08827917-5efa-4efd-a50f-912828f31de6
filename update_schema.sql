-- Script de mise à jour de la structure de la base de données
USE suivi_affaires_juridiques;

-- Mettre à jour la table avocats
ALTER TABLE avocats
    CHANGE COLUMN date_signature date_signature DATE NOT NULL,
    CHANGE COLUMN validite_contrat validite_contrat DATE NOT NULL;

-- Mettre à jour la table affaires
ALTER TABLE affaires
    CHANGE COLUMN nature nature ENUM('social', 'administrative', 'commerciale', 'foncier', 'refere', 'civile', 'penal') NOT NULL,
    CHANGE COLUMN objet objet TEXT NOT NULL,
    CHANGE COLUMN parties parties TEXT NULL,
    CHANGE COLUMN juridiction juridiction ENUM('tribunal', 'cour', 'cours-supreme', 'c-etat') NOT NULL,
    CHANGE COLUMN engagement engagement ENUM('societe', 'tiers') NOT NULL,
    CHANGE COLUMN date_engagement date_engagement DATE NOT NULL,
    CHANGE COLUMN date_jugement date_jugement DATE NULL,
    CHANGE COLUMN statut statut ENUM('en-cours', 'jugee-faveur', 'jugee-tiers') NOT NULL,
    CHANGE COLUMN dispositif dispositif TEXT NULL,
    CHANGE COLUMN montant montant DECIMAL(15,2) NULL,
    CHANGE COLUMN maitre_id maitre INT NOT NULL,
    ADD CONSTRAINT fk_maitre FOREIGN KEY (maitre) REFERENCES avocats(id) ON DELETE RESTRICT;

-- Mettre à jour la table users
ALTER TABLE users
    CHANGE COLUMN password_hash password VARCHAR(255) NOT NULL,
    ADD COLUMN email VARCHAR(100) NOT NULL UNIQUE,
    ADD COLUMN last_login DATETIME NULL,
    ADD COLUMN account_locked TINYINT(1) DEFAULT 0,
    ADD COLUMN locked_until DATETIME DEFAULT NULL,
    ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- Créer la table des journaux d'activité
CREATE TABLE IF NOT EXISTS activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    action ENUM('create', 'update', 'delete', 'delete_failed', 'reset', 'login', 'logout') NOT NULL,
    entity_type ENUM('avocat', 'affaire', 'database', 'user') NOT NULL,
    entity_id INT,
    details TEXT,
    ip_address VARCHAR(45),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB;

-- Créer la table pour les demandes de réinitialisation de mot de passe
CREATE TABLE IF NOT EXISTS password_resets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    reset_code VARCHAR(10) NOT NULL,
    reset_token VARCHAR(100) NOT NULL,
    expires_at DATETIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB;

-- Créer la table pour les tentatives de connexion échouées
CREATE TABLE IF NOT EXISTS login_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB;

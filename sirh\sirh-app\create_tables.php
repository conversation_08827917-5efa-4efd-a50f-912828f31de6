<?php

/**
 * <PERSON>ript pour créer les tables nécessaires
 */

echo "=== CRÉATION DES TABLES ===\n\n";

try {
    // Configuration de la base de données
    $host = '127.0.0.1';
    $port = '3306';
    $username = 'root';
    $password = '';
    $database = 'sirh';
    
    echo "1. Connexion à la base de données...\n";
    
    // Connexion à la base de données
    $pdo = new PDO("mysql:host={$host};port={$port};dbname={$database}", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connexion réussie !\n";
    
    echo "2. Création de la table workers...\n";
    
    // Créer la table workers
    $createWorkersTable = "
        CREATE TABLE IF NOT EXISTS `workers` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `employee_id` varchar(255) NOT NULL,
            `first_name` varchar(255) NOT NULL,
            `last_name` varchar(255) NOT NULL,
            `email` varchar(255) NOT NULL,
            `phone` varchar(255) DEFAULT NULL,
            `position` varchar(255) NOT NULL,
            `department` varchar(255) NOT NULL,
            `hire_date` date NOT NULL,
            `hourly_rate` decimal(8,2) NOT NULL,
            `status` enum('active','inactive','terminated') NOT NULL DEFAULT 'active',
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `workers_employee_id_unique` (`employee_id`),
            UNIQUE KEY `workers_email_unique` (`email`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($createWorkersTable);
    echo "✅ Table workers créée !\n";
    
    echo "3. Création de la table overtimes...\n";
    
    // Créer la table overtimes
    $createOvertimesTable = "
        CREATE TABLE IF NOT EXISTS `overtimes` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `worker_id` bigint(20) unsigned NOT NULL,
            `date` date NOT NULL,
            `hours` decimal(5,2) NOT NULL,
            `rate_multiplier` decimal(3,2) NOT NULL DEFAULT '1.50',
            `total_pay` decimal(10,2) NOT NULL,
            `description` text DEFAULT NULL,
            `approved_by` varchar(255) DEFAULT NULL,
            `status` enum('pending','approved','rejected') NOT NULL DEFAULT 'pending',
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `overtimes_worker_id_foreign` (`worker_id`),
            CONSTRAINT `overtimes_worker_id_foreign` FOREIGN KEY (`worker_id`) REFERENCES `workers` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($createOvertimesTable);
    echo "✅ Table overtimes créée !\n";
    
    echo "4. Création de la table migrations...\n";
    
    // Créer la table migrations pour Laravel
    $createMigrationsTable = "
        CREATE TABLE IF NOT EXISTS `migrations` (
            `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
            `migration` varchar(255) NOT NULL,
            `batch` int(11) NOT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($createMigrationsTable);
    echo "✅ Table migrations créée !\n";
    
    echo "5. Insertion de données de test...\n";
    
    // Insérer quelques travailleurs de test
    $insertWorkers = "
        INSERT IGNORE INTO `workers` (`employee_id`, `first_name`, `last_name`, `email`, `phone`, `position`, `department`, `hire_date`, `hourly_rate`, `status`, `created_at`, `updated_at`) VALUES
        ('EMP001', 'Jean', 'Dupont', '<EMAIL>', '0123456789', 'Développeur', 'IT', '2023-01-15', 25.00, 'active', NOW(), NOW()),
        ('EMP002', 'Marie', 'Martin', '<EMAIL>', '0123456790', 'Designer', 'Marketing', '2023-02-20', 22.50, 'active', NOW(), NOW()),
        ('EMP003', 'Pierre', 'Durand', '<EMAIL>', '0123456791', 'Manager', 'RH', '2022-12-01', 30.00, 'active', NOW(), NOW()),
        ('EMP004', 'Sophie', 'Leroy', '<EMAIL>', '0123456792', 'Comptable', 'Finance', '2023-03-10', 24.00, 'active', NOW(), NOW()),
        ('EMP005', 'Thomas', 'Moreau', '<EMAIL>', '0123456793', 'Technicien', 'IT', '2023-04-05', 20.00, 'active', NOW(), NOW());
    ";
    
    $pdo->exec($insertWorkers);
    echo "✅ Travailleurs de test insérés !\n";
    
    // Insérer quelques heures supplémentaires de test
    $insertOvertimes = "
        INSERT IGNORE INTO `overtimes` (`worker_id`, `date`, `hours`, `rate_multiplier`, `total_pay`, `description`, `status`, `created_at`, `updated_at`) VALUES
        (1, '2024-05-25', 3.00, 1.50, 112.50, 'Projet urgent', 'approved', NOW(), NOW()),
        (2, '2024-05-26', 2.50, 1.50, 84.38, 'Campagne marketing', 'approved', NOW(), NOW()),
        (3, '2024-05-27', 4.00, 1.50, 180.00, 'Réunion client', 'pending', NOW(), NOW()),
        (1, '2024-05-28', 2.00, 1.50, 75.00, 'Maintenance système', 'approved', NOW(), NOW()),
        (4, '2024-05-29', 1.50, 1.50, 54.00, 'Clôture mensuelle', 'pending', NOW(), NOW());
    ";
    
    $pdo->exec($insertOvertimes);
    echo "✅ Heures supplémentaires de test insérées !\n";
    
} catch (PDOException $e) {
    echo "❌ Erreur de base de données: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n🎉 Tables créées avec succès !\n";

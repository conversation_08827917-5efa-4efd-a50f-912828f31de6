<?php
// Script pour ajouter des affaires à haut risque (montant > 500 000 €) pour tester la fonctionnalité

// Activer l'affichage des erreurs
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Paramètres de connexion à la base de données
$host = "localhost";
$dbname = "suivi_affaires_juridiques";
$username = "root";
$password = "";

// Se connecter à la base de données
try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p>Connexion à la base de données réussie.</p>";
} catch (PDOException $e) {
    die("<p>Erreur de connexion à la base de données: " . $e->getMessage() . "</p>");
}

// Vérifier si la table affaires existe
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'affaires'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        // Créer la table si elle n'existe pas
        $sql = "
        CREATE TABLE IF NOT EXISTS affaires (
            id INT AUTO_INCREMENT PRIMARY KEY,
            numero VARCHAR(50) NOT NULL,
            objet VARCHAR(255) NOT NULL,
            nature VARCHAR(100),
            juridiction VARCHAR(100),
            date_creation DATE,
            statut VARCHAR(50) DEFAULT 'en-cours',
            montant DECIMAL(15,2) DEFAULT 0,
            maitre INT,
            parties TEXT,
            details TEXT
        ) ENGINE=InnoDB;
        ";
        
        $pdo->exec($sql);
        echo "<p>La table affaires a été créée.</p>";
    } else {
        echo "<p>La table affaires existe déjà.</p>";
    }
} catch (PDOException $e) {
    die("<p>Erreur lors de la vérification/création de la table: " . $e->getMessage() . "</p>");
}

// Vérifier si la table avocats existe
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'avocats'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        // Créer la table si elle n'existe pas
        $sql = "
        CREATE TABLE IF NOT EXISTS avocats (
            id INT AUTO_INCREMENT PRIMARY KEY,
            nom VARCHAR(100) NOT NULL,
            specialite VARCHAR(100),
            telephone VARCHAR(20),
            email VARCHAR(100),
            adresse TEXT,
            dateSignature DATE,
            validiteContrat DATE,
            details TEXT
        ) ENGINE=InnoDB;
        ";
        
        $pdo->exec($sql);
        echo "<p>La table avocats a été créée.</p>";
        
        // Ajouter un avocat par défaut
        $stmt = $pdo->prepare("
            INSERT INTO avocats (nom, specialite, telephone, email, dateSignature, validiteContrat)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $dateSignature = date('Y-m-d');
        $validiteContrat = date('Y-m-d', strtotime('+1 year'));
        
        $stmt->execute([
            'Me Dupont',
            'Droit des affaires',
            '0123456789',
            '<EMAIL>',
            $dateSignature,
            $validiteContrat
        ]);
        
        echo "<p>Un avocat par défaut a été ajouté.</p>";
    } else {
        echo "<p>La table avocats existe déjà.</p>";
    }
} catch (PDOException $e) {
    die("<p>Erreur lors de la vérification/création de la table: " . $e->getMessage() . "</p>");
}

// Récupérer l'ID d'un avocat existant
try {
    $stmt = $pdo->query("SELECT id FROM avocats LIMIT 1");
    $avocat = $stmt->fetch(PDO::FETCH_ASSOC);
    $avocatId = $avocat ? $avocat['id'] : null;
    
    if (!$avocatId) {
        // Ajouter un avocat si aucun n'existe
        $stmt = $pdo->prepare("
            INSERT INTO avocats (nom, specialite, telephone, email, dateSignature, validiteContrat)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $dateSignature = date('Y-m-d');
        $validiteContrat = date('Y-m-d', strtotime('+1 year'));
        
        $stmt->execute([
            'Me Martin',
            'Droit commercial',
            '0123456789',
            '<EMAIL>',
            $dateSignature,
            $validiteContrat
        ]);
        
        $avocatId = $pdo->lastInsertId();
        echo "<p>Un nouvel avocat a été ajouté avec l'ID: $avocatId</p>";
    } else {
        echo "<p>Avocat existant trouvé avec l'ID: $avocatId</p>";
    }
} catch (PDOException $e) {
    echo "<p>Erreur lors de la récupération/création de l'avocat: " . $e->getMessage() . "</p>";
    $avocatId = null;
}

// Données des affaires à haut risque à ajouter
$affairesHautRisque = [
    [
        'numero' => 'HR-2023-001',
        'objet' => 'Litige immobilier majeur',
        'nature' => 'Immobilier',
        'juridiction' => 'tribunal',
        'date_creation' => date('Y-m-d'),
        'statut' => 'en-cours',
        'montant' => 750000.00,
        'maitre' => $avocatId,
        'parties' => 'Société ABC vs Groupe XYZ',
        'details' => 'Litige concernant un projet immobilier de grande envergure'
    ],
    [
        'numero' => 'HR-2023-002',
        'objet' => 'Contentieux propriété intellectuelle',
        'nature' => 'Propriété intellectuelle',
        'juridiction' => 'cour',
        'date_creation' => date('Y-m-d', strtotime('-3 months')),
        'statut' => 'en-cours',
        'montant' => 1200000.00,
        'maitre' => $avocatId,
        'parties' => 'Entreprise Tech vs Concurrent',
        'details' => 'Violation de brevets sur des technologies innovantes'
    ],
    [
        'numero' => 'HR-2023-003',
        'objet' => 'Fusion-acquisition contestée',
        'nature' => 'Droit des affaires',
        'juridiction' => 'cours-supreme',
        'date_creation' => date('Y-m-d', strtotime('-6 months')),
        'statut' => 'jugee-faveur',
        'montant' => 2500000.00,
        'maitre' => $avocatId,
        'parties' => 'Groupe Financier vs Autorité de régulation',
        'details' => 'Contestation d\'une décision de l\'autorité de la concurrence'
    ],
    [
        'numero' => 'HR-2023-004',
        'objet' => 'Litige environnemental',
        'nature' => 'Environnement',
        'juridiction' => 'c-etat',
        'date_creation' => date('Y-m-d', strtotime('-1 year')),
        'statut' => 'jugee-tiers',
        'montant' => 850000.00,
        'maitre' => $avocatId,
        'parties' => 'Association vs Industriel',
        'details' => 'Pollution d\'un site naturel protégé'
    ],
    [
        'numero' => 'HR-2023-005',
        'objet' => 'Recours collectif consommateurs',
        'nature' => 'Droit de la consommation',
        'juridiction' => 'tribunal',
        'date_creation' => date('Y-m-d', strtotime('-2 months')),
        'statut' => 'en-cours',
        'montant' => 620000.00,
        'maitre' => $avocatId,
        'parties' => 'Association de consommateurs vs Fabricant',
        'details' => 'Défaut de sécurité sur des produits grand public'
    ]
];

// Ajouter les affaires à haut risque
try {
    // Préparer la requête d'insertion
    $stmt = $pdo->prepare("
        INSERT INTO affaires (numero, objet, nature, juridiction, date_creation, statut, montant, maitre, parties, details)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    // Compter les insertions réussies
    $successCount = 0;
    
    // Exécuter la requête pour chaque affaire
    foreach ($affairesHautRisque as $affaire) {
        // Vérifier si l'affaire existe déjà
        $checkStmt = $pdo->prepare("SELECT id FROM affaires WHERE numero = ?");
        $checkStmt->execute([$affaire['numero']]);
        
        if ($checkStmt->rowCount() === 0) {
            $stmt->execute([
                $affaire['numero'],
                $affaire['objet'],
                $affaire['nature'],
                $affaire['juridiction'],
                $affaire['date_creation'],
                $affaire['statut'],
                $affaire['montant'],
                $affaire['maitre'],
                $affaire['parties'],
                $affaire['details']
            ]);
            
            $successCount++;
        } else {
            echo "<p>L'affaire {$affaire['numero']} existe déjà.</p>";
        }
    }
    
    echo "<p>$successCount affaires à haut risque ont été ajoutées avec succès.</p>";
} catch (PDOException $e) {
    echo "<p>Erreur lors de l'ajout des affaires à haut risque: " . $e->getMessage() . "</p>";
}

// Compter le nombre total d'affaires
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM affaires");
    $count = $stmt->fetchColumn();
    
    echo "<p>Nombre total d'affaires dans la base de données: $count</p>";
    
    // Compter les affaires à haut risque
    $stmt = $pdo->query("SELECT COUNT(*) FROM affaires WHERE montant > 500000");
    $countHautRisque = $stmt->fetchColumn();
    
    echo "<p>Nombre d'affaires à haut risque (montant > 500 000 €): $countHautRisque</p>";
} catch (PDOException $e) {
    echo "<p>Erreur lors du comptage des affaires: " . $e->getMessage() . "</p>";
}

// Afficher les affaires à haut risque
try {
    $stmt = $pdo->query("
        SELECT a.*, av.nom as avocat_nom 
        FROM affaires a 
        LEFT JOIN avocats av ON a.maitre = av.id 
        WHERE a.montant > 500000 
        ORDER BY a.montant DESC
    ");
    $affaires = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Liste des affaires à haut risque:</h2>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Numéro</th><th>Objet</th><th>Montant</th><th>Statut</th><th>Juridiction</th><th>Avocat</th><th>Parties</th></tr>";
    
    foreach ($affaires as $affaire) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($affaire['numero']) . "</td>";
        echo "<td>" . htmlspecialchars($affaire['objet']) . "</td>";
        echo "<td>" . number_format($affaire['montant'], 2, ',', ' ') . " €</td>";
        echo "<td>" . htmlspecialchars($affaire['statut']) . "</td>";
        echo "<td>" . htmlspecialchars($affaire['juridiction']) . "</td>";
        echo "<td>" . htmlspecialchars($affaire['avocat_nom'] ?? 'Non assigné') . "</td>";
        echo "<td>" . htmlspecialchars($affaire['parties']) . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} catch (PDOException $e) {
    echo "<p>Erreur lors de l'affichage des affaires à haut risque: " . $e->getMessage() . "</p>";
}

// Liens utiles
echo "<h2>Liens utiles:</h2>";
echo "<ul>";
echo "<li><a href='app.html'>Tableau de bord</a></li>";
echo "</ul>";
?>

# Plan de l'application de gestion des affaires juridiques

L'application est un système de gestion des affaires juridiques conçu pour aider les cabinets d'avocats ou les services juridiques à suivre leurs dossiers, gérer leurs avocats et analyser leurs performances. Voici une explication détaillée de sa structure et de ses fonctionnalités :

## 1. Architecture générale

L'application est construite avec une architecture client-serveur légère :
- **Frontend** : HTML, CSS, JavaScript (vanilla)
- **Backend** : PHP pour les API
- **Base de données** : MySQL
- **Stockage local** : IndexedDB pour le stockage côté client

## 2. Structure des pages

### Page de connexion (login.html)
- Formulaire d'authentification
- Gestion des sessions utilisateurs
- Différenciation des rôles (administrateur/utilisateur)

### Page principale (app.html)
Organisée en trois onglets principaux :
1. **Tableau de bord** : Visualisation des statistiques et graphiques
2. **Affaires** : Gestion des dossiers juridiques
3. **Avocats** : Gestion des avocats et de leurs contrats

## 3. Fonctionnalités principales

### Gestion des affaires
- **Création/modification/suppression** de dossiers juridiques
- **Recherche et filtrage** par différents critères (statut, nature, etc.)
- **Suivi des parties** impliquées dans chaque affaire
- **Gestion des montants** associés aux affaires
- **Attribution d'avocats** aux affaires

### Gestion des avocats
- **Création/modification/suppression** de profils d'avocats
- **Suivi des contrats** avec dates de signature et de validité
- **Alerte automatique** pour les contrats expirant prochainement
- **Mise en évidence visuelle** des contrats expirés (lignes colorées)

### Tableau de bord analytique
- **Statistiques générales** (nombre d'affaires, taux de succès, etc.)
- **Graphiques de répartition** par nature d'affaire
- **Graphiques de répartition** par juridiction
- **Évolution temporelle** des affaires
- **Analyse financière** des montants par nature d'affaire
- **Résumé financier** (montants en cours, gagnés, perdus, moyens)
- **Section d'alertes** pour les contrats d'avocats expirant

### Gestion des utilisateurs et sécurité
- **Système d'authentification** avec différents niveaux d'accès
- **Restrictions des fonctionnalités** selon le rôle (ex: seuls les administrateurs peuvent supprimer)
- **Journal d'activité** pour suivre les modifications dans le système
- **Gestion des sessions** avec rafraîchissement automatique

## 4. Composants techniques

### Interface utilisateur
- **Design responsive** adapté à différentes tailles d'écran
- **Formulaires interactifs** avec validation
- **Tableaux de données** avec fonctionnalités de tri et filtrage
- **Graphiques interactifs** créés avec Chart.js
- **Notifications** pour informer l'utilisateur des actions réussies/échouées

### Base de données
- **Table des affaires** : stockage des informations sur les dossiers juridiques
- **Table des avocats** : stockage des informations sur les avocats
- **Table des utilisateurs** : gestion des comptes et des rôles
- **Table de journal** : enregistrement des activités du système

### API
- **API d'authentification** : gestion des connexions et sessions
- **API de gestion des affaires** : CRUD pour les dossiers
- **API de gestion des avocats** : CRUD pour les avocats
- **API de statistiques** : génération des données pour le tableau de bord

## 5. Flux de travail typique

1. L'utilisateur se connecte avec ses identifiants
2. Il accède à la page principale qui affiche par défaut la section "Affaires"
3. Il peut naviguer entre les différents onglets :
   - Consulter le tableau de bord pour une vue d'ensemble
   - Gérer les affaires (créer, modifier, rechercher)
   - Gérer les avocats et surveiller les contrats
4. Les administrateurs ont accès à des fonctionnalités supplémentaires comme la suppression d'éléments
5. Toutes les actions importantes sont enregistrées dans le journal d'activité

## 6. Caractéristiques spéciales

- **Auto-focus** sur les champs de formulaire pour une meilleure expérience utilisateur
- **Calcul automatique** de la date de validité des contrats (un an après la signature)
- **Mise en évidence** des contrats expirés pour une meilleure visibilité
- **Données de test** générées automatiquement pour les graphiques en l'absence de données réelles
- **Exportation des données** du tableau de bord

Cette application offre une solution complète pour la gestion des affaires juridiques, combinant suivi des dossiers, gestion des avocats et analyse des performances dans une interface intuitive et responsive.

<?php
// Script pour mettre à jour les références aux fichiers externes dans les fichiers HTML et JS

// Liste des fichiers à mettre à jour
$files = [
    'login.html',
    'app.html',
    'index.php',
    'test_db.php'
];

// Correspondances entre les URL CDN et les chemins locaux
$replacements = [
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' => 'assets/css/bootstrap.min.css',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js' => 'assets/js/bootstrap.bundle.min.js',
    'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css' => 'assets/css/bootstrap-icons.css',
    'https://code.jquery.com/jquery-3.6.0.min.js' => 'assets/js/jquery.min.js',
    'https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css' => 'assets/css/dataTables.bootstrap5.min.css',
    'https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js' => 'assets/js/jquery.dataTables.min.js',
    'https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js' => 'assets/js/dataTables.bootstrap5.min.js'
];

// Mettre à jour les références dans les fichiers
foreach ($files as $file) {
    if (!file_exists($file)) {
        echo "Fichier non trouvé: $file<br>";
        continue;
    }
    
    $content = file_get_contents($file);
    $originalContent = $content;
    
    foreach ($replacements as $cdn => $local) {
        $content = str_replace($cdn, $local, $content);
    }
    
    if ($content !== $originalContent) {
        if (file_put_contents($file, $content)) {
            echo "Références mises à jour dans $file<br>";
        } else {
            echo "Erreur lors de la mise à jour des références dans $file<br>";
        }
    } else {
        echo "Aucune référence à mettre à jour dans $file<br>";
    }
}

// Créer un fichier .htaccess pour améliorer la sécurité et les performances
$htaccessContent = <<<EOT
# Activer le module de réécriture d'URL
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /
    
    # Rediriger vers HTTPS si disponible
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
    
    # Empêcher l'accès direct aux fichiers .php dans le répertoire api/
    RewriteCond %{REQUEST_URI} ^/api/.*\.php$
    RewriteCond %{REQUEST_METHOD} !POST
    RewriteRule ^ - [F]
    
    # Empêcher l'accès direct aux fichiers .sql
    RewriteRule \.sql$ - [F]
</IfModule>

# Définir le fuseau horaire par défaut
<IfModule mod_php7.c>
    php_value date.timezone "Europe/Paris"
</IfModule>

# Protection des fichiers sensibles
<FilesMatch "^\.(htaccess|htpasswd|ini|log|sh|inc|bak)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>
EOT;

file_put_contents('.htaccess', $htaccessContent);
echo "Fichier .htaccess créé<br>";

echo "<h2>Mise à jour des références terminée!</h2>";
echo "<p>Votre application est maintenant prête à fonctionner sans connexion Internet.</p>";
echo "<a href='index.php' class='btn btn-primary' autofocus>Aller à la page d'accueil</a>";
?>




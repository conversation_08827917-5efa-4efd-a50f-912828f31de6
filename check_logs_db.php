<?php
// Script pour vérifier la structure de la base de données

// Activer l'affichage des erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Inclure le fichier de configuration
require_once __DIR__ . '/app/config/config.php';

// Fonction pour vérifier si une table existe
function tableExists($pdo, $table) {
    try {
        $result = $pdo->query("SHOW TABLES LIKE '{$table}'");
        return $result->rowCount() > 0;
    } catch (Exception $e) {
        echo "Erreur lors de la vérification de la table {$table}: " . $e->getMessage() . "<br>";
        return false;
    }
}

// Fonction pour obtenir la structure d'une table
function getTableStructure($pdo, $table) {
    try {
        $result = $pdo->query("DESCRIBE {$table}");
        return $result->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        echo "Erreur lors de la récupération de la structure de la table {$table}: " . $e->getMessage() . "<br>";
        return [];
    }
}

// Fonction pour compter les enregistrements dans une table
function countRecords($pdo, $table) {
    try {
        $result = $pdo->query("SELECT COUNT(*) FROM {$table}");
        return $result->fetchColumn();
    } catch (Exception $e) {
        echo "Erreur lors du comptage des enregistrements dans la table {$table}: " . $e->getMessage() . "<br>";
        return 0;
    }
}

// Fonction pour récupérer les 5 derniers enregistrements d'une table
function getLastRecords($pdo, $table, $limit = 5) {
    try {
        $result = $pdo->query("SELECT * FROM {$table} ORDER BY id DESC LIMIT {$limit}");
        return $result->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        echo "Erreur lors de la récupération des derniers enregistrements de la table {$table}: " . $e->getMessage() . "<br>";
        return [];
    }
}

// Se connecter à la base de données
try {
    $pdo = getDbConnection();
    echo "Connexion à la base de données réussie.<br>";
} catch (Exception $e) {
    die("Erreur de connexion à la base de données: " . $e->getMessage());
}

// Vérifier si la table activity_logs existe
$tableActivityLogs = 'activity_logs';
if (tableExists($pdo, $tableActivityLogs)) {
    echo "La table {$tableActivityLogs} existe.<br>";
    
    // Afficher la structure de la table
    echo "<h3>Structure de la table {$tableActivityLogs}:</h3>";
    echo "<pre>";
    print_r(getTableStructure($pdo, $tableActivityLogs));
    echo "</pre>";
    
    // Compter les enregistrements
    $count = countRecords($pdo, $tableActivityLogs);
    echo "Nombre d'enregistrements dans la table {$tableActivityLogs}: {$count}<br>";
    
    // Afficher les 5 derniers enregistrements
    if ($count > 0) {
        echo "<h3>Les 5 derniers enregistrements:</h3>";
        echo "<pre>";
        print_r(getLastRecords($pdo, $tableActivityLogs));
        echo "</pre>";
    } else {
        echo "Aucun enregistrement dans la table {$tableActivityLogs}.<br>";
        
        // Ajouter un enregistrement de test
        try {
            $stmt = $pdo->prepare("INSERT INTO activity_logs (username, action, entity_type, details, ip_address) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute(['admin', 'test', 'database', 'Test de la table activity_logs', '127.0.0.1']);
            echo "Un enregistrement de test a été ajouté.<br>";
        } catch (Exception $e) {
            echo "Erreur lors de l'ajout d'un enregistrement de test: " . $e->getMessage() . "<br>";
        }
    }
} else {
    echo "La table {$tableActivityLogs} n'existe pas!<br>";
    
    // Créer la table si elle n'existe pas
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS activity_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL,
                action ENUM('create', 'update', 'delete', 'delete_failed', 'reset', 'login', 'logout', 'test') NOT NULL,
                entity_type ENUM('avocat', 'affaire', 'database', 'user') NOT NULL,
                entity_id INT,
                details TEXT,
                ip_address VARCHAR(45),
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB;
        ");
        echo "La table {$tableActivityLogs} a été créée.<br>";
    } catch (Exception $e) {
        echo "Erreur lors de la création de la table {$tableActivityLogs}: " . $e->getMessage() . "<br>";
    }
}

// Vérifier les autres tables importantes
$tables = ['users', 'avocats', 'affaires'];
foreach ($tables as $table) {
    if (tableExists($pdo, $table)) {
        echo "La table {$table} existe. Nombre d'enregistrements: " . countRecords($pdo, $table) . "<br>";
    } else {
        echo "La table {$table} n'existe pas!<br>";
    }
}

<?php
// Version ultra-simplifiée du journal d'activité

// Activer l'affichage des erreurs
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Paramètres de connexion à la base de données
$host = "localhost";
$dbname = "suivi_affaires_juridiques";
$username = "root";
$password = "";

// Fonction pour se connecter à la base de données
function connectDB() {
    global $host, $dbname, $username, $password;
    
    try {
        $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch (PDOException $e) {
        die("Erreur de connexion à la base de données: " . $e->getMessage());
    }
}

// Fonction pour vérifier si la table activity_logs existe
function tableExists($pdo, $table) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

// Fonction pour créer la table activity_logs
function createTable($pdo) {
    try {
        $sql = "
        CREATE TABLE IF NOT EXISTS activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL,
            action ENUM('create', 'update', 'delete', 'delete_failed', 'reset', 'login', 'logout', 'test') NOT NULL,
            entity_type ENUM('avocat', 'affaire', 'database', 'user') NOT NULL,
            entity_id INT,
            details TEXT,
            ip_address VARCHAR(45),
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB;
        ";
        
        $pdo->exec($sql);
        return true;
    } catch (PDOException $e) {
        return false;
    }
}

// Fonction pour ajouter un enregistrement de test
function addTestRecord($pdo) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO activity_logs (username, action, entity_type, entity_id, details, ip_address)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            'admin',
            'test',
            'database',
            null,
            'Test depuis basic_logs.php',
            $_SERVER['REMOTE_ADDR']
        ]);
        
        return true;
    } catch (PDOException $e) {
        return false;
    }
}

// Fonction pour récupérer les logs
function getLogs($pdo, $limit = 20, $offset = 0) {
    try {
        $stmt = $pdo->prepare("
            SELECT * FROM activity_logs
            ORDER BY timestamp DESC
            LIMIT ? OFFSET ?
        ");
        
        $stmt->bindValue(1, $limit, PDO::PARAM_INT);
        $stmt->bindValue(2, $offset, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        return [];
    }
}

// Fonction pour compter le nombre total de logs
function countLogs($pdo) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM activity_logs");
        return $stmt->fetchColumn();
    } catch (PDOException $e) {
        return 0;
    }
}

// Fonction pour vider la table
function clearLogs($pdo) {
    try {
        $pdo->exec("TRUNCATE TABLE activity_logs");
        return true;
    } catch (PDOException $e) {
        return false;
    }
}

// Se connecter à la base de données
$pdo = connectDB();

// Vérifier si la table existe, sinon la créer
if (!tableExists($pdo, 'activity_logs')) {
    createTable($pdo);
}

// Traiter les actions
$message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_test':
                if (addTestRecord($pdo)) {
                    $message = 'Enregistrement de test ajouté avec succès.';
                } else {
                    $message = 'Erreur lors de l\'ajout de l\'enregistrement de test.';
                }
                break;
                
            case 'clear':
                if (clearLogs($pdo)) {
                    $message = 'Tous les logs ont été supprimés.';
                } else {
                    $message = 'Erreur lors de la suppression des logs.';
                }
                break;
        }
    }
}

// Récupérer les logs
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$logsPerPage = 10;
$offset = ($page - 1) * $logsPerPage;

$logs = getLogs($pdo, $logsPerPage, $offset);
$totalLogs = countLogs($pdo);
$totalPages = ceil($totalLogs / $logsPerPage);
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Journal d'Activité (Version basique)</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1 {
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .message {
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 5px;
            background-color: #d4edda;
            color: #155724;
        }
        .actions {
            margin-bottom: 20px;
        }
        button, .button {
            padding: 8px 16px;
            margin-right: 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
        }
        .primary {
            background-color: #007bff;
            color: white;
        }
        .danger {
            background-color: #dc3545;
            color: white;
        }
        .secondary {
            background-color: #6c757d;
            color: white;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
        }
        tr:hover {
            background-color: #f1f1f1;
        }
        .pagination {
            display: flex;
            list-style: none;
            padding: 0;
            justify-content: center;
        }
        .pagination li {
            margin: 0 5px;
        }
        .pagination a {
            display: block;
            padding: 8px 12px;
            text-decoration: none;
            background-color: #fff;
            border: 1px solid #ddd;
            color: #007bff;
            border-radius: 4px;
        }
        .pagination a.active {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }
        .pagination a:hover:not(.active) {
            background-color: #f1f1f1;
        }
        .footer {
            margin-top: 20px;
            text-align: center;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Journal d'Activité (Version basique)</h1>
        
        <?php if (!empty($message)): ?>
        <div class="message">
            <?php echo htmlspecialchars($message); ?>
        </div>
        <?php endif; ?>
        
        <div class="actions">
            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="add_test">
                <button type="submit" class="primary">Ajouter un enregistrement de test</button>
            </form>
            
            <form method="post" style="display: inline;" onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer tous les logs?');">
                <input type="hidden" name="action" value="clear">
                <button type="submit" class="danger">Vider les logs</button>
            </form>
            
            <a href="test_db_connection.php" class="button secondary">Tester la connexion</a>
        </div>
        
        <h2>Liste des activités (<?php echo $totalLogs; ?> au total)</h2>
        
        <?php if (empty($logs)): ?>
        <p>Aucune activité enregistrée.</p>
        <?php else: ?>
        <div style="overflow-x: auto;">
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Date et heure</th>
                        <th>Utilisateur</th>
                        <th>Action</th>
                        <th>Type d'entité</th>
                        <th>ID Entité</th>
                        <th>Détails</th>
                        <th>Adresse IP</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($logs as $log): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($log['id']); ?></td>
                        <td><?php echo htmlspecialchars($log['timestamp']); ?></td>
                        <td><?php echo htmlspecialchars($log['username']); ?></td>
                        <td><?php echo htmlspecialchars($log['action']); ?></td>
                        <td><?php echo htmlspecialchars($log['entity_type']); ?></td>
                        <td><?php echo htmlspecialchars($log['entity_id'] ?? 'N/A'); ?></td>
                        <td><?php echo htmlspecialchars($log['details']); ?></td>
                        <td><?php echo htmlspecialchars($log['ip_address']); ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <?php if ($totalPages > 1): ?>
        <ul class="pagination">
            <?php if ($page > 1): ?>
            <li><a href="?page=1">«</a></li>
            <li><a href="?page=<?php echo $page - 1; ?>">‹</a></li>
            <?php endif; ?>
            
            <?php
            $startPage = max(1, $page - 2);
            $endPage = min($totalPages, $page + 2);
            
            for ($i = $startPage; $i <= $endPage; $i++):
            ?>
            <li><a href="?page=<?php echo $i; ?>" <?php echo ($i === $page) ? 'class="active"' : ''; ?>><?php echo $i; ?></a></li>
            <?php endfor; ?>
            
            <?php if ($page < $totalPages): ?>
            <li><a href="?page=<?php echo $page + 1; ?>">›</a></li>
            <li><a href="?page=<?php echo $totalPages; ?>">»</a></li>
            <?php endif; ?>
        </ul>
        <?php endif; ?>
        <?php endif; ?>
        
        <div class="footer">
            <p>
                <a href="index.html">Retour à l'accueil</a> | 
                <a href="views/admin/admin_logs.html">Version originale</a> | 
                <a href="admin_logs_standalone.php">Version autonome</a>
            </p>
        </div>
    </div>
</body>
</html>

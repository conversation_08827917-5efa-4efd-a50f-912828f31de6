<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion - Suivi des Affaires Juridiques</title>
    <link rel="stylesheet" href="../../assets/css/styles.css">
    <link href="../../assets/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }
        .login-container {
            max-width: 400px;
            width: 100%;
            padding: 30px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        .login-logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-logo h1 {
            color: #0d6efd;
            font-size: 1.8rem;
        }
        .login-form .form-control {
            padding: 12px;
            margin-bottom: 20px;
        }
        .login-btn {
            width: 100%;
            padding: 12px;
            font-weight: 500;
        }
        .error-message {
            color: #dc3545;
            margin-bottom: 15px;
            display: none;
        }
        .register-link {
            text-align: center;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-logo">
            <h1>Système de Suivi des Affaires Juridiques</h1>
            <p class="text-muted">Veuillez vous connecter pour accéder au système</p>
        </div>

        <div id="error-message" class="error-message alert alert-danger">
            Nom d'utilisateur ou mot de passe incorrect.
        </div>

        <form id="login-form" class="login-form">
            <div class="mb-3">
                <label for="username" class="form-label">Nom d'utilisateur</label>
                <input type="text" class="form-control" id="username" required>
            </div>

            <div class="mb-3">
                <label for="password" class="form-label">Mot de passe</label>
                <input type="password" class="form-control" id="password" required>
                <div class="text-end mt-1">
                    <a href="reset_password.html" class="text-decoration-none small">Mot de passe oublié ?</a>
                </div>
            </div>

            <button type="submit" class="btn btn-primary login-btn">Se connecter</button>
        </form>

        <div class="register-link">
            <p>Pas encore de compte? <a href="#" id="register-link">Créer un compte</a></p>
        </div>

        <!-- Formulaire d'inscription (caché par défaut) -->
        <form id="register-form" class="login-form" style="display: none;">
            <h3 class="mb-3">Créer un compte</h3>

            <div class="mb-3">
                <label for="reg-username" class="form-label">Nom d'utilisateur</label>
                <input type="text" class="form-control" id="reg-username" required>
            </div>

            <div class="mb-3">
                <label for="reg-password" class="form-label">Mot de passe</label>
                <input type="password" class="form-control" id="reg-password" required>
            </div>

            <div class="mb-3">
                <label for="reg-confirm-password" class="form-label">Confirmer le mot de passe</label>
                <input type="password" class="form-control" id="reg-confirm-password" required>
            </div>

            <button type="submit" class="btn btn-success login-btn">Créer un compte</button>

            <div class="text-center mt-3">
                <a href="#" id="back-to-login">Retour à la connexion</a>
            </div>
        </form>
    </div>

    <script src="../../assets/js/bootstrap.bundle.min.js"></script>
    <script src="../../assets/js/database_mysql.js"></script>
    <script src="../../assets/js/auth.js"></script>
</body>
</html>

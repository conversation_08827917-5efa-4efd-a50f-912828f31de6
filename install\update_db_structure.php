<?php
/**
 * Script pour mettre à jour la structure de la base de données
 * Ce script exécute les migrations nécessaires pour mettre à jour la structure de la base de données
 */

// Inclure le fichier de configuration
require_once __DIR__ . '/../app/config/config.php';

// Fonction pour exécuter un fichier SQL
function executeSqlFile($pdo, $filePath) {
    try {
        // Lire le contenu du fichier SQL
        $sql = file_get_contents($filePath);
        
        // Exécuter les requêtes SQL
        $pdo->exec($sql);
        
        return true;
    } catch (PDOException $e) {
        echo "Erreur lors de l'exécution du fichier SQL: " . $e->getMessage() . "\n";
        return false;
    }
}

// Fonction pour afficher un message
function displayMessage($message, $isError = false) {
    echo ($isError ? "Erreur: " : "Info: ") . $message . "\n";
}

// Vérifier si le script est exécuté en ligne de commande ou via le navigateur
$isCli = php_sapi_name() === 'cli';

// Adapter l'affichage en fonction du mode d'exécution
if (!$isCli) {
    echo "<!DOCTYPE html>
<html>
<head>
    <title>Mise à jour de la structure de la base de données</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        h1 {
            color: #333;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class='container'>
        <h1>Mise à jour de la structure de la base de données</h1>
        <pre>";
}

try {
    // Se connecter à la base de données
    $pdo = getDbConnection();
    displayMessage("Connexion à la base de données réussie.");
    
    // Liste des fichiers de migration à exécuter
    $migrations = [
        __DIR__ . '/../database/migrations/rename_password_column.sql'
    ];
    
    // Exécuter chaque fichier de migration
    foreach ($migrations as $migration) {
        displayMessage("Exécution de la migration: " . basename($migration));
        
        if (executeSqlFile($pdo, $migration)) {
            displayMessage("Migration " . basename($migration) . " exécutée avec succès.");
        } else {
            displayMessage("Échec de l'exécution de la migration " . basename($migration) . ".", true);
        }
    }
    
    displayMessage("Mise à jour de la structure de la base de données terminée.");
} catch (PDOException $e) {
    displayMessage("Erreur de connexion à la base de données: " . $e->getMessage(), true);
}

// Fermer l'affichage HTML si nécessaire
if (!$isCli) {
    echo "</pre>
        <p class='success'>Vous pouvez maintenant <a href='../public/'>retourner à l'application</a>.</p>
    </div>
</body>
</html>";
}

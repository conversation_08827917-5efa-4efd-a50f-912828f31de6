/* Styles généraux */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* En-tête */
header {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 1rem;
}

header h1 {
    color: #0d6efd;
}

/* Navigation */
.nav-tabs .nav-link {
    color: #495057;
    cursor: pointer;
}

.nav-tabs .nav-link.active {
    color: #0d6efd;
    font-weight: 500;
}

/* Tableaux */
.table {
    background-color: white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
    table-layout: fixed;
    width: 100%;
}

.table th {
    background-color: #f1f5f9;
    position: sticky;
    top: 0;
    z-index: 10;
    font-weight: 600;
    vertical-align: middle;
}

.table td {
    vertical-align: middle;
    padding: 0.5rem;
}

/* Styles pour les cellules du tableau */
.cell-numero, .cell-nature, .cell-juridiction, .cell-statut, .cell-maitre {
    text-align: center;
}

.cell-objet {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: help;
}

/* Largeurs des colonnes du tableau des affaires */
.col-numero {
    width: 10%;
}

.col-nature {
    width: 10%;
}

.col-objet {
    width: 15%;
}

.col-parties {
    width: 20%;
}

.col-juridiction {
    width: 10%;
}

.col-statut {
    width: 10%;
}

.col-maitre {
    width: 10%;
}

.col-actions {
    width: 15%;
}

/* Cellule des parties */
.parties-cell {
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: help;
    position: relative;
    font-style: italic;
    color: #495057;
    padding: 0.5rem 0.75rem;
    border-left: 3px solid #0d6efd;
    background-color: rgba(13, 110, 253, 0.05);
}

/* Style pour l'infobulle personnalisée */
.parties-cell:hover::after {
    content: attr(title);
    position: absolute;
    left: 0;
    top: 100%;
    z-index: 1000;
    background-color: #fff;
    border: 1px solid #0d6efd;
    border-radius: 4px;
    padding: 8px 12px;
    white-space: normal;
    max-width: 350px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    display: none;
    font-style: normal;
    font-weight: normal;
    color: #212529;
    font-size: 0.9rem;
    line-height: 1.5;
}

.parties-cell:hover::after {
    display: block;
    animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-5px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Formulaires */
.card {
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    margin-top: 20px;
}

.form-label {
    font-weight: 500;
}

/* Boutons */
.btn-primary {
    background-color: #0d6efd;
}

.btn-secondary {
    background-color: #6c757d;
}

.btn-danger {
    background-color: #dc3545;
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
}

/* Actions dans les tableaux */
.action-buttons {
    display: flex;
    gap: 5px;
}

.action-buttons button {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Styles pour les contrats expirés */
.table-danger {
    background-color: rgba(220, 53, 69, 0.15) !important;
    border-left: 4px solid #dc3545;
}

.table-danger:hover {
    background-color: rgba(220, 53, 69, 0.25) !important;
}

.text-danger.fw-bold {
    color: #dc3545 !important;
    font-weight: 700 !important;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 10px;
        max-width: 100%;
    }

    .table-responsive {
        overflow-x: auto;
    }

    /* Ajustements pour les tablettes */
    header h1 {
        font-size: 1.5rem;
    }

    .card {
        margin-bottom: 15px;
    }

    .col-md-3, .col-md-6 {
        margin-bottom: 15px;
    }

    /* Ajustements pour les graphiques */
    canvas {
        height: 250px !important;
        max-height: 250px !important;
    }

    /* Amélioration de l'affichage des statistiques */
    #total-affaires, #taux-succes, #montant-total, #total-avocats {
        font-size: 1.5rem;
    }
}

/* Styles spécifiques pour les petits écrans */
@media (max-width: 576px) {
    header {
        flex-direction: column;
        align-items: flex-start;
    }

    header h1 {
        font-size: 1.2rem;
        margin-bottom: 10px;
    }

    .action-buttons {
        flex-direction: column;
        gap: 5px;
    }

    .action-buttons button {
        width: 100%;
    }

    /* Ajustements pour les graphiques sur petits écrans */
    canvas {
        height: 200px !important;
    }
}

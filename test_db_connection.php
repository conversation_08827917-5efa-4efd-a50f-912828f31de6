<?php
// Script pour tester la connexion à la base de données

// Activer l'affichage des erreurs
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Test de connexion à la base de données</h1>";

// Paramètres de connexion
$host = "localhost";
$dbname = "suivi_affaires_juridiques";
$username = "root";
$password = "";

// Tester la connexion avec PDO
echo "<h2>Test avec PDO</h2>";
try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color:green'>✓ Connexion PDO réussie</p>";
    
    // Vérifier si la base de données existe
    $stmt = $pdo->query("SELECT DATABASE()");
    $dbname_result = $stmt->fetchColumn();
    echo "<p>Base de données actuelle: <strong>$dbname_result</strong></p>";
    
    // Lister les tables
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<p>Tables dans la base de données:</p>";
    echo "<ul>";
    if (count($tables) > 0) {
        foreach ($tables as $table) {
            echo "<li>$table</li>";
        }
    } else {
        echo "<li>Aucune table trouvée</li>";
    }
    echo "</ul>";
    
    // Vérifier si la table activity_logs existe
    $activity_logs_exists = in_array('activity_logs', $tables);
    if ($activity_logs_exists) {
        echo "<p style='color:green'>✓ La table activity_logs existe</p>";
        
        // Vérifier la structure de la table
        $stmt = $pdo->query("DESCRIBE activity_logs");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>Structure de la table activity_logs:</p>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            foreach ($column as $key => $value) {
                echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
            }
            echo "</tr>";
        }
        
        echo "</table>";
        
        // Compter les enregistrements
        $stmt = $pdo->query("SELECT COUNT(*) FROM activity_logs");
        $count = $stmt->fetchColumn();
        echo "<p>Nombre d'enregistrements dans activity_logs: <strong>$count</strong></p>";
        
        // Afficher quelques enregistrements
        if ($count > 0) {
            $stmt = $pdo->query("SELECT * FROM activity_logs ORDER BY id DESC LIMIT 5");
            $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<p>5 derniers enregistrements:</p>";
            echo "<table border='1' cellpadding='5'>";
            
            // En-têtes
            echo "<tr>";
            foreach (array_keys($logs[0]) as $header) {
                echo "<th>" . htmlspecialchars($header) . "</th>";
            }
            echo "</tr>";
            
            // Données
            foreach ($logs as $log) {
                echo "<tr>";
                foreach ($log as $value) {
                    echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
                }
                echo "</tr>";
            }
            
            echo "</table>";
        }
    } else {
        echo "<p style='color:red'>✗ La table activity_logs n'existe pas</p>";
        
        // Proposer de créer la table
        echo "<form method='post'>";
        echo "<input type='hidden' name='action' value='create_table'>";
        echo "<button type='submit'>Créer la table activity_logs</button>";
        echo "</form>";
    }
} catch (PDOException $e) {
    echo "<p style='color:red'>✗ Erreur de connexion PDO: " . $e->getMessage() . "</p>";
}

// Tester la connexion avec mysqli
echo "<h2>Test avec MySQLi</h2>";
try {
    $mysqli = new mysqli($host, $username, $password, $dbname);
    
    if ($mysqli->connect_error) {
        throw new Exception("Erreur de connexion: " . $mysqli->connect_error);
    }
    
    echo "<p style='color:green'>✓ Connexion MySQLi réussie</p>";
    $mysqli->close();
} catch (Exception $e) {
    echo "<p style='color:red'>✗ Erreur de connexion MySQLi: " . $e->getMessage() . "</p>";
}

// Traiter les actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'create_table') {
        try {
            $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            $sql = "
            CREATE TABLE IF NOT EXISTS activity_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL,
                action ENUM('create', 'update', 'delete', 'delete_failed', 'reset', 'login', 'logout', 'test') NOT NULL,
                entity_type ENUM('avocat', 'affaire', 'database', 'user') NOT NULL,
                entity_id INT,
                details TEXT,
                ip_address VARCHAR(45),
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB;
            ";
            
            $pdo->exec($sql);
            echo "<p style='color:green'>✓ Table activity_logs créée avec succès</p>";
            echo "<p>Veuillez <a href=''>rafraîchir la page</a> pour voir les changements.</p>";
        } catch (PDOException $e) {
            echo "<p style='color:red'>✗ Erreur lors de la création de la table: " . $e->getMessage() . "</p>";
        }
    }
}

// Informations sur le serveur
echo "<h2>Informations sur le serveur</h2>";
echo "<p>Version PHP: " . phpversion() . "</p>";
echo "<p>Extensions PHP chargées:</p>";
echo "<ul>";
$extensions = get_loaded_extensions();
sort($extensions);
foreach ($extensions as $extension) {
    if ($extension === 'mysql' || $extension === 'mysqli' || $extension === 'pdo_mysql') {
        echo "<li><strong>$extension</strong></li>";
    } else {
        echo "<li>$extension</li>";
    }
}
echo "</ul>";

// Liens utiles
echo "<h2>Liens utiles</h2>";
echo "<ul>";
echo "<li><a href='admin_logs_standalone.php'>Journal d'activité (version autonome)</a></li>";
echo "<li><a href='add_test_logs_simple.php'>Ajouter des données de test</a></li>";
echo "<li><a href='views/admin/admin_logs.html'>Journal d'activité (version originale)</a></li>";
echo "</ul>";
?>

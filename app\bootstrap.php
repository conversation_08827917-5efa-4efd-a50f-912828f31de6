<?php
/**
 * Bootstrap de l'application
 * Ce fichier initialise l'application et charge les dépendances nécessaires
 */

// Définir le chemin de base de l'application
define('BASE_PATH', dirname(__DIR__));

// Charger la configuration
require_once BASE_PATH . '/app/config/config.php';

// Charger les fonctions utilitaires
require_once BASE_PATH . '/app/utils/functions.php';

// Initialiser la session
session_start();

// Fonction pour charger automatiquement les classes
spl_autoload_register(function ($className) {
    // Convertir le nom de la classe en chemin de fichier
    $className = str_replace('\\', DIRECTORY_SEPARATOR, $className);
    
    // Chemins possibles pour les classes
    $paths = [
        BASE_PATH . '/app/core/',
        BASE_PATH . '/app/models/',
        BASE_PATH . '/app/utils/'
    ];
    
    // Vérifier chaque chemin
    foreach ($paths as $path) {
        $file = $path . $className . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
});

// Fonction pour vérifier si l'utilisateur est connecté
function isLoggedIn() {
    return isset($_SESSION['user_id']) && isset($_SESSION['username']) && isset($_SESSION['user_role']);
}

// Fonction pour vérifier si l'utilisateur est administrateur
function isAdmin() {
    return isLoggedIn() && $_SESSION['user_role'] === 'admin';
}

// Fonction pour rediriger vers une page
function redirect($page) {
    header("Location: $page");
    exit;
}

// Fonction pour afficher un message d'erreur
function displayError($message) {
    echo json_encode(['success' => false, 'message' => $message]);
    exit;
}

// Fonction pour afficher un message de succès
function displaySuccess($message, $data = null) {
    $response = ['success' => true, 'message' => $message];
    if ($data !== null) {
        $response['data'] = $data;
    }
    echo json_encode($response);
    exit;
}
?>

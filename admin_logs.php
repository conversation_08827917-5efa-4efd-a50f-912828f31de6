<?php
// Version ultra-simplifiée du journal d'activité directement à la racine

// Activer l'affichage des erreurs
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Paramètres de connexion à la base de données
$host = "localhost";
$dbname = "suivi_affaires_juridiques";
$username = "root";
$password = "";

// Fonction pour se connecter à la base de données
function connectDB() {
    global $host, $dbname, $username, $password;

    try {
        $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch (PDOException $e) {
        die("<div class='alert alert-danger'>Erreur de connexion à la base de données: " . $e->getMessage() . "</div>");
    }
}

// Fonction pour vérifier si la table activity_logs existe
function tableExists($pdo, $table) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

// Fonction pour créer la table activity_logs
function createTable($pdo) {
    try {
        $sql = "
        CREATE TABLE IF NOT EXISTS activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL,
            action ENUM('create', 'update', 'delete', 'delete_failed', 'reset', 'login', 'logout', 'test') NOT NULL,
            entity_type ENUM('avocat', 'affaire', 'database', 'user') NOT NULL,
            entity_id INT,
            details TEXT,
            ip_address VARCHAR(45),
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB;
        ";

        $pdo->exec($sql);
        return true;
    } catch (PDOException $e) {
        return false;
    }
}

// Fonction pour ajouter un enregistrement de test
function addTestRecord($pdo) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO activity_logs (username, action, entity_type, entity_id, details, ip_address)
            VALUES (?, ?, ?, ?, ?, ?)
        ");

        $stmt->execute([
            'admin',
            'test',
            'database',
            null,
            'Test depuis admin_logs.php',
            $_SERVER['REMOTE_ADDR']
        ]);

        return true;
    } catch (PDOException $e) {
        return false;
    }
}

// Fonction pour récupérer les logs
function getLogs($pdo, $limit = 20, $offset = 0, $filters = []) {
    try {
        // Construire la requête SQL avec les filtres
        $sql = "SELECT * FROM activity_logs WHERE 1=1";
        $params = [];

        // Ajouter les conditions de filtrage
        if (!empty($filters['username'])) {
            $sql .= " AND username = ?";
            $params[] = $filters['username'];
        }

        if (!empty($filters['action'])) {
            $sql .= " AND action = ?";
            $params[] = $filters['action'];
        }

        if (!empty($filters['entity_type'])) {
            $sql .= " AND entity_type = ?";
            $params[] = $filters['entity_type'];
        }

        if (!empty($filters['date'])) {
            $sql .= " AND DATE(timestamp) = ?";
            $params[] = $filters['date'];
        }

        // Ajouter l'ordre et la pagination
        $sql .= " ORDER BY timestamp DESC";

        // Utiliser LIMIT et OFFSET seulement si ce sont des entiers positifs
        if (is_numeric($limit) && $limit > 0) {
            $sql .= " LIMIT " . (int)$limit;

            if (is_numeric($offset) && $offset >= 0) {
                $sql .= " OFFSET " . (int)$offset;
            }
        }

        // Afficher la requête SQL pour le débogage (commentez cette ligne en production)
        // echo "<div class='alert alert-info'>SQL: $sql</div>";
        // echo "<div class='alert alert-info'>Params: " . print_r($params, true) . "</div>";

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);

        $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Afficher le nombre de logs récupérés pour le débogage (commentez cette ligne en production)
        // echo "<div class='alert alert-info'>Nombre de logs récupérés: " . count($logs) . "</div>";

        // Afficher les premiers logs pour le débogage
        // if (count($logs) > 0) {
        //     echo "<div class='alert alert-info'>Premier log: <pre>" . print_r($logs[0], true) . "</pre></div>";
        // }

        return $logs;
    } catch (PDOException $e) {
        // Afficher l'erreur pour le débogage (commentez cette ligne en production)
        echo "<div class='alert alert-danger'>Erreur SQL: " . $e->getMessage() . "</div>";
        return [];
    }
}

// Fonction pour compter le nombre total de logs
function countLogs($pdo, $filters = []) {
    try {
        // Construire la requête SQL avec les filtres
        $sql = "SELECT COUNT(*) FROM activity_logs WHERE 1=1";
        $params = [];

        // Ajouter les conditions de filtrage
        if (!empty($filters['username'])) {
            $sql .= " AND username = ?";
            $params[] = $filters['username'];
        }

        if (!empty($filters['action'])) {
            $sql .= " AND action = ?";
            $params[] = $filters['action'];
        }

        if (!empty($filters['entity_type'])) {
            $sql .= " AND entity_type = ?";
            $params[] = $filters['entity_type'];
        }

        if (!empty($filters['date'])) {
            $sql .= " AND DATE(timestamp) = ?";
            $params[] = $filters['date'];
        }

        // Afficher la requête SQL pour le débogage (commentez cette ligne en production)
        // echo "<div class='alert alert-info'>Count SQL: $sql</div>";
        // echo "<div class='alert alert-info'>Count Params: " . print_r($params, true) . "</div>";

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);

        $count = $stmt->fetchColumn();

        // Afficher le nombre total pour le débogage (commentez cette ligne en production)
        // echo "<div class='alert alert-info'>Nombre total de logs: $count</div>";

        return $count;
    } catch (PDOException $e) {
        // Afficher l'erreur pour le débogage (commentez cette ligne en production)
        echo "<div class='alert alert-danger'>Erreur SQL (count): " . $e->getMessage() . "</div>";
        return 0;
    }
}

// Fonction pour vider la table
function clearLogs($pdo) {
    try {
        $pdo->exec("TRUNCATE TABLE activity_logs");
        return true;
    } catch (PDOException $e) {
        return false;
    }
}

// Fonction pour récupérer la liste des utilisateurs
function getUsers($pdo) {
    try {
        $stmt = $pdo->query("SELECT DISTINCT username FROM activity_logs ORDER BY username");
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    } catch (PDOException $e) {
        return [];
    }
}

// Se connecter à la base de données
$pdo = connectDB();

// Fonction pour vérifier et corriger la structure de la table activity_logs
function checkAndFixTableStructure($pdo) {
    try {
        // Vérifier si la table existe
        $tableExists = tableExists($pdo, 'activity_logs');

        if (!$tableExists) {
            // Créer la table si elle n'existe pas
            createTable($pdo);
            echo "<div class='alert alert-success'>La table activity_logs a été créée.</div>";
            return;
        }

        // Vérifier la structure de la table
        $stmt = $pdo->query("DESCRIBE activity_logs");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Vérifier si toutes les colonnes nécessaires existent
        $requiredColumns = [
            'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
            'username' => 'VARCHAR(50) NOT NULL',
            'action' => "ENUM('create','update','delete','delete_failed','reset','login','logout','test') NOT NULL",
            'entity_type' => "ENUM('avocat','affaire','database','user') NOT NULL",
            'entity_id' => 'INT',
            'details' => 'TEXT',
            'ip_address' => 'VARCHAR(45)',
            'timestamp' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
        ];

        $existingColumns = [];
        foreach ($columns as $column) {
            $existingColumns[$column['Field']] = true;
        }

        $missingColumns = [];
        foreach ($requiredColumns as $column => $type) {
            if (!isset($existingColumns[$column])) {
                $missingColumns[$column] = $type;
            }
        }

        if (!empty($missingColumns)) {
            echo "<div class='alert alert-warning'>Des colonnes sont manquantes dans la table activity_logs. Tentative de correction...</div>";

            // Ajouter les colonnes manquantes
            foreach ($missingColumns as $column => $type) {
                try {
                    $pdo->exec("ALTER TABLE activity_logs ADD COLUMN {$column} {$type}");
                    echo "<div class='alert alert-success'>Colonne {$column} ajoutée avec succès.</div>";
                } catch (PDOException $e) {
                    echo "<div class='alert alert-danger'>Erreur lors de l'ajout de la colonne {$column}: " . $e->getMessage() . "</div>";
                }
            }
        }

        // Vérifier s'il y a des données dans la table
        $stmt = $pdo->query("SELECT COUNT(*) FROM activity_logs");
        $count = $stmt->fetchColumn();

        if ($count == 0) {
            echo "<div class='alert alert-warning'>La table activity_logs est vide. Ajoutez des enregistrements de test pour voir si tout fonctionne correctement.</div>";
        }
    } catch (PDOException $e) {
        echo "<div class='alert alert-danger'>Erreur lors de la vérification de la structure de la table: " . $e->getMessage() . "</div>";
    }
}

// Vérifier et corriger la structure de la table
checkAndFixTableStructure($pdo);

// Vérifier si la table existe, sinon la créer
if (!tableExists($pdo, 'activity_logs')) {
    createTable($pdo);
}

// Traiter les actions
$message = '';
$messageType = 'success';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_test':
                if (addTestRecord($pdo)) {
                    $message = 'Enregistrement de test ajouté avec succès.';
                } else {
                    $message = 'Erreur lors de l\'ajout de l\'enregistrement de test.';
                    $messageType = 'danger';
                }
                break;

            case 'clear':
                if (clearLogs($pdo)) {
                    $message = 'Tous les logs ont été supprimés.';
                } else {
                    $message = 'Erreur lors de la suppression des logs.';
                    $messageType = 'danger';
                }
                break;
        }
    }
}

// Récupérer les paramètres de pagination et de filtrage
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$logsPerPage = 20;
$offset = ($page - 1) * $logsPerPage;

$filters = [
    'username' => isset($_GET['username']) ? $_GET['username'] : '',
    'action' => isset($_GET['action']) ? $_GET['action'] : '',
    'entity_type' => isset($_GET['entity_type']) ? $_GET['entity_type'] : '',
    'date' => isset($_GET['date']) ? $_GET['date'] : ''
];

// Récupérer les logs
$logs = getLogs($pdo, $logsPerPage, $offset, $filters);
$totalLogs = countLogs($pdo, $filters);
$totalPages = ceil($totalLogs / $logsPerPage);

// Récupérer la liste des utilisateurs
$users = getUsers($pdo);

// Traduire les actions et entités
$actionLabels = [
    'create' => 'Création',
    'update' => 'Mise à jour',
    'delete' => 'Suppression',
    'delete_failed' => 'Suppression échouée',
    'reset' => 'Réinitialisation',
    'login' => 'Connexion',
    'logout' => 'Déconnexion',
    'test' => 'Test'
];

$entityLabels = [
    'avocat' => 'Avocat',
    'affaire' => 'Affaire',
    'database' => 'Base de données',
    'user' => 'Utilisateur'
];
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Journal d'Activité - Suivi des Affaires Juridiques</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        .action-create { color: #28a745; }
        .action-update { color: #007bff; }
        .action-delete { color: #dc3545; }
        .action-delete_failed { color: #dc3545; }
        .action-reset { color: #6c757d; }
        .action-login { color: #17a2b8; }
        .action-logout { color: #6c757d; }
        .action-test { color: #ffc107; }

        .entity-avocat { background-color: #f8f9fa; }
        .entity-affaire { background-color: #f0f0f0; }
        .entity-database { background-color: #e9ecef; }
        .entity-user { background-color: #e2e3e5; }
    </style>
</head>
<body>
    <!-- Barre de navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="app.html">Suivi des Affaires Juridiques</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="app.html">Tableau de bord</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="app.html#affaires">Affaires</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="app.html#avocats">Avocats</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin_users.html">Utilisateurs</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="admin_logs.php">Journal d'activité</a>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <span class="nav-link" id="user-info">Connecté en tant que: Admin</span>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="logout-btn">
                            <i class="bi bi-box-arrow-right"></i> Déconnexion
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Contenu principal -->
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1>Journal d'Activité</h1>
                    <div>
                        <a href="<?php echo $_SERVER['PHP_SELF']; ?>" class="btn btn-secondary">
                            <i class="bi bi-arrow-clockwise"></i> Actualiser
                        </a>
                        <form method="post" style="display: inline;" onsubmit="return confirm('Êtes-vous sûr de vouloir vider tous les logs?');">
                            <input type="hidden" name="action" value="clear">
                            <button type="submit" class="btn btn-danger">
                                <i class="bi bi-trash"></i> Vider le journal
                            </button>
                        </form>
                    </div>
                </div>

                <?php if (!empty($message)): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <!-- Filtres -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="get" action="<?php echo $_SERVER['PHP_SELF']; ?>">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="username" class="form-label">Utilisateur</label>
                                        <select class="form-select" id="username" name="username">
                                            <option value="">Tous</option>
                                            <?php foreach ($users as $user): ?>
                                            <option value="<?php echo htmlspecialchars($user); ?>" <?php echo ($filters['username'] === $user) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($user); ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="action" class="form-label">Action</label>
                                        <select class="form-select" id="action" name="action">
                                            <option value="">Toutes</option>
                                            <?php foreach ($actionLabels as $value => $label): ?>
                                            <option value="<?php echo htmlspecialchars($value); ?>" <?php echo ($filters['action'] === $value) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($label); ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="entity_type" class="form-label">Entité</label>
                                        <select class="form-select" id="entity_type" name="entity_type">
                                            <option value="">Toutes</option>
                                            <?php foreach ($entityLabels as $value => $label): ?>
                                            <option value="<?php echo htmlspecialchars($value); ?>" <?php echo ($filters['entity_type'] === $value) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($label); ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="date" class="form-label">Date</label>
                                        <input type="date" class="form-control" id="date" name="date" value="<?php echo htmlspecialchars($filters['date']); ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-funnel"></i> Appliquer les filtres
                                </button>
                                <a href="<?php echo $_SERVER['PHP_SELF']; ?>" class="btn btn-outline-secondary ms-2">
                                    <i class="bi bi-x-circle"></i> Réinitialiser
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Tableau des logs -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Date et heure</th>
                                        <th>Utilisateur</th>
                                        <th>Action</th>
                                        <th>Entité</th>
                                        <th>ID Entité</th>
                                        <th>Détails</th>
                                        <th>Adresse IP</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($logs)): ?>
                                    <tr>
                                        <td colspan="8" class="text-center">Aucune activité trouvée</td>
                                    </tr>
                                    <?php else: ?>
                                    <?php foreach ($logs as $log): ?>
                                    <tr class="entity-<?php echo htmlspecialchars($log['entity_type']); ?>">
                                        <td><?php echo htmlspecialchars($log['id']); ?></td>
                                        <td><?php echo date('d/m/Y H:i:s', strtotime($log['timestamp'])); ?></td>
                                        <td><?php echo htmlspecialchars($log['username']); ?></td>
                                        <td>
                                            <span class="action-<?php echo htmlspecialchars($log['action']); ?>">
                                                <?php echo isset($actionLabels[$log['action']]) ? htmlspecialchars($actionLabels[$log['action']]) : htmlspecialchars($log['action']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php echo isset($entityLabels[$log['entity_type']]) ? htmlspecialchars($entityLabels[$log['entity_type']]) : htmlspecialchars($log['entity_type']); ?>
                                        </td>
                                        <td><?php echo $log['entity_id'] ? htmlspecialchars($log['entity_id']) : '-'; ?></td>
                                        <td><?php echo htmlspecialchars($log['details']); ?></td>
                                        <td><?php echo htmlspecialchars($log['ip_address']); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                Affichage de <?php echo count($logs); ?> sur <?php echo $totalLogs; ?> activités
                                <?php if ($totalLogs > 0): ?>
                                (Page <?php echo $page; ?> sur <?php echo $totalPages; ?>)
                                <?php endif; ?>
                            </div>
                            <?php if ($totalPages > 1): ?>
                            <nav aria-label="Pagination">
                                <ul class="pagination">
                                    <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1<?php echo !empty($filters) ? '&' . http_build_query(array_filter($filters)) : ''; ?>" aria-label="Première">
                                            <span aria-hidden="true">&laquo;&laquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page - 1; ?><?php echo !empty($filters) ? '&' . http_build_query(array_filter($filters)) : ''; ?>" aria-label="Précédente">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                    <?php endif; ?>

                                    <?php
                                    $startPage = max(1, $page - 2);
                                    $endPage = min($totalPages, $page + 2);

                                    for ($i = $startPage; $i <= $endPage; $i++):
                                    ?>
                                    <li class="page-item <?php echo ($i === $page) ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?><?php echo !empty($filters) ? '&' . http_build_query(array_filter($filters)) : ''; ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                    <?php endfor; ?>

                                    <?php if ($page < $totalPages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page + 1; ?><?php echo !empty($filters) ? '&' . http_build_query(array_filter($filters)) : ''; ?>" aria-label="Suivante">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $totalPages; ?><?php echo !empty($filters) ? '&' . http_build_query(array_filter($filters)) : ''; ?>" aria-label="Dernière">
                                            <span aria-hidden="true">&raquo;&raquo;</span>
                                        </a>
                                    </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Ajouter un enregistrement de test -->
                <div class="mt-4">
                    <form method="post">
                        <input type="hidden" name="action" value="add_test">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="bi bi-plus-circle"></i> Ajouter un enregistrement de test
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

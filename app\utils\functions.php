<?php
/**
 * Fonctions utilitaires pour l'application
 */

// Fonction pour nettoyer les données d'entrée
function sanitize($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

// Fonction pour valider une adresse email
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// Fonction pour valider une date
function validateDate($date, $format = 'Y-m-d') {
    $d = DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) === $date;
}

// Fonction pour formater une date
function formatDate($date, $format = 'd/m/Y') {
    if (empty($date)) {
        return '';
    }
    
    $d = new DateTime($date);
    return $d->format($format);
}

// Fonction pour calculer la différence entre deux dates en jours
function dateDiffInDays($date1, $date2) {
    $d1 = new DateTime($date1);
    $d2 = new DateTime($date2);
    $diff = $d1->diff($d2);
    return $diff->days;
}

// Fonction pour vérifier si une date est expirée
function isDateExpired($date) {
    if (empty($date)) {
        return false;
    }
    
    $today = new DateTime();
    $dateToCheck = new DateTime($date);
    
    return $dateToCheck < $today;
}

// Fonction pour générer un jeton aléatoire
function generateToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

// Fonction pour journaliser une activité
function logActivity($username, $action, $entityType, $entityId = null, $details = null) {
    try {
        $pdo = getDbConnection();
        $stmt = $pdo->prepare("INSERT INTO activity_logs (username, action, entity_type, entity_id, details, ip_address)
                                VALUES (?, ?, ?, ?, ?, ?)");
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
        $stmt->execute([$username, $action, $entityType, $entityId, $details, $ipAddress]);
        return true;
    } catch (PDOException $e) {
        error_log("Erreur lors de la journalisation de l'activité: " . $e->getMessage());
        return false;
    }
}

// Fonction pour envoyer une réponse JSON
function jsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

// Fonction pour vérifier si une requête est une requête AJAX
function isAjaxRequest() {
    return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

// Fonction pour obtenir l'URL de base de l'application
function getBaseUrl() {
    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
    $domainName = $_SERVER['HTTP_HOST'];
    return $protocol . $domainName;
}
?>

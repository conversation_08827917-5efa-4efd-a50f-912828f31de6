# Système de Suivi des Affaires Juridiques

Ce système permet de gérer et suivre les affaires juridiques d'une organisation, ainsi que les avocats associés à ces affaires.

## Configuration de la base de données MySQL

### Prérequis
- Serveur MySQL (version 5.7 ou supérieure)
- PHP 7.4 ou supérieur
- Serveur web (Apache, Nginx, etc.)

### Étapes d'installation

1. **Créer la base de données**
   - Exécutez le script `schema.sql` pour créer la base de données et les tables initiales :
   ```
   mysql -u root -p < schema.sql
   ```

2. **Mettre à jour le schéma de la base de données**
   - Accédez à `http://votre-site/update_schema.php` pour mettre à jour le schéma de la base de données avec les nouvelles tables et colonnes.

3. **Initialiser les utilisateurs par défaut**
   - Acc<PERSON>dez à `http://votre-site/init_users.php` pour créer les utilisateurs par défaut :
     - Administrateur : admin / admin123
     - Utilisateur : user / user123

4. **Configurer la connexion à la base de données**
   - Modifiez le fichier `config.php` avec vos informations de connexion MySQL :
   ```php
   function getDbConnection() {
       $host = 'localhost';
       $dbname = 'suivi_affaires_juridiques';
       $username = 'root';
       $password = 'votre_mot_de_passe';
       
       $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
       $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
       
       return $pdo;
   }
   ```

## Fonctionnalités

### Gestion des affaires
- Ajout, modification et suppression d'affaires juridiques
- Filtrage et recherche d'affaires
- Suivi du statut des affaires (en cours, jugée en faveur, jugée pour tiers)

### Gestion des avocats
- Ajout, modification et suppression d'avocats
- Suivi des contrats d'avocats et de leur validité
- Mise en évidence des contrats expirés

### Tableau de bord
- Statistiques sur les affaires et les avocats
- Graphiques de répartition par nature et juridiction
- Suivi financier des affaires

### Gestion des utilisateurs
- Authentification sécurisée
- Gestion des rôles (administrateur, utilisateur)
- Verrouillage de compte après plusieurs tentatives échouées
- Récupération de mot de passe

### Journal d'activité
- Suivi de toutes les actions des utilisateurs
- Filtrage des activités par utilisateur, action, entité et date

## Sécurité

- Les mots de passe sont stockés de manière sécurisée avec la fonction `password_hash()` de PHP
- Verrouillage de compte après 5 tentatives de connexion échouées
- Seuls les administrateurs peuvent supprimer des affaires
- Toutes les actions sont journalisées pour un audit complet

## Utilisation

1. **Connexion**
   - Accédez à `http://votre-site/login.html`
   - Connectez-vous avec l'un des comptes créés

2. **Gestion des affaires**
   - Cliquez sur l'onglet "Affaires" pour gérer les affaires juridiques
   - Utilisez le bouton "Nouvelle Affaire" pour ajouter une affaire
   - Utilisez les boutons d'action pour modifier ou supprimer une affaire

3. **Gestion des avocats**
   - Cliquez sur l'onglet "Avocats" pour gérer les avocats
   - Utilisez le bouton "Nouvel Avocat" pour ajouter un avocat
   - Utilisez les boutons d'action pour modifier ou supprimer un avocat

4. **Tableau de bord**
   - Cliquez sur l'onglet "Tableau de bord" pour voir les statistiques
   - Utilisez le bouton "Exporter les données" pour exporter les données au format CSV

5. **Administration (administrateurs uniquement)**
   - Accédez à `http://votre-site/admin_users.html` pour gérer les utilisateurs
   - Accédez à `http://votre-site/admin_logs.html` pour consulter le journal d'activité

## Dépannage

Si vous rencontrez des problèmes lors de l'installation ou de l'utilisation de l'application, voici quelques solutions :

1. **Erreur de connexion à la base de données**
   - Vérifiez que les informations de connexion dans `config.php` sont correctes
   - Assurez-vous que le serveur MySQL est en cours d'exécution
   - Vérifiez que l'utilisateur MySQL a les droits nécessaires sur la base de données

2. **Erreur lors de la mise à jour du schéma**
   - Vérifiez les logs d'erreur PHP pour plus de détails
   - Assurez-vous que l'utilisateur MySQL a les droits nécessaires pour modifier la structure des tables

3. **Problèmes d'authentification**
   - Si vous avez oublié votre mot de passe, utilisez la fonction "Mot de passe oublié" sur la page de connexion
   - Si votre compte est verrouillé, attendez 30 minutes ou demandez à un administrateur de le déverrouiller

## Licence

Ce projet est sous licence MIT. Voir le fichier LICENSE pour plus de détails.

<?php
// Autoriser les requêtes CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

// Si c'est une requête OPTIONS (preflight), renvoyer 200 OK
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Démarrer la session
session_start();

// Inclure les fichiers nécessaires
require_once '../auth.php';

// Récupérer la méthode HTTP et l'action
$method = $_SERVER['REQUEST_METHOD'];
$action = isset($_GET['action']) ? $_GET['action'] : '';

// Traiter la requête en fonction de la méthode et de l'action
switch ($method) {
    case 'POST':
        // Récupérer les données JSON de la requête
        $data = json_decode(file_get_contents('php://input'), true);
        
        switch ($action) {
            case 'login':
                // Vérifier que les données nécessaires sont présentes
                if (!isset($data['username']) || !isset($data['password'])) {
                    jsonResponse(['success' => false, 'message' => 'Nom d\'utilisateur et mot de passe requis'], 400);
                }
                
                // Authentifier l'utilisateur
                $result = login($data['username'], $data['password']);
                jsonResponse($result, $result['success'] ? 200 : 401);
                break;
                
            case 'logout':
                // Déconnecter l'utilisateur
                $result = logout();
                jsonResponse($result);
                break;
                
            case 'create_user':
                // Vérifier que les données nécessaires sont présentes
                if (!isset($data['username']) || !isset($data['password']) || !isset($data['email'])) {
                    jsonResponse(['success' => false, 'message' => 'Données incomplètes'], 400);
                }
                
                // Créer un nouvel utilisateur
                $role = isset($data['role']) ? $data['role'] : 'user';
                $result = createUser($data['username'], $data['password'], $data['email'], $role);
                jsonResponse($result, $result['success'] ? 201 : 400);
                break;
                
            case 'change_password':
                // Vérifier que les données nécessaires sont présentes
                if (!isset($data['user_id']) || !isset($data['new_password'])) {
                    jsonResponse(['success' => false, 'message' => 'Données incomplètes'], 400);
                }
                
                // Changer le mot de passe
                $currentPassword = isset($data['current_password']) ? $data['current_password'] : '';
                $result = changePassword($data['user_id'], $currentPassword, $data['new_password']);
                jsonResponse($result, $result['success'] ? 200 : 400);
                break;
                
            default:
                jsonResponse(['success' => false, 'message' => 'Action non reconnue'], 400);
        }
        break;
        
    case 'GET':
        switch ($action) {
            case 'check':
                // Vérifier si l'utilisateur est authentifié
                $result = checkAuth();
                jsonResponse($result, $result['success'] ? 200 : 401);
                break;
                
            default:
                jsonResponse(['success' => false, 'message' => 'Action non reconnue'], 400);
        }
        break;
        
    default:
        jsonResponse(['success' => false, 'message' => 'Méthode non autorisée'], 405);
}

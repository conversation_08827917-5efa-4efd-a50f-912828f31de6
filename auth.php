<?php
// Démarrer la session si elle n'est pas déjà démarrée
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Inclure le fichier de configuration
require_once 'config.php';

// Fonction pour authentifier un utilisateur
function login($username, $password) {
    try {
        $pdo = getDbConnection();

        // Récupérer l'adresse IP du client
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';

        // Rechercher l'utilisateur par son nom d'utilisateur
        $stmt = $pdo->prepare("SELECT id, username, password_hash as password, role, account_locked, locked_until FROM users WHERE username = ?");
        $stmt->execute([$username]);
        $user = $stmt->fetch();

        // Vérifier si l'utilisateur existe
        if (!$user) {
            return [
                'success' => false,
                'message' => 'Nom d\'utilisateur ou mot de passe incorrect'
            ];
        }

        // Vérifier si le compte est verrouillé
        if ($user['account_locked']) {
            // Vérifier si la période de verrouillage est terminée
            if ($user['locked_until'] && new DateTime() < new DateTime($user['locked_until'])) {
                $lockedUntil = new DateTime($user['locked_until']);
                $now = new DateTime();
                $interval = $now->diff($lockedUntil);

                // Formater le temps restant
                $remainingTime = '';
                if ($interval->h > 0) {
                    $remainingTime .= $interval->h . ' heure(s) ';
                }
                if ($interval->i > 0) {
                    $remainingTime .= $interval->i . ' minute(s) ';
                }
                if ($interval->s > 0) {
                    $remainingTime .= $interval->s . ' seconde(s)';
                }

                return [
                    'success' => false,
                    'message' => 'Compte verrouillé. Veuillez réessayer dans ' . $remainingTime
                ];
            } else {
                // Déverrouiller le compte si la période de verrouillage est terminée
                $unlockStmt = $pdo->prepare("UPDATE users SET account_locked = 0, locked_until = NULL WHERE id = ?");
                $unlockStmt->execute([$user['id']]);
            }
        }

        // Vérifier si le mot de passe est correct
        if (password_verify($password, $user['password'])) {
            // Mettre à jour la date de dernière connexion
            $updateStmt = $pdo->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
            $updateStmt->execute([$user['id']]);

            // Supprimer les tentatives de connexion échouées
            $deleteStmt = $pdo->prepare("DELETE FROM login_attempts WHERE user_id = ?");
            $deleteStmt->execute([$user['id']]);

            // Enregistrer les informations de l'utilisateur dans la session
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['user_role'] = $user['role'];
            $_SESSION['last_activity'] = time(); // Initialiser le temps de dernière activité

            // Journaliser la connexion
            logActivity($user['username'], 'login', 'user', $user['id'], 'Connexion réussie');

            return [
                'success' => true,
                'user' => [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'role' => $user['role']
                ]
            ];
        } else {
            // Enregistrer la tentative de connexion échouée
            $attemptStmt = $pdo->prepare("INSERT INTO login_attempts (user_id, ip_address) VALUES (?, ?)");
            $attemptStmt->execute([$user['id'], $ipAddress]);

            // Compter le nombre de tentatives échouées dans les 30 dernières minutes
            $countStmt = $pdo->prepare("SELECT COUNT(*) FROM login_attempts WHERE user_id = ? AND attempt_time > DATE_SUB(NOW(), INTERVAL 30 MINUTE)");
            $countStmt->execute([$user['id']]);
            $attemptCount = $countStmt->fetchColumn();

            // Verrouiller le compte après 5 tentatives échouées
            if ($attemptCount >= 5) {
                // Verrouiller le compte pour 30 minutes
                $lockedUntil = new DateTime();
                $lockedUntil->add(new DateInterval('PT30M')); // 30 minutes

                $lockStmt = $pdo->prepare("UPDATE users SET account_locked = 1, locked_until = ? WHERE id = ?");
                $lockStmt->execute([$lockedUntil->format('Y-m-d H:i:s'), $user['id']]);

                // Journaliser le verrouillage du compte
                logActivity($user['username'], 'update', 'user', $user['id'], 'Compte verrouillé après 5 tentatives de connexion échouées');

                return [
                    'success' => false,
                    'message' => 'Compte verrouillé après 5 tentatives de connexion échouées. Veuillez réessayer dans 30 minutes.'
                ];
            }

            // Nombre de tentatives restantes avant verrouillage
            $remainingAttempts = 5 - $attemptCount;

            return [
                'success' => false,
                'message' => 'Nom d\'utilisateur ou mot de passe incorrect. ' . ($remainingAttempts > 0 ? "Il vous reste $remainingAttempts tentative(s)." : '')
            ];
        }
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Erreur lors de la connexion: ' . $e->getMessage()
        ];
    }
}

// Fonction pour déconnecter un utilisateur
function logout() {
    // Journaliser la déconnexion si l'utilisateur est connecté
    if (isset($_SESSION['username'])) {
        logActivity($_SESSION['username'], 'logout', 'user', $_SESSION['user_id'], 'Déconnexion');
    }

    // Détruire toutes les variables de session
    $_SESSION = [];

    // Détruire la session
    if (session_status() === PHP_SESSION_ACTIVE) {
        session_destroy();
    }

    return [
        'success' => true,
        'message' => 'Déconnexion réussie'
    ];
}

// Fonction pour vérifier si un utilisateur est connecté via API
function checkAuth() {
    if (isLoggedIn()) {
        // Rafraîchir la session en mettant à jour la date de dernière activité
        $_SESSION['last_activity'] = time();

        // Vérifier si l'utilisateur existe toujours dans la base de données
        try {
            $pdo = getDbConnection();
            $stmt = $pdo->prepare("SELECT id, username, role FROM users WHERE id = ?");
            $stmt->execute([$_SESSION['user_id']]);
            $user = $stmt->fetch();

            if ($user) {
                return [
                    'success' => true,
                    'user' => [
                        'id' => $_SESSION['user_id'],
                        'username' => $_SESSION['username'],
                        'role' => $_SESSION['user_role']
                    ]
                ];
            } else {
                // L'utilisateur n'existe plus dans la base de données
                logout();
                return [
                    'success' => false,
                    'message' => 'Utilisateur non trouvé'
                ];
            }
        } catch (PDOException $e) {
            // En cas d'erreur de base de données, on considère que l'utilisateur est toujours connecté
            return [
                'success' => true,
                'user' => [
                    'id' => $_SESSION['user_id'],
                    'username' => $_SESSION['username'],
                    'role' => $_SESSION['user_role']
                ]
            ];
        }
    }

    return [
        'success' => false,
        'message' => 'Non authentifié'
    ];
}

// Fonction pour créer un nouvel utilisateur (réservé aux administrateurs)
function createUser($username, $password, $email, $role = 'user') {
    // Vérifier si l'utilisateur actuel est administrateur
    if (!isAdmin()) {
        return [
            'success' => false,
            'message' => 'Seuls les administrateurs peuvent créer de nouveaux utilisateurs'
        ];
    }

    try {
        $pdo = getDbConnection();

        // Vérifier si le nom d'utilisateur existe déjà
        $checkStmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
        $checkStmt->execute([$username]);
        if ($checkStmt->fetch()) {
            return [
                'success' => false,
                'message' => 'Ce nom d\'utilisateur existe déjà'
            ];
        }

        // Vérifier si l'email existe déjà
        $checkStmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
        $checkStmt->execute([$email]);
        if ($checkStmt->fetch()) {
            return [
                'success' => false,
                'message' => 'Cet email est déjà utilisé'
            ];
        }

        // Hasher le mot de passe
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

        // Insérer le nouvel utilisateur
        $stmt = $pdo->prepare("INSERT INTO users (username, password_hash, email, role) VALUES (?, ?, ?, ?)");
        $stmt->execute([$username, $hashedPassword, $email, $role]);

        $userId = $pdo->lastInsertId();

        // Journaliser la création de l'utilisateur
        logActivity($_SESSION['username'], 'create', 'user', $userId, "Création de l'utilisateur {$username}");

        return [
            'success' => true,
            'message' => 'Utilisateur créé avec succès',
            'user_id' => $userId
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Erreur lors de la création de l\'utilisateur: ' . $e->getMessage()
        ];
    }
}

// Fonction pour vérifier si un utilisateur est connecté
function isLoggedIn() {
    // Vérifier si les variables de session existent
    $sessionValid = isset($_SESSION['user_id']) && isset($_SESSION['username']) && isset($_SESSION['user_role']);

    if (!$sessionValid) {
        return false;
    }

    // Vérifier si la session n'a pas expiré
    if (isset($_SESSION['last_activity'])) {
        $sessionLifetime = 86400; // 24 heures en secondes
        if (time() - $_SESSION['last_activity'] > $sessionLifetime) {
            // La session a expiré, déconnecter l'utilisateur
            logout();
            return false;
        }
    }

    // Mettre à jour le temps de dernière activité
    $_SESSION['last_activity'] = time();

    return true;
}

// Fonction pour vérifier si l'utilisateur connecté est un administrateur
function isAdmin() {
    return isLoggedIn() && $_SESSION['user_role'] === 'admin';
}

function changePassword($userId, $currentPassword, $newPassword) {
    // Vérifier si l'utilisateur est connecté
    if (!isLoggedIn()) {
        return [
            'success' => false,
            'message' => 'Vous devez être connecté pour changer votre mot de passe'
        ];
    }

    // Vérifier si l'utilisateur est administrateur ou s'il change son propre mot de passe
    if (!isAdmin() && $_SESSION['user_id'] != $userId) {
        return [
            'success' => false,
            'message' => 'Vous n\'êtes pas autorisé à changer le mot de passe de cet utilisateur'
        ];
    }

    try {
        $pdo = getDbConnection();

        // Si l'utilisateur change son propre mot de passe, vérifier le mot de passe actuel
        if ($_SESSION['user_id'] == $userId) {
            $stmt = $pdo->prepare("SELECT password_hash as password FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch();

            if (!$user || !password_verify($currentPassword, $user['password'])) {
                return [
                    'success' => false,
                    'message' => 'Mot de passe actuel incorrect'
                ];
            }
        }

        // Hasher le nouveau mot de passe
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);

        // Mettre à jour le mot de passe
        $stmt = $pdo->prepare("UPDATE users SET password_hash = ? WHERE id = ?");
        $stmt->execute([$hashedPassword, $userId]);

        // Journaliser le changement de mot de passe
        logActivity($_SESSION['username'], 'update', 'user', $userId, "Changement de mot de passe");

        return [
            'success' => true,
            'message' => 'Mot de passe changé avec succès'
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Erreur lors du changement de mot de passe: ' . $e->getMessage()
        ];
    }
}

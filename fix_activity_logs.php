<?php
// Script pour vérifier et corriger la table activity_logs

// Activer l'affichage des erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Inclure les fichiers nécessaires
require_once __DIR__ . '/config.php';

// Fonction pour vérifier si une table existe
function tableExists($pdo, $table) {
    try {
        $result = $pdo->query("SHOW TABLES LIKE '{$table}'");
        return $result->rowCount() > 0;
    } catch (Exception $e) {
        echo "<p>Erreur lors de la vérification de la table {$table}: " . $e->getMessage() . "</p>";
        return false;
    }
}

// Fonction pour vérifier la structure d'une table
function getTableStructure($pdo, $table) {
    try {
        $result = $pdo->query("DESCRIBE {$table}");
        return $result->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        echo "<p>Erreur lors de la récupération de la structure de la table {$table}: " . $e->getMessage() . "</p>";
        return [];
    }
}

// Fonction pour créer la table activity_logs
function createActivityLogsTable($pdo) {
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS activity_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL,
                action ENUM('create', 'update', 'delete', 'delete_failed', 'reset', 'login', 'logout', 'test') NOT NULL,
                entity_type ENUM('avocat', 'affaire', 'database', 'user') NOT NULL,
                entity_id INT,
                details TEXT,
                ip_address VARCHAR(45),
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB;
        ");
        
        echo "<p>Table activity_logs créée avec succès.</p>";
        return true;
    } catch (Exception $e) {
        echo "<p>Erreur lors de la création de la table activity_logs: " . $e->getMessage() . "</p>";
        return false;
    }
}

// Fonction pour ajouter des données de test
function addTestData($pdo) {
    try {
        // Ajouter quelques entrées de test
        $entries = [
            ['admin', 'login', 'user', 1, 'Connexion réussie', '127.0.0.1'],
            ['admin', 'create', 'avocat', 1, 'Création de l\'avocat: Me Dupont', '127.0.0.1'],
            ['admin', 'update', 'avocat', 1, 'Mise à jour de l\'avocat: Me Dupont', '127.0.0.1'],
            ['admin', 'create', 'affaire', 1, 'Création de l\'affaire: Dossier 123', '127.0.0.1'],
            ['admin', 'update', 'affaire', 1, 'Mise à jour de l\'affaire: Dossier 123', '127.0.0.1'],
            ['admin', 'logout', 'user', 1, 'Déconnexion', '127.0.0.1']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO activity_logs (username, action, entity_type, entity_id, details, ip_address) VALUES (?, ?, ?, ?, ?, ?)");
        
        $count = 0;
        foreach ($entries as $entry) {
            $stmt->execute($entry);
            $count++;
        }
        
        echo "<p>{$count} entrées de test ajoutées avec succès.</p>";
        return true;
    } catch (Exception $e) {
        echo "<p>Erreur lors de l'ajout des données de test: " . $e->getMessage() . "</p>";
        return false;
    }
}

// Fonction pour vider la table
function truncateTable($pdo, $table) {
    try {
        $pdo->exec("TRUNCATE TABLE {$table}");
        echo "<p>Table {$table} vidée avec succès.</p>";
        return true;
    } catch (Exception $e) {
        echo "<p>Erreur lors du vidage de la table {$table}: " . $e->getMessage() . "</p>";
        return false;
    }
}

// Fonction pour compter les enregistrements
function countRecords($pdo, $table) {
    try {
        $result = $pdo->query("SELECT COUNT(*) FROM {$table}");
        return $result->fetchColumn();
    } catch (Exception $e) {
        echo "<p>Erreur lors du comptage des enregistrements dans la table {$table}: " . $e->getMessage() . "</p>";
        return 0;
    }
}

// Fonction pour afficher les derniers enregistrements
function displayLastRecords($pdo, $table, $limit = 10) {
    try {
        $stmt = $pdo->query("SELECT * FROM {$table} ORDER BY id DESC LIMIT {$limit}");
        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>Les {$limit} derniers enregistrements:</h3>";
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        
        // En-têtes de colonnes
        if (!empty($records)) {
            echo "<tr>";
            foreach (array_keys($records[0]) as $column) {
                echo "<th>{$column}</th>";
            }
            echo "</tr>";
            
            // Données
            foreach ($records as $record) {
                echo "<tr>";
                foreach ($record as $value) {
                    echo "<td>" . htmlspecialchars($value) . "</td>";
                }
                echo "</tr>";
            }
        } else {
            echo "<tr><td>Aucun enregistrement trouvé</td></tr>";
        }
        
        echo "</table>";
    } catch (Exception $e) {
        echo "<p>Erreur lors de l'affichage des derniers enregistrements: " . $e->getMessage() . "</p>";
    }
}

// Fonction principale
function main() {
    try {
        // Se connecter à la base de données
        $pdo = getDbConnection();
        echo "<p>Connexion à la base de données réussie.</p>";
        
        // Vérifier si la table activity_logs existe
        $tableExists = tableExists($pdo, 'activity_logs');
        echo "<p>Table activity_logs: " . ($tableExists ? "Existe" : "N'existe pas") . "</p>";
        
        // Si la table n'existe pas, la créer
        if (!$tableExists) {
            createActivityLogsTable($pdo);
            $tableExists = tableExists($pdo, 'activity_logs');
        }
        
        // Si la table existe, afficher sa structure
        if ($tableExists) {
            $structure = getTableStructure($pdo, 'activity_logs');
            
            echo "<h3>Structure de la table activity_logs:</h3>";
            echo "<table border='1' cellpadding='5' cellspacing='0'>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
            
            foreach ($structure as $column) {
                echo "<tr>";
                foreach ($column as $key => $value) {
                    echo "<td>" . htmlspecialchars($value) . "</td>";
                }
                echo "</tr>";
            }
            
            echo "</table>";
            
            // Compter les enregistrements
            $count = countRecords($pdo, 'activity_logs');
            echo "<p>Nombre d'enregistrements: {$count}</p>";
            
            // Afficher les derniers enregistrements
            if ($count > 0) {
                displayLastRecords($pdo, 'activity_logs');
            }
            
            // Proposer de vider la table et d'ajouter des données de test
            echo "<h3>Actions:</h3>";
            echo "<form method='post'>";
            echo "<button type='submit' name='action' value='truncate'>Vider la table</button> ";
            echo "<button type='submit' name='action' value='add_test_data'>Ajouter des données de test</button>";
            echo "</form>";
            
            // Traiter les actions
            if (isset($_POST['action'])) {
                switch ($_POST['action']) {
                    case 'truncate':
                        truncateTable($pdo, 'activity_logs');
                        break;
                    case 'add_test_data':
                        addTestData($pdo);
                        break;
                }
                
                // Rafraîchir la page
                echo "<script>window.location.href = window.location.pathname;</script>";
            }
        }
        
        // Liens utiles
        echo "<h3>Liens utiles:</h3>";
        echo "<ul>";
        echo "<li><a href='views/admin/admin_logs.html' target='_blank'>Page du journal d'activité</a></li>";
        echo "<li><a href='api/logs_api.php?action=list' target='_blank'>API des journaux d'activité</a></li>";
        echo "</ul>";
    } catch (Exception $e) {
        echo "<p>Erreur: " . $e->getMessage() . "</p>";
    }
}

// Exécuter la fonction principale
echo "<h1>Vérification et correction de la table activity_logs</h1>";
main();

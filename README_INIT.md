# Instructions d'initialisation de la base de données

Ce document explique comment initialiser la base de données pour le système de suivi des affaires juridiques.

## Prérequis

- Laragon installé et configuré
- MySQL activé dans Laragon
- PHP activé dans Laragon

## Étapes d'initialisation

1. Assurez-vous que Laragon est démarré (MySQL et Apache doivent être en cours d'exécution)

2. Ouvrez votre navigateur et accédez à l'URL suivante:
   ```
   http://localhost/init_db.php
   ```

3. Le script va:
   - Créer la base de données `suivi_affaires_juridiques` si elle n'existe pas
   - Créer les tables nécessaires
   - Créer un utilisateur administrateur par défaut

4. Une fois l'initialisation terminée, vous verrez un message de confirmation avec les identifiants de connexion:
   - Nom d'utilisateur: `admin`
   - Mot de passe: `password`

5. Cliquez sur le lien "Aller à la page de connexion" pour vous connecter à l'application

## Résolution des problèmes

Si vous rencontrez des erreurs lors de l'initialisation:

1. Vérifiez que MySQL est bien démarré dans Laragon
2. Vérifiez que les identifiants de connexion à MySQL dans le fichier `config.php` sont corrects
3. Assurez-vous que la base de données `suivi_affaires_juridiques` n'existe pas déjà (ou supprimez-la si c'est le cas)
4. Vérifiez les messages d'erreur affichés par le script d'initialisation

## Réinitialisation de la base de données

Si vous souhaitez réinitialiser complètement la base de données:

1. Connectez-vous à MySQL via phpMyAdmin (accessible depuis Laragon)
2. Supprimez la base de données `suivi_affaires_juridiques`
3. Exécutez à nouveau le script d'initialisation comme indiqué ci-dessus

<?php
/**
 * Fonctions du thème Cabinet Juridique
 */

// Empêcher l'accès direct
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Configuration du thème
 */
function juridique_theme_setup() {
    // Support des images à la une
    add_theme_support('post-thumbnails');
    
    // Support du titre automatique
    add_theme_support('title-tag');
    
    // Support des formats de contenu
    add_theme_support('post-formats', array(
        'aside',
        'gallery',
        'link',
        'image',
        'quote',
        'status',
        'video',
        'audio',
        'chat'
    ));
    
    // Support HTML5
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
        'style',
        'script'
    ));
    
    // Support des menus
    register_nav_menus(array(
        'menu-1' => esc_html__('Menu principal', 'juridique'),
        'footer-menu' => esc_html__('Menu footer', 'juridique'),
    ));
    
    // Support des widgets
    add_theme_support('widgets');
    
    // Support de l'éditeur de blocs
    add_theme_support('wp-block-styles');
    add_theme_support('align-wide');
    add_theme_support('editor-styles');
    add_editor_style('editor-style.css');
}
add_action('after_setup_theme', 'juridique_theme_setup');

/**
 * Enregistrement des zones de widgets
 */
function juridique_widgets_init() {
    register_sidebar(array(
        'name'          => esc_html__('Sidebar', 'juridique'),
        'id'            => 'sidebar-1',
        'description'   => esc_html__('Ajoutez des widgets ici.', 'juridique'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h2 class="widget-title">',
        'after_title'   => '</h2>',
    ));
    
    register_sidebar(array(
        'name'          => esc_html__('Footer 1', 'juridique'),
        'id'            => 'footer-1',
        'description'   => esc_html__('Zone de widgets du footer 1.', 'juridique'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
    
    register_sidebar(array(
        'name'          => esc_html__('Footer 2', 'juridique'),
        'id'            => 'footer-2',
        'description'   => esc_html__('Zone de widgets du footer 2.', 'juridique'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
}
add_action('widgets_init', 'juridique_widgets_init');

/**
 * Chargement des styles et scripts
 */
function juridique_scripts() {
    // Style principal
    wp_enqueue_style('juridique-style', get_stylesheet_uri(), array(), '1.0.0');
    
    // Scripts personnalisés
    wp_enqueue_script('juridique-navigation', get_template_directory_uri() . '/js/navigation.js', array('jquery'), '1.0.0', true);
    
    // Script pour les commentaires
    if (is_singular() && comments_open() && get_option('thread_comments')) {
        wp_enqueue_script('comment-reply');
    }
    
    // Intégration avec le système juridique
    if (is_user_logged_in()) {
        wp_enqueue_script('juridique-integration', get_template_directory_uri() . '/js/integration.js', array('jquery'), '1.0.0', true);
        wp_localize_script('juridique-integration', 'juridique_theme', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('juridique_theme_nonce'),
            'app_url' => defined('JURIDIQUE_APP_URL') ? JURIDIQUE_APP_URL : ''
        ));
    }
}
add_action('wp_enqueue_scripts', 'juridique_scripts');

/**
 * Personnalisation de l'extrait
 */
function juridique_excerpt_length($length) {
    return 30;
}
add_filter('excerpt_length', 'juridique_excerpt_length', 999);

function juridique_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'juridique_excerpt_more');

/**
 * Ajout de classes CSS personnalisées
 */
function juridique_body_classes($classes) {
    // Ajouter une classe si l'utilisateur est connecté
    if (is_user_logged_in()) {
        $classes[] = 'user-logged-in';
    }
    
    // Ajouter une classe pour le type de contenu
    if (is_singular('article_juridique')) {
        $classes[] = 'single-article-juridique';
    }
    
    if (is_singular('actualite_cabinet')) {
        $classes[] = 'single-actualite-cabinet';
    }
    
    return $classes;
}
add_filter('body_class', 'juridique_body_classes');

/**
 * Personnalisation du login WordPress
 */
function juridique_login_logo() {
    ?>
    <style type="text/css">
        #login h1 a, .login h1 a {
            background-image: none;
            background-color: #2c3e50;
            color: white;
            text-decoration: none;
            width: 320px;
            height: 60px;
            line-height: 60px;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
        }
        #login h1 a:before, .login h1 a:before {
            content: "Cabinet Juridique";
        }
        .login form {
            border: 1px solid #2c3e50;
        }
        .wp-core-ui .button-primary {
            background: #3498db;
            border-color: #2980b9;
        }
        .wp-core-ui .button-primary:hover {
            background: #2980b9;
        }
    </style>
    <?php
}
add_action('login_enqueue_scripts', 'juridique_login_logo');

/**
 * Redirection après connexion
 */
function juridique_login_redirect($redirect_to, $request, $user) {
    // Rediriger vers la page d'accueil après connexion
    if (isset($user->roles) && is_array($user->roles)) {
        if (in_array('administrator', $user->roles)) {
            return admin_url('admin.php?page=juridique-integration');
        } else {
            return home_url();
        }
    }
    return $redirect_to;
}
add_filter('login_redirect', 'juridique_login_redirect', 10, 3);

/**
 * Ajout de shortcodes personnalisés
 */
function juridique_contact_shortcode($atts) {
    $atts = shortcode_atts(array(
        'style' => 'default'
    ), $atts);
    
    ob_start();
    ?>
    <div class="juridique-contact-widget">
        <div style="text-align: center; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
            <h3>Contactez-nous</h3>
            <p><strong>Téléphone :</strong> +33 1 23 45 67 89</p>
            <p><strong>Email :</strong> <EMAIL></p>
            <a href="<?php echo home_url('/contact/'); ?>" class="juridique-btn">Formulaire de contact</a>
        </div>
    </div>
    <?php
    return ob_get_clean();
}
add_shortcode('juridique_contact', 'juridique_contact_shortcode');

/**
 * Ajout d'un widget personnalisé pour les horaires
 */
class Juridique_Horaires_Widget extends WP_Widget {
    
    function __construct() {
        parent::__construct(
            'juridique_horaires',
            'Horaires du Cabinet',
            array('description' => 'Affiche les horaires d\'ouverture du cabinet')
        );
    }
    
    public function widget($args, $instance) {
        echo $args['before_widget'];
        if (!empty($instance['title'])) {
            echo $args['before_title'] . apply_filters('widget_title', $instance['title']) . $args['after_title'];
        }
        ?>
        <table style="width: 100%; font-size: 0.9rem;">
            <tr><td><strong>Lundi - Vendredi</strong></td><td>9h00 - 18h00</td></tr>
            <tr><td><strong>Samedi</strong></td><td>9h00 - 12h00</td></tr>
            <tr><td><strong>Dimanche</strong></td><td>Fermé</td></tr>
        </table>
        <?php
        echo $args['after_widget'];
    }
    
    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : 'Horaires d\'ouverture';
        ?>
        <p>
            <label for="<?php echo $this->get_field_id('title'); ?>">Titre:</label>
            <input class="widefat" id="<?php echo $this->get_field_id('title'); ?>" name="<?php echo $this->get_field_name('title'); ?>" type="text" value="<?php echo esc_attr($title); ?>">
        </p>
        <?php
    }
    
    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? strip_tags($new_instance['title']) : '';
        return $instance;
    }
}

function register_juridique_widgets() {
    register_widget('Juridique_Horaires_Widget');
}
add_action('widgets_init', 'register_juridique_widgets');

/**
 * Personnalisation de l'administration
 */
function juridique_admin_bar_menu($wp_admin_bar) {
    if (is_user_logged_in()) {
        $wp_admin_bar->add_menu(array(
            'id'    => 'juridique-system',
            'title' => 'Système Juridique',
            'href'  => admin_url('admin.php?page=juridique-integration'),
            'meta'  => array(
                'title' => 'Accéder au système de gestion juridique'
            )
        ));
    }
}
add_action('admin_bar_menu', 'juridique_admin_bar_menu', 999);

/**
 * Sécurité : Masquer la version de WordPress
 */
remove_action('wp_head', 'wp_generator');

/**
 * Optimisation : Supprimer les emojis si non nécessaires
 */
function juridique_disable_emojis() {
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('admin_print_scripts', 'print_emoji_detection_script');
    remove_action('wp_print_styles', 'print_emoji_styles');
    remove_action('admin_print_styles', 'print_emoji_styles');
}
add_action('init', 'juridique_disable_emojis');

<?php
/**
 * Configuration de l'application
 * Ce fichier contient les paramètres de configuration de l'application
 */

// Configuration de la base de données
define('DB_HOST', 'localhost');
define('DB_NAME', 'suivi_affaires_juridiques');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_PORT', '3306');

// Configuration de l'application
define('APP_NAME', 'Système de Suivi des Affaires Juridiques');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'http://' . $_SERVER['SERVER_NAME']); // Utilise l'adresse IP du serveur
define('APP_TIMEZONE', 'Europe/Paris');
define('APP_DEBUG', true);

// Configuration des chemins
define('ROOT_PATH', dirname(dirname(__DIR__)));
define('APP_PATH', ROOT_PATH . '/app');
define('PUBLIC_PATH', ROOT_PATH . '/public');
define('VIEWS_PATH', ROOT_PATH . '/views');
define('ASSETS_PATH', ROOT_PATH . '/assets');

// Configuration de la session
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0); // Mettre à 1 en production avec HTTPS
ini_set('session.cookie_samesite', 'Lax'); // Changé de 'Strict' à 'Lax' pour une meilleure compatibilité
ini_set('session.gc_maxlifetime', 86400); // 24 heures au lieu de 1 heure
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/',
    'domain' => '',
    'secure' => false, // Mettre à true en production avec HTTPS
    'httponly' => true,
    'samesite' => 'Lax'
]);

// Configuration du fuseau horaire
date_default_timezone_set(APP_TIMEZONE);

// Configuration des erreurs
if (APP_DEBUG) {
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);
} else {
    ini_set('display_errors', 0);
    ini_set('display_startup_errors', 0);
    error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED & ~E_STRICT & ~E_WARNING);
}

/**
 * Fonction pour obtenir une connexion à la base de données
 * @return PDO Instance de PDO pour la connexion à la base de données
 */
function getDbConnection() {
    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST .
            ";port=" . DB_PORT .
            ";dbname=" . DB_NAME .
            ";charset=utf8",
            DB_USER,
            DB_PASS
        );
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch (PDOException $e) {
        if (APP_DEBUG) {
            die("Erreur de connexion à la base de données : " . $e->getMessage());
        } else {
            die("Erreur de connexion à la base de données. Veuillez contacter l'administrateur.");
        }
    }
}

/**
 * Fonction pour envoyer une réponse JSON
 * @param mixed $data Données à envoyer
 * @param int $statusCode Code de statut HTTP
 */
function jsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

/**
 * Fonction pour journaliser une activité
 * @param string $username Nom d'utilisateur
 * @param string $action Action effectuée
 * @param string $entityType Type d'entité
 * @param int|null $entityId ID de l'entité
 * @param string|null $details Détails supplémentaires
 * @return bool Succès ou échec
 */
function logActivity($username, $action, $entityType, $entityId = null, $details = null) {
    try {
        $pdo = getDbConnection();
        $stmt = $pdo->prepare("INSERT INTO activity_logs (username, action, entity_type, entity_id, details, ip_address)
                                VALUES (?, ?, ?, ?, ?, ?)");
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
        $stmt->execute([$username, $action, $entityType, $entityId, $details, $ipAddress]);
        return true;
    } catch (PDOException $e) {
        error_log("Erreur lors de la journalisation de l'activité: " . $e->getMessage());
        return false;
    }
}


<?php

/**
 * Script simple pour réinitialiser le mot de passe administrateur
 * Usage: php reset_admin_password.php
 */

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use Illuminate\Support\Facades\Hash;

// Charger l'application Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== RÉINITIALISATION DU MOT DE PASSE ADMINISTRATEUR ===\n\n";

// Configuration par défaut
$defaultEmail = '<EMAIL>';
$defaultPassword = 'admin123';

echo "Email par défaut: {$defaultEmail}\n";
echo "Mot de passe par défaut: {$defaultPassword}\n\n";

// Demander confirmation
echo "Voulez-vous continuer avec ces paramètres par défaut ? (y/n): ";
$handle = fopen("php://stdin", "r");
$line = fgets($handle);
fclose($handle);

if (trim($line) !== 'y' && trim($line) !== 'Y') {
    echo "Opération annulée.\n";
    exit(1);
}

try {
    // Chercher l'utilisateur existant
    $user = User::where('email', $defaultEmail)->first();

    if ($user) {
        // Mettre à jour le mot de passe
        $user->update([
            'password' => Hash::make($defaultPassword),
        ]);
        echo "✅ Mot de passe mis à jour avec succès !\n";
    } else {
        // Créer un nouvel utilisateur
        $user = User::create([
            'name' => 'Administrateur SIRH',
            'email' => $defaultEmail,
            'password' => Hash::make($defaultPassword),
            'email_verified_at' => now(),
        ]);
        echo "✅ Nouvel utilisateur administrateur créé avec succès !\n";
    }

    echo "\n=== INFORMATIONS DE CONNEXION ===\n";
    echo "Email: {$user->email}\n";
    echo "Mot de passe: {$defaultPassword}\n";
    echo "Nom: {$user->name}\n";
    echo "Créé le: " . $user->created_at->format('d/m/Y H:i:s') . "\n";

} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n✅ Opération terminée avec succès !\n";

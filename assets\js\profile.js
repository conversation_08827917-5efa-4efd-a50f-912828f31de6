// Script pour la gestion du profil utilisateur (profile.js)

// Vérifier si l'utilisateur est connecté
document.addEventListener('DOMContentLoaded', async () => {
    // Vérifier si l'utilisateur est connecté
    const currentUser = localStorage.getItem('currentUser');
    if (!currentUser) {
        // Rediriger vers la page de connexion si l'utilisateur n'est pas connecté
        window.location.href = 'login.html';
        return;
    }

    // Récupérer les informations de l'utilisateur
    const userData = JSON.parse(currentUser);
    
    // Afficher les informations de l'utilisateur connecté
    const userInfoElement = document.getElementById('user-info');
    if (userInfoElement) {
        userInfoElement.textContent = `Connecté en tant que: ${userData.username}`;
    }

    // Afficher les éléments réservés aux administrateurs si l'utilisateur est administrateur
    if (userData.role === 'admin') {
        document.querySelectorAll('.admin-only').forEach(element => {
            element.style.display = 'block';
        });
    }

    // Gérer le bouton de déconnexion
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', () => {
            logout()
                .then(() => {
                    window.location.href = 'login.html';
                })
                .catch(error => {
                    console.error('Erreur lors de la déconnexion:', error);
                    window.location.href = 'login.html';
                });
        });
    }

    // Charger les informations du profil
    await loadProfile();

    // Gérer la soumission du formulaire de profil
    const profileForm = document.getElementById('profile-form');
    profileForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        // Récupérer les valeurs du formulaire
        const email = document.getElementById('email').value;
        const currentPassword = document.getElementById('current-password').value;
        const newPassword = document.getElementById('new-password').value;
        const confirmPassword = document.getElementById('confirm-password').value;
        
        // Valider les données
        if (newPassword && newPassword.length < 8) {
            showAlert('Le nouveau mot de passe doit contenir au moins 8 caractères.', 'error');
            return;
        }
        
        if (newPassword && newPassword !== confirmPassword) {
            showAlert('Les mots de passe ne correspondent pas.', 'error');
            return;
        }
        
        // Si un nouveau mot de passe est fourni, le mot de passe actuel est requis
        if (newPassword && !currentPassword) {
            showAlert('Veuillez saisir votre mot de passe actuel pour le changer.', 'error');
            return;
        }
        
        try {
            // Mettre à jour le profil
            await updateProfile(userData.id, email, currentPassword, newPassword);
            
            // Réinitialiser les champs de mot de passe
            document.getElementById('current-password').value = '';
            document.getElementById('new-password').value = '';
            document.getElementById('confirm-password').value = '';
            
            showAlert('Profil mis à jour avec succès.', 'success');
        } catch (error) {
            showAlert(error.message || 'Erreur lors de la mise à jour du profil.', 'error');
        }
    });
});

// Fonction pour charger les informations du profil
async function loadProfile() {
    try {
        const userData = JSON.parse(localStorage.getItem('currentUser'));
        
        const response = await fetch(`api/profile_api.php?action=get&id=${userData.id}`, {
            method: 'GET',
            credentials: 'include'
        });

        const data = await response.json();

        if (data.success) {
            // Remplir le formulaire avec les données de l'utilisateur
            document.getElementById('username').value = data.user.username;
            document.getElementById('email').value = data.user.email;
            document.getElementById('role').value = data.user.role === 'admin' ? 'Administrateur' : 'Utilisateur';
            
            // Formater la date de dernière connexion
            const lastLogin = data.user.last_login 
                ? new Date(data.user.last_login).toLocaleString('fr-FR') 
                : 'Jamais';
            document.getElementById('last-login').value = lastLogin;
        } else {
            showAlert(data.message || 'Erreur lors du chargement du profil.', 'error');
        }
    } catch (error) {
        console.error('Erreur lors du chargement du profil:', error);
        showAlert('Erreur de connexion au serveur.', 'error');
    }
}

// Fonction pour mettre à jour le profil
async function updateProfile(userId, email, currentPassword, newPassword) {
    const userData = {
        user_id: userId,
        email: email
    };
    
    // Ajouter les mots de passe uniquement s'ils sont fournis
    if (newPassword) {
        userData.current_password = currentPassword;
        userData.new_password = newPassword;
    }
    
    const response = await fetch('api/profile_api.php?action=update', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(userData),
        credentials: 'include'
    });

    const data = await response.json();

    if (!data.success) {
        throw new Error(data.message || 'Erreur lors de la mise à jour du profil.');
    }
    
    // Si l'email a été modifié, mettre à jour les informations dans le localStorage
    if (email) {
        const currentUser = JSON.parse(localStorage.getItem('currentUser'));
        currentUser.email = email;
        localStorage.setItem('currentUser', JSON.stringify(currentUser));
    }

    return data;
}

// Fonction pour afficher une alerte
function showAlert(message, type) {
    const successAlert = document.getElementById('success-alert');
    const errorAlert = document.getElementById('error-alert');
    
    // Masquer les deux alertes
    successAlert.style.display = 'none';
    errorAlert.style.display = 'none';
    
    if (type === 'success') {
        successAlert.textContent = message;
        successAlert.style.display = 'block';
        
        // Masquer l'alerte après 5 secondes
        setTimeout(() => {
            successAlert.style.display = 'none';
        }, 5000);
    } else {
        errorAlert.textContent = message;
        errorAlert.style.display = 'block';
        
        // Masquer l'alerte après 5 secondes
        setTimeout(() => {
            errorAlert.style.display = 'none';
        }, 5000);
    }
}

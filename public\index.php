<?php
/**
 * Point d'entrée principal de l'application
 * Ce fichier charge les dépendances nécessaires et initialise l'application
 */

// Définir le chemin de base de l'application
define('BASE_PATH', dirname(__DIR__));

// Charger la configuration
require_once BASE_PATH . '/app/config/config.php';

// Vérifier si l'utilisateur est connecté
session_start();
$isLoggedIn = isset($_SESSION['user_id']) && isset($_SESSION['username']) && isset($_SESSION['user_role']);

// Rediriger vers la page de connexion si l'utilisateur n'est pas connecté
if (!$isLoggedIn && basename($_SERVER['PHP_SELF']) !== 'login.php') {
    // Exceptions pour les pages qui ne nécessitent pas d'authentification
    $publicPages = ['login.php', 'reset_password.php', 'test_db.php', 'init_db.php'];
    $currentPage = basename($_SERVER['PHP_SELF']);

    if (!in_array($currentPage, $publicPages)) {
        header('Location: index.php?page=login');
        exit;
    }
}

// Déterminer quelle page afficher
$page = isset($_GET['page']) ? $_GET['page'] : 'dashboard';

// Mapper les pages aux fichiers de vue
$pageMap = [
    'dashboard' => BASE_PATH . '/views/app/app.html',
    'login' => BASE_PATH . '/views/auth/login.html',
    'reset_password' => BASE_PATH . '/views/auth/reset_password.html',
    'admin_logs' => BASE_PATH . '/views/admin/admin_logs.html',
    'admin_users' => BASE_PATH . '/views/admin/admin_users.html',
    'profile' => BASE_PATH . '/views/app/profile.html'
];

// Vérifier si la page demandée existe
if (isset($pageMap[$page])) {
    // Vérifier les permissions pour les pages d'administration
    if (strpos($page, 'admin_') === 0 && (!$isLoggedIn || $_SESSION['user_role'] !== 'admin')) {
        header('HTTP/1.1 403 Forbidden');
        echo 'Accès refusé. Vous devez être administrateur pour accéder à cette page.';
        exit;
    }

    // Inclure la page demandée
    include $pageMap[$page];
} else {
    // Page non trouvée
    header('HTTP/1.1 404 Not Found');
    echo 'Page non trouvée.';
}
?>

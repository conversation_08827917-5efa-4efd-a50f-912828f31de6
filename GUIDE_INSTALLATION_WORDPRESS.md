# Guide d'installation WordPress pour le Système Juridique

Ce guide vous explique comment installer et configurer WordPress en local avec Laragon pour intégrer votre système de gestion des affaires juridiques.

## Prérequis

- Laragon installé et fonctionnel
- Votre application juridique existante dans `C:\laragon\www\juridique-app\`
- MySQL et Apache activés dans Laragon

## Étape 1 : Installation de WordPress

### 1.1 Créer une nouvelle installation WordPress

1. **Ouvrir <PERSON>**
2. **Clic droit sur l'icône Laragon** → `Quick app` → `WordPress`
3. **Nommer le projet** : `juridique-cms`
4. **Attendre la fin de l'installation**

### 1.2 Configuration de la base de données

1. **Ouvrir phpMyAdmin** : `http://localhost/phpmyadmin`
2. **Créer une nouvelle base de données** : `juridique_cms`
3. **Configurer WordPress** :
   - URL : `http://juridique-cms.test`
   - Base de données : `juridique_cms`
   - Utilisateur : `root`
   - Mot de passe : (laisser vide)
   - Hôte : `localhost`

## Étape 2 : Installation du thème et du plugin

### 2.1 Installation du thème personnalisé

1. **Copier le dossier du thème** :
   ```
   Copier le contenu de wp-theme/ vers :
   C:\laragon\www\juridique-cms\wp-content\themes\cabinet-juridique\
   ```

2. **Activer le thème** :
   - Aller dans `Apparence` → `Thèmes`
   - Activer le thème "Cabinet Juridique"

### 2.2 Installation du plugin d'intégration

1. **Copier le plugin** :
   ```
   Copier wp-juridique-integration.php vers :
   C:\laragon\www\juridique-cms\wp-content\plugins\juridique-integration\wp-juridique-integration.php
   ```

2. **Créer le dossier assets** :
   ```
   Créer : C:\laragon\www\juridique-cms\wp-content\plugins\juridique-integration\assets\
   Copier wp-assets/css/ vers assets/css/
   Copier wp-assets/js/ vers assets/js/
   ```

3. **Activer le plugin** :
   - Aller dans `Extensions` → `Extensions installées`
   - Activer "Intégration Système Juridique"

## Étape 3 : Configuration du plugin

### 3.1 Configuration de base

1. **Aller dans** `Système Juridique` → `Configuration`
2. **Configurer les paramètres** :
   - URL de l'application : `http://localhost/juridique-app`
   - Clé API : (optionnel, laisser vide pour l'instant)
   - Activer la synchronisation automatique : ✓
   - Intervalle : 5 minutes

3. **Sauvegarder la configuration**

### 3.2 Test de l'intégration

1. **Aller dans** `Système Juridique` → `Synchronisation`
2. **Tester la synchronisation** :
   - Cliquer sur "Synchroniser les affaires"
   - Cliquer sur "Synchroniser les avocats"
   - Vérifier que les données sont synchronisées

## Étape 4 : Configuration du contenu

### 4.1 Création des pages principales

Créer les pages suivantes dans `Pages` → `Ajouter` :

1. **Page d'accueil**
   - Titre : "Accueil"
   - Contenu : Présentation du cabinet
   - Shortcode : `[juridique_stats]` pour afficher les statistiques

2. **Page Contact**
   - Titre : "Contact"
   - Contenu : Formulaire de contact
   - Shortcode : `[juridique_contact]`

3. **Page Mentions légales**
   - Titre : "Mentions légales"
   - Contenu : Mentions légales du cabinet

### 4.2 Configuration des menus

1. **Aller dans** `Apparence` → `Menus`
2. **Créer un nouveau menu** : "Menu principal"
3. **Ajouter les éléments** :
   - Accueil
   - Articles Juridiques
   - Actualités
   - Contact
   - Système Juridique (pour les utilisateurs connectés)

4. **Assigner au menu principal**

### 4.3 Configuration des widgets

1. **Aller dans** `Apparence` → `Widgets`
2. **Configurer la sidebar** avec :
   - Widget "Statistiques Système Juridique" (automatique)
   - Widget "Articles récents"
   - Widget "Horaires du Cabinet"
   - Widget "Contact rapide"

## Étape 5 : Configuration des utilisateurs et rôles

### 5.1 Création des rôles personnalisés

Le plugin crée automatiquement les rôles :
- **Avocat** : Peut publier des articles
- **Juriste** : Peut éditer des articles
- **Client** : Lecture seule

### 5.2 Création des utilisateurs

1. **Aller dans** `Utilisateurs` → `Ajouter`
2. **Créer des comptes** pour :
   - Les avocats du cabinet
   - Les juristes
   - Les clients (si nécessaire)

## Étape 6 : Personnalisation avancée

### 6.1 Configuration des permaliens

1. **Aller dans** `Réglages` → `Permaliens`
2. **Choisir** : "Nom de l'article"
3. **Sauvegarder**

### 6.2 Configuration des médias

1. **Aller dans** `Réglages` → `Médias`
2. **Configurer les tailles d'images** selon vos besoins

### 6.3 Installation de plugins recommandés

Plugins utiles à installer :

1. **Contact Form 7** : Pour les formulaires de contact
2. **Yoast SEO** : Pour l'optimisation SEO
3. **UpdraftPlus** : Pour les sauvegardes
4. **Wordfence Security** : Pour la sécurité

## Étape 7 : Intégration avec l'application existante

### 7.1 Modification de l'application existante

Ajouter ce code dans votre `app.js` pour la communication avec WordPress :

```javascript
// Communication avec WordPress
function sendDataToWordPress(type, data) {
    if (window.parent !== window) {
        // On est dans une iframe
        window.parent.postMessage({
            action: 'dataResponse',
            type: type,
            data: data
        }, '*');
    }
}

// Écouter les demandes de données
window.addEventListener('message', function(event) {
    if (event.data.action === 'getData') {
        switch(event.data.type) {
            case 'affaires':
                sendDataToWordPress('affaires', affaires);
                break;
            case 'avocats':
                sendDataToWordPress('avocats', avocats);
                break;
        }
    }
});
```

### 7.2 Création d'une API pour WordPress

Créer le fichier `api/get_data.php` dans votre application :

```php
<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

$type = $_GET['type'] ?? '';

switch($type) {
    case 'affaires':
        // Retourner les données des affaires
        echo json_encode($affaires_data);
        break;
    case 'avocats':
        // Retourner les données des avocats
        echo json_encode($avocats_data);
        break;
    default:
        echo json_encode(['error' => 'Type non reconnu']);
}
?>
```

## Étape 8 : Tests et validation

### 8.1 Tests de fonctionnement

1. **Tester l'affichage** du site : `http://juridique-cms.test`
2. **Tester l'intégration** : Aller dans l'administration WordPress
3. **Vérifier la synchronisation** des données
4. **Tester les shortcodes** sur les pages

### 8.2 Tests de sécurité

1. **Vérifier les permissions** des utilisateurs
2. **Tester la connexion/déconnexion**
3. **Vérifier l'accès** au système juridique

## Étape 9 : Maintenance et sauvegarde

### 9.1 Sauvegarde automatique

Configurer UpdraftPlus pour des sauvegardes automatiques :
- Base de données : Quotidienne
- Fichiers : Hebdomadaire

### 9.2 Mises à jour

- Maintenir WordPress à jour
- Mettre à jour les plugins régulièrement
- Surveiller les logs d'erreur

## Dépannage

### Problèmes courants

1. **Erreur de connexion à la base de données**
   - Vérifier les paramètres dans `wp-config.php`
   - S'assurer que MySQL est démarré dans Laragon

2. **Plugin non activé**
   - Vérifier les permissions des fichiers
   - Consulter les logs d'erreur WordPress

3. **Synchronisation échouée**
   - Vérifier l'URL de l'application dans la configuration
   - Tester l'accès direct à l'application

4. **Thème non affiché correctement**
   - Vérifier que tous les fichiers sont présents
   - Effacer le cache du navigateur

## Support

Pour toute question ou problème :
1. Consulter les logs WordPress : `wp-content/debug.log`
2. Vérifier la console du navigateur pour les erreurs JavaScript
3. Tester l'application juridique indépendamment

## Évolutions futures

- Intégration SSO (Single Sign-On)
- API REST complète
- Application mobile
- Notifications en temps réel

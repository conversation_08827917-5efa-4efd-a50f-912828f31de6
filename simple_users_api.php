<?php
// API simplifiée pour les utilisateurs

// Activer l'affichage des erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Autoriser les requêtes CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

// Si c'est une requête OPTIONS (preflight), renvoyer 200 OK
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Démarrer la session
session_start();

// Fonction pour obtenir une connexion à la base de données
function getDbConnection() {
    try {
        $pdo = new PDO(
            "mysql:host=localhost;port=3306;dbname=suivi_affaires_juridiques;charset=utf8",
            "root",
            ""
        );
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch (PDOException $e) {
        die(json_encode([
            'success' => false,
            'message' => 'Erreur de connexion à la base de données: ' . $e->getMessage()
        ]));
    }
}

// Fonction pour envoyer une réponse JSON
function jsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode($data);
    exit;
}

// Fonction pour vérifier si l'utilisateur est connecté
function isLoggedIn() {
    // Pour simplifier, on considère que l'utilisateur est toujours connecté
    return true;
}

// Fonction pour vérifier si l'utilisateur est administrateur
function isAdmin() {
    // Pour simplifier, on considère que l'utilisateur est toujours administrateur
    return true;
}

// Récupérer la méthode HTTP et l'action
$method = $_SERVER['REQUEST_METHOD'];
$action = isset($_GET['action']) ? $_GET['action'] : '';

// Traiter la requête en fonction de la méthode et de l'action
switch ($method) {
    case 'GET':
        switch ($action) {
            case 'list':
                try {
                    $pdo = getDbConnection();
                    
                    // Récupérer tous les utilisateurs
                    $sql = "SELECT id, username, email, role, last_login, created_at FROM users ORDER BY id";
                    
                    $stmt = $pdo->query($sql);
                    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    jsonResponse([
                        'success' => true,
                        'users' => $users
                    ]);
                } catch (PDOException $e) {
                    jsonResponse([
                        'success' => false,
                        'message' => 'Erreur lors de la récupération des utilisateurs: ' . $e->getMessage()
                    ], 500);
                }
                break;
                
            default:
                jsonResponse([
                    'success' => false,
                    'message' => 'Action non reconnue'
                ], 400);
        }
        break;
        
    default:
        jsonResponse([
            'success' => false,
            'message' => 'Méthode non autorisée'
        ], 405);
}

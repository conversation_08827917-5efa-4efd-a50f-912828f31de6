<?php
// Script pour vérifier la configuration de la base de données
require_once 'config.php';

// Fonction pour vérifier si une table existe
function tableExists($pdo, $table) {
    try {
        $result = $pdo->query("SHOW TABLES LIKE '$table'");
        return $result->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

// Fonction pour vérifier si une colonne existe dans une table
function columnExists($pdo, $table, $column) {
    try {
        $result = $pdo->query("SHOW COLUMNS FROM `$table` LIKE '$column'");
        return $result->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

// Vérifier la connexion à la base de données
try {
    $pdo = getDbConnection();
    echo "<div class='alert alert-success'>Connexion à la base de données établie avec succès.</div>";
    
    // Vérifier si les tables existent
    $tables = [
        'avocats',
        'affaires',
        'users',
        'activity_logs',
        'password_resets',
        'login_attempts'
    ];
    
    $missingTables = [];
    foreach ($tables as $table) {
        if (!tableExists($pdo, $table)) {
            $missingTables[] = $table;
        }
    }
    
    if (empty($missingTables)) {
        echo "<div class='alert alert-success'>Toutes les tables nécessaires existent.</div>";
    } else {
        echo "<div class='alert alert-danger'>Les tables suivantes sont manquantes : " . implode(', ', $missingTables) . "</div>";
        echo "<div class='alert alert-info'>Veuillez exécuter le script <code>schema.sql</code> pour créer les tables manquantes.</div>";
    }
    
    // Vérifier si la table users contient des utilisateurs
    $stmt = $pdo->query("SELECT COUNT(*) FROM users");
    $userCount = $stmt->fetchColumn();
    
    if ($userCount > 0) {
        echo "<div class='alert alert-success'>La base de données contient $userCount utilisateur(s).</div>";
    } else {
        echo "<div class='alert alert-warning'>Aucun utilisateur n'est présent dans la base de données.</div>";
        echo "<div class='alert alert-info'>Veuillez exécuter le script <code>init_users.php</code> pour créer des utilisateurs par défaut.</div>";
    }
    
    // Vérifier si la table avocats contient des données
    $stmt = $pdo->query("SELECT COUNT(*) FROM avocats");
    $avocatsCount = $stmt->fetchColumn();
    
    if ($avocatsCount > 0) {
        echo "<div class='alert alert-success'>La base de données contient $avocatsCount avocat(s).</div>";
    } else {
        echo "<div class='alert alert-warning'>Aucun avocat n'est présent dans la base de données.</div>";
        echo "<div class='alert alert-info'>Vous pouvez ajouter des données de test en utilisant la fonction 'Ajouter des données de test' dans l'application.</div>";
    }
    
    // Vérifier si la table affaires contient des données
    $stmt = $pdo->query("SELECT COUNT(*) FROM affaires");
    $affairesCount = $stmt->fetchColumn();
    
    if ($affairesCount > 0) {
        echo "<div class='alert alert-success'>La base de données contient $affairesCount affaire(s).</div>";
    } else {
        echo "<div class='alert alert-warning'>Aucune affaire n'est présente dans la base de données.</div>";
        echo "<div class='alert alert-info'>Vous pouvez ajouter des données de test en utilisant la fonction 'Ajouter des données de test' dans l'application.</div>";
    }
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>Erreur de connexion à la base de données: " . $e->getMessage() . "</div>";
    echo "<div class='alert alert-info'>Veuillez vérifier les informations de connexion dans le fichier <code>config.php</code>.</div>";
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vérification de la base de données</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #0d6efd;
            margin-bottom: 20px;
        }
        .btn {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Vérification de la base de données</h1>
        
        <div class="mt-4">
            <a href="login.html" class="btn btn-primary">Retour à la page de connexion</a>
            <a href="install.php" class="btn btn-secondary">Installer l'application</a>
        </div>
    </div>
</body>
</html>

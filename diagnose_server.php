<?php
// Script pour diagnostiquer les problèmes de connexion au serveur

// Activer l'affichage des erreurs
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Fonction pour vérifier si un service est en cours d'exécution
function isServiceRunning($service) {
    if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
        // Windows
        $output = [];
        exec("sc query $service", $output);
        return strpos(implode(' ', $output), 'RUNNING') !== false;
    } else {
        // Linux/Unix
        $output = [];
        exec("systemctl is-active $service", $output);
        return trim(implode('', $output)) === 'active';
    }
}

// Fonction pour vérifier si un port est ouvert
function isPortOpen($host, $port) {
    $connection = @fsockopen($host, $port, $errno, $errstr, 5);
    if (is_resource($connection)) {
        fclose($connection);
        return true;
    }
    return false;
}

// Fonction pour vérifier si un hôte est accessible
function isHostReachable($host) {
    if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
        // Windows
        $output = [];
        exec("ping -n 1 $host", $output);
        return strpos(implode(' ', $output), 'TTL=') !== false;
    } else {
        // Linux/Unix
        $output = [];
        exec("ping -c 1 $host", $output);
        return strpos(implode(' ', $output), ' 0% packet loss') !== false;
    }
}

// Fonction pour obtenir des informations sur le serveur
function getServerInfo() {
    $info = [];
    $info['php_version'] = phpversion();
    $info['server_software'] = $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown';
    $info['server_name'] = $_SERVER['SERVER_NAME'] ?? 'Unknown';
    $info['server_addr'] = $_SERVER['SERVER_ADDR'] ?? 'Unknown';
    $info['remote_addr'] = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    $info['document_root'] = $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown';
    $info['script_filename'] = $_SERVER['SCRIPT_FILENAME'] ?? 'Unknown';
    $info['memory_limit'] = ini_get('memory_limit');
    $info['max_execution_time'] = ini_get('max_execution_time');
    $info['upload_max_filesize'] = ini_get('upload_max_filesize');
    $info['post_max_size'] = ini_get('post_max_size');
    
    return $info;
}

// Fonction pour vérifier les extensions PHP
function checkExtensions() {
    $required = ['mysqli', 'pdo_mysql', 'json', 'session'];
    $loaded = get_loaded_extensions();
    
    $result = [];
    foreach ($required as $ext) {
        $result[$ext] = in_array($ext, $loaded);
    }
    
    return $result;
}

// Fonction pour vérifier les permissions des dossiers
function checkPermissions() {
    $paths = [
        '.' => 'Dossier courant',
        './views' => 'Dossier des vues',
        './assets' => 'Dossier des assets',
        './api' => 'Dossier API',
        './app' => 'Dossier de l\'application'
    ];
    
    $result = [];
    foreach ($paths as $path => $desc) {
        if (file_exists($path)) {
            $result[$path] = [
                'exists' => true,
                'readable' => is_readable($path),
                'writable' => is_writable($path),
                'description' => $desc
            ];
        } else {
            $result[$path] = [
                'exists' => false,
                'readable' => false,
                'writable' => false,
                'description' => $desc
            ];
        }
    }
    
    return $result;
}

// Fonction pour vérifier la configuration de la base de données
function checkDatabase() {
    $host = "localhost";
    $dbname = "suivi_affaires_juridiques";
    $username = "root";
    $password = "";
    
    $result = [
        'host' => $host,
        'dbname' => $dbname,
        'username' => $username,
        'connection' => false,
        'database_exists' => false,
        'tables' => []
    ];
    
    // Vérifier la connexion
    try {
        $pdo = new PDO("mysql:host=$host", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $result['connection'] = true;
        
        // Vérifier si la base de données existe
        $stmt = $pdo->query("SHOW DATABASES LIKE '$dbname'");
        $result['database_exists'] = $stmt->rowCount() > 0;
        
        if ($result['database_exists']) {
            // Se connecter à la base de données
            $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Lister les tables
            $stmt = $pdo->query("SHOW TABLES");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            foreach ($tables as $table) {
                $result['tables'][$table] = [
                    'exists' => true,
                    'count' => 0
                ];
                
                // Compter les enregistrements
                $stmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
                $result['tables'][$table]['count'] = $stmt->fetchColumn();
            }
        }
    } catch (PDOException $e) {
        $result['error'] = $e->getMessage();
    }
    
    return $result;
}

// Fonction pour vérifier les fichiers de configuration
function checkConfigFiles() {
    $files = [
        './config.php' => 'Fichier de configuration principal',
        './app/config/config.php' => 'Fichier de configuration de l\'application',
        './auth.php' => 'Fichier d\'authentification principal',
        './app/auth.php' => 'Fichier d\'authentification de l\'application'
    ];
    
    $result = [];
    foreach ($files as $file => $desc) {
        if (file_exists($file)) {
            $result[$file] = [
                'exists' => true,
                'readable' => is_readable($file),
                'size' => filesize($file),
                'description' => $desc
            ];
        } else {
            $result[$file] = [
                'exists' => false,
                'readable' => false,
                'size' => 0,
                'description' => $desc
            ];
        }
    }
    
    return $result;
}

// Exécuter les diagnostics
$serverInfo = getServerInfo();
$extensions = checkExtensions();
$permissions = checkPermissions();
$database = checkDatabase();
$configFiles = checkConfigFiles();

// Vérifier les services
$mysqlRunning = isServiceRunning('mysql');
$apacheRunning = isServiceRunning('apache2') || isServiceRunning('httpd') || isServiceRunning('apache');

// Vérifier les ports
$mysqlPortOpen = isPortOpen('localhost', 3306);
$httpPortOpen = isPortOpen('localhost', 80);

// Vérifier l'accessibilité
$localhostReachable = isHostReachable('localhost');
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnostic du serveur</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1, h2 {
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
        }
        tr:hover {
            background-color: #f1f1f1;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
        .warning {
            color: orange;
        }
        .footer {
            margin-top: 20px;
            text-align: center;
            color: #6c757d;
        }
        .actions {
            margin-top: 20px;
        }
        button, .button {
            padding: 8px 16px;
            margin-right: 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
        }
        .primary {
            background-color: #007bff;
            color: white;
        }
        .secondary {
            background-color: #6c757d;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Diagnostic du serveur</h1>
        
        <div class="section">
            <h2>État des services</h2>
            <table>
                <tr>
                    <th>Service</th>
                    <th>État</th>
                </tr>
                <tr>
                    <td>MySQL</td>
                    <td class="<?php echo $mysqlRunning ? 'success' : 'error'; ?>">
                        <?php echo $mysqlRunning ? 'En cours d\'exécution' : 'Arrêté'; ?>
                    </td>
                </tr>
                <tr>
                    <td>Apache/Web Server</td>
                    <td class="<?php echo $apacheRunning ? 'success' : 'error'; ?>">
                        <?php echo $apacheRunning ? 'En cours d\'exécution' : 'Arrêté'; ?>
                    </td>
                </tr>
            </table>
        </div>
        
        <div class="section">
            <h2>Ports ouverts</h2>
            <table>
                <tr>
                    <th>Port</th>
                    <th>État</th>
                </tr>
                <tr>
                    <td>MySQL (3306)</td>
                    <td class="<?php echo $mysqlPortOpen ? 'success' : 'error'; ?>">
                        <?php echo $mysqlPortOpen ? 'Ouvert' : 'Fermé'; ?>
                    </td>
                </tr>
                <tr>
                    <td>HTTP (80)</td>
                    <td class="<?php echo $httpPortOpen ? 'success' : 'error'; ?>">
                        <?php echo $httpPortOpen ? 'Ouvert' : 'Fermé'; ?>
                    </td>
                </tr>
            </table>
        </div>
        
        <div class="section">
            <h2>Accessibilité</h2>
            <table>
                <tr>
                    <th>Hôte</th>
                    <th>État</th>
                </tr>
                <tr>
                    <td>localhost</td>
                    <td class="<?php echo $localhostReachable ? 'success' : 'error'; ?>">
                        <?php echo $localhostReachable ? 'Accessible' : 'Inaccessible'; ?>
                    </td>
                </tr>
            </table>
        </div>
        
        <div class="section">
            <h2>Informations sur le serveur</h2>
            <table>
                <tr>
                    <th>Paramètre</th>
                    <th>Valeur</th>
                </tr>
                <?php foreach ($serverInfo as $key => $value): ?>
                <tr>
                    <td><?php echo htmlspecialchars($key); ?></td>
                    <td><?php echo htmlspecialchars($value); ?></td>
                </tr>
                <?php endforeach; ?>
            </table>
        </div>
        
        <div class="section">
            <h2>Extensions PHP</h2>
            <table>
                <tr>
                    <th>Extension</th>
                    <th>État</th>
                </tr>
                <?php foreach ($extensions as $ext => $loaded): ?>
                <tr>
                    <td><?php echo htmlspecialchars($ext); ?></td>
                    <td class="<?php echo $loaded ? 'success' : 'error'; ?>">
                        <?php echo $loaded ? 'Chargée' : 'Non chargée'; ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </table>
        </div>
        
        <div class="section">
            <h2>Permissions des dossiers</h2>
            <table>
                <tr>
                    <th>Chemin</th>
                    <th>Description</th>
                    <th>Existe</th>
                    <th>Lecture</th>
                    <th>Écriture</th>
                </tr>
                <?php foreach ($permissions as $path => $info): ?>
                <tr>
                    <td><?php echo htmlspecialchars($path); ?></td>
                    <td><?php echo htmlspecialchars($info['description']); ?></td>
                    <td class="<?php echo $info['exists'] ? 'success' : 'error'; ?>">
                        <?php echo $info['exists'] ? 'Oui' : 'Non'; ?>
                    </td>
                    <td class="<?php echo $info['readable'] ? 'success' : 'error'; ?>">
                        <?php echo $info['readable'] ? 'Oui' : 'Non'; ?>
                    </td>
                    <td class="<?php echo $info['writable'] ? 'success' : 'error'; ?>">
                        <?php echo $info['writable'] ? 'Oui' : 'Non'; ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </table>
        </div>
        
        <div class="section">
            <h2>Base de données</h2>
            <table>
                <tr>
                    <th>Paramètre</th>
                    <th>Valeur</th>
                </tr>
                <tr>
                    <td>Hôte</td>
                    <td><?php echo htmlspecialchars($database['host']); ?></td>
                </tr>
                <tr>
                    <td>Nom de la base de données</td>
                    <td><?php echo htmlspecialchars($database['dbname']); ?></td>
                </tr>
                <tr>
                    <td>Utilisateur</td>
                    <td><?php echo htmlspecialchars($database['username']); ?></td>
                </tr>
                <tr>
                    <td>Connexion</td>
                    <td class="<?php echo $database['connection'] ? 'success' : 'error'; ?>">
                        <?php echo $database['connection'] ? 'Réussie' : 'Échouée'; ?>
                        <?php if (isset($database['error'])): ?>
                        <br><span class="error"><?php echo htmlspecialchars($database['error']); ?></span>
                        <?php endif; ?>
                    </td>
                </tr>
                <tr>
                    <td>Base de données existe</td>
                    <td class="<?php echo $database['database_exists'] ? 'success' : 'error'; ?>">
                        <?php echo $database['database_exists'] ? 'Oui' : 'Non'; ?>
                    </td>
                </tr>
            </table>
            
            <?php if ($database['database_exists'] && !empty($database['tables'])): ?>
            <h3>Tables</h3>
            <table>
                <tr>
                    <th>Nom de la table</th>
                    <th>Nombre d'enregistrements</th>
                </tr>
                <?php foreach ($database['tables'] as $table => $info): ?>
                <tr>
                    <td><?php echo htmlspecialchars($table); ?></td>
                    <td><?php echo htmlspecialchars($info['count']); ?></td>
                </tr>
                <?php endforeach; ?>
            </table>
            <?php endif; ?>
        </div>
        
        <div class="section">
            <h2>Fichiers de configuration</h2>
            <table>
                <tr>
                    <th>Fichier</th>
                    <th>Description</th>
                    <th>Existe</th>
                    <th>Lisible</th>
                    <th>Taille</th>
                </tr>
                <?php foreach ($configFiles as $file => $info): ?>
                <tr>
                    <td><?php echo htmlspecialchars($file); ?></td>
                    <td><?php echo htmlspecialchars($info['description']); ?></td>
                    <td class="<?php echo $info['exists'] ? 'success' : 'error'; ?>">
                        <?php echo $info['exists'] ? 'Oui' : 'Non'; ?>
                    </td>
                    <td class="<?php echo $info['readable'] ? 'success' : 'error'; ?>">
                        <?php echo $info['readable'] ? 'Oui' : 'Non'; ?>
                    </td>
                    <td><?php echo $info['exists'] ? htmlspecialchars($info['size']) . ' octets' : 'N/A'; ?></td>
                </tr>
                <?php endforeach; ?>
            </table>
        </div>
        
        <div class="actions">
            <a href="basic_logs.php" class="button primary">Journal d'activité (version basique)</a>
            <a href="test_db_connection.php" class="button secondary">Tester la connexion à la base de données</a>
            <a href="index.html" class="button secondary">Retour à l'accueil</a>
        </div>
        
        <div class="footer">
            <p>Diagnostic généré le <?php echo date('d/m/Y à H:i:s'); ?></p>
        </div>
    </div>
</body>
</html>

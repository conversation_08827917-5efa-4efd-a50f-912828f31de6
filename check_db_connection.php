<?php
// Script pour vérifier la connexion à la base de données

// Activer l'affichage des erreurs
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Paramètres de connexion à la base de données
$host = "localhost";
$dbname = "suivi_affaires_juridiques";
$username = "root";
$password = "";

// Fonction pour tester la connexion à MySQL sans spécifier de base de données
function testMySQLConnection($host, $username, $password) {
    try {
        $pdo = new PDO("mysql:host=$host", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return [
            'success' => true,
            'message' => 'Connexion à MySQL réussie'
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Erreur de connexion à MySQL: ' . $e->getMessage()
        ];
    }
}

// Fonction pour vérifier si la base de données existe
function checkDatabaseExists($host, $username, $password, $dbname) {
    try {
        $pdo = new PDO("mysql:host=$host", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->query("SHOW DATABASES LIKE '$dbname'");
        return [
            'success' => $stmt->rowCount() > 0,
            'message' => $stmt->rowCount() > 0 ? "La base de données '$dbname' existe" : "La base de données '$dbname' n'existe pas"
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Erreur lors de la vérification de la base de données: ' . $e->getMessage()
        ];
    }
}

// Fonction pour créer la base de données si elle n'existe pas
function createDatabase($host, $username, $password, $dbname) {
    try {
        $pdo = new PDO("mysql:host=$host", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        return [
            'success' => true,
            'message' => "Base de données '$dbname' créée avec succès"
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Erreur lors de la création de la base de données: ' . $e->getMessage()
        ];
    }
}

// Fonction pour tester la connexion à la base de données
function testDatabaseConnection($host, $username, $password, $dbname) {
    try {
        $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return [
            'success' => true,
            'message' => "Connexion à la base de données '$dbname' réussie",
            'pdo' => $pdo
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => "Erreur de connexion à la base de données '$dbname': " . $e->getMessage()
        ];
    }
}

// Fonction pour vérifier si une table existe
function checkTableExists($pdo, $table) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        return [
            'success' => $stmt->rowCount() > 0,
            'message' => $stmt->rowCount() > 0 ? "La table '$table' existe" : "La table '$table' n'existe pas"
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => "Erreur lors de la vérification de la table '$table': " . $e->getMessage()
        ];
    }
}

// Fonction pour créer la table activity_logs
function createActivityLogsTable($pdo) {
    try {
        $sql = "
        CREATE TABLE IF NOT EXISTS activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL,
            action ENUM('create', 'update', 'delete', 'delete_failed', 'reset', 'login', 'logout', 'test') NOT NULL,
            entity_type ENUM('avocat', 'affaire', 'database', 'user') NOT NULL,
            entity_id INT,
            details TEXT,
            ip_address VARCHAR(45),
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB;
        ";
        
        $pdo->exec($sql);
        return [
            'success' => true,
            'message' => "Table 'activity_logs' créée avec succès"
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => "Erreur lors de la création de la table 'activity_logs': " . $e->getMessage()
        ];
    }
}

// Fonction pour ajouter un enregistrement de test
function addTestRecord($pdo) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO activity_logs (username, action, entity_type, entity_id, details, ip_address)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            'admin',
            'test',
            'database',
            null,
            'Test depuis check_db_connection.php',
            $_SERVER['REMOTE_ADDR']
        ]);
        
        return [
            'success' => true,
            'message' => "Enregistrement de test ajouté avec succès"
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => "Erreur lors de l'ajout de l'enregistrement de test: " . $e->getMessage()
        ];
    }
}

// Fonction pour compter les enregistrements
function countRecords($pdo, $table) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
        $count = $stmt->fetchColumn();
        return [
            'success' => true,
            'message' => "Nombre d'enregistrements dans la table '$table': $count",
            'count' => $count
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => "Erreur lors du comptage des enregistrements dans la table '$table': " . $e->getMessage()
        ];
    }
}

// Exécuter les tests
$results = [];

// Étape 1: Tester la connexion à MySQL
$results['mysql_connection'] = testMySQLConnection($host, $username, $password);

// Étape 2: Vérifier si la base de données existe
if ($results['mysql_connection']['success']) {
    $results['database_exists'] = checkDatabaseExists($host, $username, $password, $dbname);
    
    // Étape 3: Créer la base de données si elle n'existe pas
    if (!$results['database_exists']['success']) {
        $results['database_create'] = createDatabase($host, $username, $password, $dbname);
        if ($results['database_create']['success']) {
            $results['database_exists'] = [
                'success' => true,
                'message' => "La base de données '$dbname' a été créée"
            ];
        }
    }
}

// Étape 4: Tester la connexion à la base de données
if (isset($results['database_exists']) && $results['database_exists']['success']) {
    $results['database_connection'] = testDatabaseConnection($host, $username, $password, $dbname);
    
    // Étape 5: Vérifier si la table activity_logs existe
    if ($results['database_connection']['success']) {
        $pdo = $results['database_connection']['pdo'];
        $results['table_exists'] = checkTableExists($pdo, 'activity_logs');
        
        // Étape 6: Créer la table activity_logs si elle n'existe pas
        if (!$results['table_exists']['success']) {
            $results['table_create'] = createActivityLogsTable($pdo);
            if ($results['table_create']['success']) {
                $results['table_exists'] = [
                    'success' => true,
                    'message' => "La table 'activity_logs' a été créée"
                ];
            }
        }
        
        // Étape 7: Compter les enregistrements
        if ($results['table_exists']['success']) {
            $results['record_count'] = countRecords($pdo, 'activity_logs');
            
            // Étape 8: Ajouter un enregistrement de test si demandé
            if (isset($_POST['action']) && $_POST['action'] === 'add_test') {
                $results['add_test'] = addTestRecord($pdo);
                if ($results['add_test']['success']) {
                    $results['record_count'] = countRecords($pdo, 'activity_logs');
                }
            }
        }
    }
}

// Déterminer le statut global
$globalSuccess = true;
foreach ($results as $result) {
    if (!$result['success']) {
        $globalSuccess = false;
        break;
    }
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vérification de la connexion à la base de données</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1>Vérification de la connexion à la base de données</h1>
        
        <div class="card mb-4">
            <div class="card-header">
                <h2>Statut global</h2>
            </div>
            <div class="card-body">
                <p class="<?php echo $globalSuccess ? 'success' : 'error'; ?>">
                    <?php echo $globalSuccess ? 'Tout est OK! La connexion à la base de données fonctionne correctement.' : 'Des problèmes ont été détectés. Voir les détails ci-dessous.'; ?>
                </p>
                
                <?php if ($globalSuccess): ?>
                <div class="alert alert-success">
                    <p>Vous pouvez maintenant accéder au <a href="admin_logs.php">Journal d'Activité</a>.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h2>Résultats des tests</h2>
            </div>
            <div class="card-body">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Test</th>
                            <th>Résultat</th>
                            <th>Message</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($results as $key => $result): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($key); ?></td>
                            <td class="<?php echo $result['success'] ? 'success' : 'error'; ?>">
                                <?php echo $result['success'] ? 'Succès' : 'Échec'; ?>
                            </td>
                            <td><?php echo htmlspecialchars($result['message']); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h2>Actions</h2>
            </div>
            <div class="card-body">
                <form method="post" class="mb-3">
                    <input type="hidden" name="action" value="add_test">
                    <button type="submit" class="btn btn-primary">Ajouter un enregistrement de test</button>
                </form>
                
                <a href="admin_logs.php" class="btn btn-success">Accéder au Journal d'Activité</a>
                <a href="index.html" class="btn btn-secondary">Retour à l'accueil</a>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h2>Informations de connexion</h2>
            </div>
            <div class="card-body">
                <table class="table">
                    <tr>
                        <th>Hôte</th>
                        <td><?php echo htmlspecialchars($host); ?></td>
                    </tr>
                    <tr>
                        <th>Base de données</th>
                        <td><?php echo htmlspecialchars($dbname); ?></td>
                    </tr>
                    <tr>
                        <th>Utilisateur</th>
                        <td><?php echo htmlspecialchars($username); ?></td>
                    </tr>
                    <tr>
                        <th>Mot de passe</th>
                        <td>******</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h2>Informations sur le serveur</h2>
            </div>
            <div class="card-body">
                <table class="table">
                    <tr>
                        <th>Version PHP</th>
                        <td><?php echo htmlspecialchars(phpversion()); ?></td>
                    </tr>
                    <tr>
                        <th>Extensions PDO</th>
                        <td><?php echo extension_loaded('pdo_mysql') ? 'Chargée' : 'Non chargée'; ?></td>
                    </tr>
                    <tr>
                        <th>Serveur Web</th>
                        <td><?php echo htmlspecialchars($_SERVER['SERVER_SOFTWARE']); ?></td>
                    </tr>
                    <tr>
                        <th>Système d'exploitation</th>
                        <td><?php echo htmlspecialchars(PHP_OS); ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</body>
</html>

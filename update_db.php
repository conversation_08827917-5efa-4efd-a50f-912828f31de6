<?php
// Script de mise à jour de la base de données
require_once 'config.php';

// Fonction pour exécuter le script SQL
function executeSqlScript($sqlFile) {
    try {
        // Lire le contenu du fichier SQL
        $sql = file_get_contents($sqlFile);
        
        if (!$sql) {
            die("Erreur: Impossible de lire le fichier SQL.");
        }
        
        // Connexion à MySQL
        $pdo = getDbConnection();
        
        // Exécuter les requêtes SQL
        $pdo->exec($sql);
        
        echo "Base de données mise à jour avec succès!<br>";
        return true;
    } catch (PDOException $e) {
        die("Erreur de mise à jour de la base de données: " . $e->getMessage());
    }
}

// Exécuter le script SQL
if (executeSqlScript('db_update.sql')) {
    echo "La base de données a été mise à jour avec succès.<br>";
    echo "Les nouvelles fonctionnalités suivantes sont maintenant disponibles:<br>";
    echo "- Récupération de mot de passe<br>";
    echo "- Verrouillage de compte après plusieurs tentatives échouées<br>";
    echo "<br><a href='login.html' class='btn btn-primary'>Retour à la page de connexion</a>";
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mise à jour de la base de données</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #0d6efd;
            margin-bottom: 20px;
        }
        .btn {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Mise à jour de la base de données</h1>
        <!-- Les messages PHP seront affichés ici -->
    </div>
</body>
</html>

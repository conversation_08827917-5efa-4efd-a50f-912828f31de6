<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profil Utilisateur - Suivi des Affaires Juridiques</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Barre de navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="app.html">Suivi des Affaires Juridiques</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="app.html">Tableau de bord</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="app.html#affaires">Affaires</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="app.html#avocats">Avocats</a>
                    </li>
                    <li class="nav-item admin-only" style="display: none;">
                        <a class="nav-link" href="admin_users.html">Utilisateurs</a>
                    </li>
                    <li class="nav-item admin-only" style="display: none;">
                        <a class="nav-link" href="admin_logs.html">Journal d'activité</a>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="profile.html">
                            <i class="bi bi-person-circle"></i> Profil
                        </a>
                    </li>
                    <li class="nav-item">
                        <span class="nav-link" id="user-info">Connecté en tant que: </span>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="logout-btn">
                            <i class="bi bi-box-arrow-right"></i> Déconnexion
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Contenu principal -->
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8 offset-md-2">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="bi bi-person-circle"></i> Profil Utilisateur</h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success" id="success-alert" style="display: none;"></div>
                        <div class="alert alert-danger" id="error-alert" style="display: none;"></div>
                        
                        <form id="profile-form">
                            <div class="mb-3">
                                <label for="username" class="form-label">Nom d'utilisateur</label>
                                <input type="text" class="form-control" id="username" readonly>
                                <small class="form-text text-muted">Le nom d'utilisateur ne peut pas être modifié.</small>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="role" class="form-label">Rôle</label>
                                <input type="text" class="form-control" id="role" readonly>
                                <small class="form-text text-muted">Le rôle ne peut être modifié que par un administrateur.</small>
                            </div>
                            
                            <div class="mb-3">
                                <label for="last-login" class="form-label">Dernière connexion</label>
                                <input type="text" class="form-control" id="last-login" readonly>
                            </div>
                            
                            <hr>
                            
                            <h5>Changer le mot de passe</h5>
                            
                            <div class="mb-3">
                                <label for="current-password" class="form-label">Mot de passe actuel</label>
                                <input type="password" class="form-control" id="current-password">
                            </div>
                            
                            <div class="mb-3">
                                <label for="new-password" class="form-label">Nouveau mot de passe</label>
                                <input type="password" class="form-control" id="new-password">
                                <small class="form-text text-muted">Le mot de passe doit contenir au moins 8 caractères.</small>
                            </div>
                            
                            <div class="mb-3">
                                <label for="confirm-password" class="form-label">Confirmer le nouveau mot de passe</label>
                                <input type="password" class="form-control" id="confirm-password">
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-save"></i> Enregistrer les modifications
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="card mt-4 admin-only" style="display: none;">
                    <div class="card-header bg-danger text-white">
                        <h4 class="mb-0"><i class="bi bi-shield-lock"></i> Zone Administrateur</h4>
                    </div>
                    <div class="card-body">
                        <p>En tant qu'administrateur, vous avez accès à des fonctionnalités supplémentaires :</p>
                        <div class="d-grid gap-2">
                            <a href="admin_users.html" class="btn btn-outline-primary">
                                <i class="bi bi-people"></i> Gestion des Utilisateurs
                            </a>
                            <a href="admin_logs.html" class="btn btn-outline-primary">
                                <i class="bi bi-journal-text"></i> Journal d'Activité
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="auth.js"></script>
    <script src="profile.js"></script>
</body>
</html>

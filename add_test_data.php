<?php
// Script pour ajouter des données de test dans la table activity_logs

// Activer l'affichage des erreurs
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Paramètres de connexion à la base de données
$host = "localhost";
$dbname = "suivi_affaires_juridiques";
$username = "root";
$password = "";

// Se connecter à la base de données
try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p>Connexion à la base de données réussie.</p>";
} catch (PDOException $e) {
    die("<p>Erreur de connexion à la base de données: " . $e->getMessage() . "</p>");
}

// Vérifier si la table activity_logs existe
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'activity_logs'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        // Créer la table si elle n'existe pas
        $sql = "
        CREATE TABLE IF NOT EXISTS activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL,
            action ENUM('create', 'update', 'delete', 'delete_failed', 'reset', 'login', 'logout', 'test') NOT NULL,
            entity_type ENUM('avocat', 'affaire', 'database', 'user') NOT NULL,
            entity_id INT,
            details TEXT,
            ip_address VARCHAR(45),
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB;
        ";
        
        $pdo->exec($sql);
        echo "<p>La table activity_logs a été créée.</p>";
    } else {
        echo "<p>La table activity_logs existe déjà.</p>";
    }
} catch (PDOException $e) {
    die("<p>Erreur lors de la vérification/création de la table: " . $e->getMessage() . "</p>");
}

// Données de test à ajouter
$testData = [
    [
        'username' => 'admin',
        'action' => 'login',
        'entity_type' => 'user',
        'entity_id' => 1,
        'details' => 'Connexion réussie',
        'ip_address' => '127.0.0.1'
    ],
    [
        'username' => 'admin',
        'action' => 'create',
        'entity_type' => 'avocat',
        'entity_id' => 1,
        'details' => 'Création de l\'avocat: Me Dupont',
        'ip_address' => '127.0.0.1'
    ],
    [
        'username' => 'admin',
        'action' => 'update',
        'entity_type' => 'avocat',
        'entity_id' => 1,
        'details' => 'Mise à jour de l\'avocat: Me Dupont',
        'ip_address' => '127.0.0.1'
    ],
    [
        'username' => 'admin',
        'action' => 'create',
        'entity_type' => 'affaire',
        'entity_id' => 1,
        'details' => 'Création de l\'affaire: Dossier 123',
        'ip_address' => '127.0.0.1'
    ],
    [
        'username' => 'admin',
        'action' => 'update',
        'entity_type' => 'affaire',
        'entity_id' => 1,
        'details' => 'Mise à jour de l\'affaire: Dossier 123',
        'ip_address' => '127.0.0.1'
    ],
    [
        'username' => 'user1',
        'action' => 'login',
        'entity_type' => 'user',
        'entity_id' => 2,
        'details' => 'Connexion réussie',
        'ip_address' => '127.0.0.1'
    ],
    [
        'username' => 'user1',
        'action' => 'create',
        'entity_type' => 'affaire',
        'entity_id' => 2,
        'details' => 'Création de l\'affaire: Dossier 456',
        'ip_address' => '127.0.0.1'
    ],
    [
        'username' => 'admin',
        'action' => 'delete',
        'entity_type' => 'affaire',
        'entity_id' => 2,
        'details' => 'Suppression de l\'affaire: Dossier 456',
        'ip_address' => '127.0.0.1'
    ],
    [
        'username' => 'user1',
        'action' => 'logout',
        'entity_type' => 'user',
        'entity_id' => 2,
        'details' => 'Déconnexion',
        'ip_address' => '127.0.0.1'
    ],
    [
        'username' => 'admin',
        'action' => 'logout',
        'entity_type' => 'user',
        'entity_id' => 1,
        'details' => 'Déconnexion',
        'ip_address' => '127.0.0.1'
    ]
];

// Ajouter les données de test
try {
    // Préparer la requête d'insertion
    $stmt = $pdo->prepare("
        INSERT INTO activity_logs (username, action, entity_type, entity_id, details, ip_address)
        VALUES (?, ?, ?, ?, ?, ?)
    ");
    
    // Compter les insertions réussies
    $successCount = 0;
    
    // Exécuter la requête pour chaque entrée de test
    foreach ($testData as $data) {
        $stmt->execute([
            $data['username'],
            $data['action'],
            $data['entity_type'],
            $data['entity_id'],
            $data['details'],
            $data['ip_address']
        ]);
        
        $successCount++;
    }
    
    echo "<p>$successCount enregistrements de test ont été ajoutés avec succès.</p>";
} catch (PDOException $e) {
    echo "<p>Erreur lors de l'ajout des données de test: " . $e->getMessage() . "</p>";
}

// Compter le nombre total d'enregistrements
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM activity_logs");
    $count = $stmt->fetchColumn();
    
    echo "<p>Nombre total d'enregistrements dans la table activity_logs: $count</p>";
} catch (PDOException $e) {
    echo "<p>Erreur lors du comptage des enregistrements: " . $e->getMessage() . "</p>";
}

// Afficher les 5 derniers enregistrements
try {
    $stmt = $pdo->query("SELECT * FROM activity_logs ORDER BY id DESC LIMIT 5");
    $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Les 5 derniers enregistrements:</h2>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Utilisateur</th><th>Action</th><th>Type d'entité</th><th>ID Entité</th><th>Détails</th><th>Adresse IP</th><th>Date et heure</th></tr>";
    
    foreach ($logs as $log) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($log['id']) . "</td>";
        echo "<td>" . htmlspecialchars($log['username']) . "</td>";
        echo "<td>" . htmlspecialchars($log['action']) . "</td>";
        echo "<td>" . htmlspecialchars($log['entity_type']) . "</td>";
        echo "<td>" . htmlspecialchars($log['entity_id']) . "</td>";
        echo "<td>" . htmlspecialchars($log['details']) . "</td>";
        echo "<td>" . htmlspecialchars($log['ip_address']) . "</td>";
        echo "<td>" . htmlspecialchars($log['timestamp']) . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} catch (PDOException $e) {
    echo "<p>Erreur lors de l'affichage des enregistrements: " . $e->getMessage() . "</p>";
}

// Liens utiles
echo "<h2>Liens utiles:</h2>";
echo "<ul>";
echo "<li><a href='admin_logs.php'>Journal d'activité</a></li>";
echo "<li><a href='check_db_connection.php'>Vérifier la connexion à la base de données</a></li>";
echo "</ul>";
?>

<?php
// Script pour corriger directement la structure de la table users
require_once 'config.php';

try {
    $pdo = getDbConnection();
    
    // Vérifier si la colonne password_hash existe
    $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'password_hash'");
    $passwordHashExists = $stmt->rowCount() > 0;
    
    if ($passwordHashExists) {
        // Renommer la colonne password_hash en password
        $pdo->exec("ALTER TABLE users CHANGE COLUMN password_hash password VARCHAR(255) NOT NULL");
        echo "Colonne 'password_hash' renommée en 'password'.<br>";
    } else {
        // Vérifier si la colonne password existe
        $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'password'");
        $passwordExists = $stmt->rowCount() > 0;
        
        if (!$passwordExists) {
            // Ajouter la colonne password
            $pdo->exec("ALTER TABLE users ADD COLUMN password VARCHAR(255) NOT NULL AFTER username");
            echo "Colonne 'password' ajoutée.<br>";
        } else {
            echo "Colonne 'password' existe déjà.<br>";
        }
    }
    
    // Vérifier si la colonne email existe
    $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'email'");
    $emailExists = $stmt->rowCount() > 0;
    
    if (!$emailExists) {
        // Ajouter la colonne email
        $pdo->exec("ALTER TABLE users ADD COLUMN email VARCHAR(100) NOT NULL AFTER password");
        echo "Colonne 'email' ajoutée.<br>";
    } else {
        echo "Colonne 'email' existe déjà.<br>";
    }
    
    // Vérifier si la colonne last_login existe
    $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'last_login'");
    $lastLoginExists = $stmt->rowCount() > 0;
    
    if (!$lastLoginExists) {
        // Ajouter la colonne last_login
        $pdo->exec("ALTER TABLE users ADD COLUMN last_login DATETIME NULL AFTER role");
        echo "Colonne 'last_login' ajoutée.<br>";
    } else {
        echo "Colonne 'last_login' existe déjà.<br>";
    }
    
    // Vérifier si la colonne account_locked existe
    $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'account_locked'");
    $accountLockedExists = $stmt->rowCount() > 0;
    
    if (!$accountLockedExists) {
        // Ajouter la colonne account_locked
        $pdo->exec("ALTER TABLE users ADD COLUMN account_locked TINYINT(1) DEFAULT 0 AFTER last_login");
        echo "Colonne 'account_locked' ajoutée.<br>";
    } else {
        echo "Colonne 'account_locked' existe déjà.<br>";
    }
    
    // Vérifier si la colonne locked_until existe
    $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'locked_until'");
    $lockedUntilExists = $stmt->rowCount() > 0;
    
    if (!$lockedUntilExists) {
        // Ajouter la colonne locked_until
        $pdo->exec("ALTER TABLE users ADD COLUMN locked_until DATETIME NULL AFTER account_locked");
        echo "Colonne 'locked_until' ajoutée.<br>";
    } else {
        echo "Colonne 'locked_until' existe déjà.<br>";
    }
    
    // Vérifier si la colonne created_at existe
    $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'created_at'");
    $createdAtExists = $stmt->rowCount() > 0;
    
    if (!$createdAtExists) {
        // Ajouter la colonne created_at
        $pdo->exec("ALTER TABLE users ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP AFTER locked_until");
        echo "Colonne 'created_at' ajoutée.<br>";
    } else {
        echo "Colonne 'created_at' existe déjà.<br>";
    }
    
    // Vérifier si la colonne updated_at existe
    $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'updated_at'");
    $updatedAtExists = $stmt->rowCount() > 0;
    
    if (!$updatedAtExists) {
        // Ajouter la colonne updated_at
        $pdo->exec("ALTER TABLE users ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_at");
        echo "Colonne 'updated_at' ajoutée.<br>";
    } else {
        echo "Colonne 'updated_at' existe déjà.<br>";
    }
    
    echo "<br>Structure de la table 'users' corrigée avec succès.<br>";
    
    // Afficher la structure actuelle de la table users
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Structure actuelle de la table 'users'</h2>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    echo "<br><a href='init_users_fixed.php' class='btn btn-primary'>Initialiser les utilisateurs</a>";
    
} catch (PDOException $e) {
    echo "Erreur: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Correction de la structure de la table users</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #0d6efd;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        .btn {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Correction de la structure de la table users</h1>
        <!-- Les messages PHP seront affichés ici -->
    </div>
</body>
</html>

// Script pour la récupération de mot de passe (reset_password.js)

document.addEventListener('DOMContentLoaded', () => {
    // Éléments du DOM
    const step1 = document.getElementById('step1');
    const step2 = document.getElementById('step2');
    const step3 = document.getElementById('step3');
    const requestForm = document.getElementById('request-form');
    const verifyForm = document.getElementById('verify-form');
    const resetForm = document.getElementById('reset-form');
    const resendCodeBtn = document.getElementById('resend-code');
    
    // Variables pour stocker les données entre les étapes
    let userEmail = '';
    let resetToken = '';
    
    // Gérer la soumission du formulaire de demande de réinitialisation
    requestForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        userEmail = document.getElementById('email').value;
        
        try {
            const response = await fetch('api/reset_password_api.php?action=request', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ email: userEmail })
            });
            
            const data = await response.json();
            
            if (data.success) {
                showAlert('Un code de réinitialisation a été envoyé à votre adresse e-mail.', 'success');
                
                // Passer à l'étape 2
                step1.classList.remove('active');
                step2.classList.add('active');
            } else {
                showAlert(data.message || 'Erreur lors de la demande de réinitialisation.', 'error');
            }
        } catch (error) {
            console.error('Erreur lors de la demande de réinitialisation:', error);
            showAlert('Erreur de connexion au serveur.', 'error');
        }
    });
    
    // Gérer la soumission du formulaire de vérification du code
    verifyForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const resetCode = document.getElementById('reset-code').value;
        
        try {
            const response = await fetch('api/reset_password_api.php?action=verify', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ 
                    email: userEmail,
                    reset_code: resetCode
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                showAlert('Code vérifié avec succès.', 'success');
                
                // Stocker le token pour l'étape suivante
                resetToken = data.reset_token;
                
                // Passer à l'étape 3
                step2.classList.remove('active');
                step3.classList.add('active');
            } else {
                showAlert(data.message || 'Code de réinitialisation invalide.', 'error');
            }
        } catch (error) {
            console.error('Erreur lors de la vérification du code:', error);
            showAlert('Erreur de connexion au serveur.', 'error');
        }
    });
    
    // Gérer le bouton de renvoi du code
    resendCodeBtn.addEventListener('click', async () => {
        try {
            const response = await fetch('api/reset_password_api.php?action=resend', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ email: userEmail })
            });
            
            const data = await response.json();
            
            if (data.success) {
                showAlert('Un nouveau code de réinitialisation a été envoyé à votre adresse e-mail.', 'success');
            } else {
                showAlert(data.message || 'Erreur lors du renvoi du code.', 'error');
            }
        } catch (error) {
            console.error('Erreur lors du renvoi du code:', error);
            showAlert('Erreur de connexion au serveur.', 'error');
        }
    });
    
    // Gérer la soumission du formulaire de réinitialisation du mot de passe
    resetForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const newPassword = document.getElementById('new-password').value;
        const confirmPassword = document.getElementById('confirm-password').value;
        
        // Vérifier que les mots de passe correspondent
        if (newPassword !== confirmPassword) {
            showAlert('Les mots de passe ne correspondent pas.', 'error');
            return;
        }
        
        // Vérifier que le mot de passe est assez long
        if (newPassword.length < 8) {
            showAlert('Le mot de passe doit contenir au moins 8 caractères.', 'error');
            return;
        }
        
        try {
            const response = await fetch('api/reset_password_api.php?action=reset', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ 
                    email: userEmail,
                    reset_token: resetToken,
                    new_password: newPassword
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                showAlert('Votre mot de passe a été réinitialisé avec succès. Vous allez être redirigé vers la page de connexion.', 'success');
                
                // Rediriger vers la page de connexion après 3 secondes
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 3000);
            } else {
                showAlert(data.message || 'Erreur lors de la réinitialisation du mot de passe.', 'error');
            }
        } catch (error) {
            console.error('Erreur lors de la réinitialisation du mot de passe:', error);
            showAlert('Erreur de connexion au serveur.', 'error');
        }
    });
    
    // Fonction pour afficher une alerte
    function showAlert(message, type) {
        const successAlert = document.getElementById('success-alert');
        const errorAlert = document.getElementById('error-alert');
        
        // Masquer les deux alertes
        successAlert.style.display = 'none';
        errorAlert.style.display = 'none';
        
        if (type === 'success') {
            successAlert.textContent = message;
            successAlert.style.display = 'block';
        } else {
            errorAlert.textContent = message;
            errorAlert.style.display = 'block';
        }
    }
});

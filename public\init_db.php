<?php
require_once('../app/config/config.php');

// Créer la base de données si elle n'existe pas
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";charset=utf8",
        DB_USER,
        DB_PASS
    );
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Créer la base de données
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `" . DB_NAME . "` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");

    echo "<p>Base de données créée avec succès</p>";

    // Se connecter à la base de données créée
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME . ";charset=utf8",
        DB_USER,
        DB_PASS
    );
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Créer les tables si elles n'existent pas
    createTables($pdo);

    // Insérer des exemples d'avocats
    insertExampleLawyers($pdo);

    // Insérer des exemples d'affaires
    insertExampleCases($pdo);

    // Créer un utilisateur administrateur par défaut
    createDefaultAdmin($pdo);

    echo "<p>Initialisation terminée avec succès!</p>";

} catch (PDOException $e) {
    die("<p>Erreur d'initialisation : " . $e->getMessage() . "</p>");
}

/**
 * Crée les tables nécessaires si elles n'existent pas
 */
function createTables($pdo) {
    // Table des avocats
    $pdo->exec("CREATE TABLE IF NOT EXISTS avocats (
        id INT PRIMARY KEY AUTO_INCREMENT,
        nom VARCHAR(100) NOT NULL,
        agrement VARCHAR(20) UNIQUE NOT NULL,
        adresse TEXT,
        telephone VARCHAR(20),
        email VARCHAR(100) UNIQUE NOT NULL,
        date_signature DATE NOT NULL,
        validite_contrat DATE NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");

    // Table des affaires
    $pdo->exec("CREATE TABLE IF NOT EXISTS affaires (
        id INT PRIMARY KEY AUTO_INCREMENT,
        numero VARCHAR(20) UNIQUE NOT NULL,
        nature ENUM('social', 'administrative', 'commerciale', 'foncier', 'refere', 'civile', 'penal') NOT NULL,
        objet TEXT NOT NULL,
        parties TEXT,
        juridiction ENUM('tribunal', 'cour', 'cours-supreme', 'c-etat') NOT NULL,
        engagement ENUM('societe', 'tiers') NOT NULL,
        date_engagement DATE NOT NULL,
        date_jugement DATE,
        statut ENUM('en-cours', 'jugee-faveur', 'jugee-tiers') NOT NULL,
        dispositif TEXT,
        montant DECIMAL(15,2),
        maitre_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (maitre_id) REFERENCES avocats(id)
    )");

    // Table des utilisateurs
    $pdo->exec("CREATE TABLE IF NOT EXISTS users (
        id INT PRIMARY KEY AUTO_INCREMENT,
        username VARCHAR(50) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        role ENUM('admin', 'user') NOT NULL DEFAULT 'user',
        last_login DATETIME,
        account_locked TINYINT(1) DEFAULT 0,
        locked_until DATETIME DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");

    // Table des journaux d'activité
    $pdo->exec("CREATE TABLE IF NOT EXISTS activity_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL,
        action VARCHAR(20) NOT NULL,
        entity_type VARCHAR(20) NOT NULL,
        entity_id INT,
        details TEXT,
        ip_address VARCHAR(45),
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");

    echo "<p>Tables créées avec succès</p>";
}

/**
 * Insère des exemples d'avocats dans la base de données
 */
function insertExampleLawyers($pdo) {
    // Vérifier si des avocats existent déjà
    $stmt = $pdo->query("SELECT COUNT(*) FROM avocats");
    $count = $stmt->fetchColumn();

    if ($count > 0) {
        echo "<p>Des avocats existent déjà dans la base de données</p>";
        return;
    }

    // Exemples d'avocats
    $avocats = [
        [
            'nom' => 'Maître Jean Dupont',
            'agrement' => 'AVT-2023-001',
            'adresse' => '15 Avenue des Avocats, Dakar',
            'telephone' => '+221 77 123 45 67',
            'email' => '<EMAIL>',
            'date_signature' => '2023-01-15',
            'validite_contrat' => '2024-01-15'
        ],
        [
            'nom' => 'Maître Marie Diallo',
            'agrement' => 'AVT-2023-002',
            'adresse' => '25 Rue de la Justice, Dakar',
            'telephone' => '+221 78 234 56 78',
            'email' => '<EMAIL>',
            'date_signature' => '2023-02-20',
            'validite_contrat' => '2024-02-20'
        ],
        [
            'nom' => 'Maître Amadou Sow',
            'agrement' => 'AVT-2022-015',
            'adresse' => '8 Boulevard de la République, Dakar',
            'telephone' => '+221 76 345 67 89',
            'email' => '<EMAIL>',
            'date_signature' => '2022-06-10',
            'validite_contrat' => '2023-06-10'
        ],
        [
            'nom' => 'Maître Fatou Ndiaye',
            'agrement' => 'AVT-2023-008',
            'adresse' => '42 Avenue Léopold Sédar Senghor, Dakar',
            'telephone' => '+221 70 456 78 90',
            'email' => '<EMAIL>',
            'date_signature' => '2023-04-05',
            'validite_contrat' => '2024-04-05'
        ],
        [
            'nom' => 'Maître Omar Diop',
            'agrement' => 'AVT-2021-023',
            'adresse' => '17 Rue des Magistrats, Dakar',
            'telephone' => '+221 77 567 89 01',
            'email' => '<EMAIL>',
            'date_signature' => '2021-11-30',
            'validite_contrat' => '2022-11-30'
        ]
    ];

    $stmt = $pdo->prepare("INSERT INTO avocats (nom, agrement, adresse, telephone, email, date_signature, validite_contrat)
                          VALUES (:nom, :agrement, :adresse, :telephone, :email, :date_signature, :validite_contrat)");

    foreach ($avocats as $avocat) {
        $stmt->execute($avocat);
    }

    echo "<p>" . count($avocats) . " exemples d'avocats insérés avec succès</p>";
}

/**
 * Insère des exemples d'affaires dans la base de données
 */
function insertExampleCases($pdo) {
    // Vérifier si des affaires existent déjà
    $stmt = $pdo->query("SELECT COUNT(*) FROM affaires");
    $count = $stmt->fetchColumn();

    if ($count > 0) {
        echo "<p>Des affaires existent déjà dans la base de données</p>";
        return;
    }

    // Récupérer les IDs des avocats
    $stmt = $pdo->query("SELECT id FROM avocats");
    $avocatIds = $stmt->fetchAll(PDO::FETCH_COLUMN);

    if (empty($avocatIds)) {
        echo "<p>Aucun avocat trouvé dans la base de données. Impossible d'ajouter des affaires.</p>";
        return;
    }

    // Exemples d'affaires
    $affaires = [
        [
            'numero' => 'AFF-2023-001',
            'nature' => 'commerciale',
            'objet' => 'Litige contractuel avec un fournisseur',
            'parties' => 'Société ABC vs Fournisseur XYZ',
            'juridiction' => 'tribunal',
            'engagement' => 'societe',
            'date_engagement' => '2023-01-20',
            'date_jugement' => null,
            'statut' => 'en-cours',
            'dispositif' => null,
            'montant' => 5000000.00,
            'maitre_id' => $avocatIds[0]
        ],
        [
            'numero' => 'AFF-2023-002',
            'nature' => 'social',
            'objet' => 'Licenciement contesté par un employé',
            'parties' => 'Société ABC vs M. Jean Samba',
            'juridiction' => 'tribunal',
            'engagement' => 'societe',
            'date_engagement' => '2023-02-15',
            'date_jugement' => '2023-06-30',
            'statut' => 'jugee-faveur',
            'dispositif' => 'Licenciement jugé conforme au droit du travail',
            'montant' => 2500000.00,
            'maitre_id' => $avocatIds[1]
        ],
        [
            'numero' => 'AFF-2022-015',
            'nature' => 'foncier',
            'objet' => 'Litige sur un terrain commercial',
            'parties' => 'Société ABC vs Entreprise DEF',
            'juridiction' => 'cour',
            'engagement' => 'tiers',
            'date_engagement' => '2022-08-10',
            'date_jugement' => '2023-03-15',
            'statut' => 'jugee-tiers',
            'dispositif' => 'Terrain attribué à l\'entreprise DEF',
            'montant' => 15000000.00,
            'maitre_id' => $avocatIds[2]
        ],
        [
            'numero' => 'AFF-2023-008',
            'nature' => 'administrative',
            'objet' => 'Contestation d\'une décision administrative',
            'parties' => 'Société ABC vs Administration fiscale',
            'juridiction' => 'c-etat',
            'engagement' => 'societe',
            'date_engagement' => '2023-04-20',
            'date_jugement' => null,
            'statut' => 'en-cours',
            'dispositif' => null,
            'montant' => 7500000.00,
            'maitre_id' => $avocatIds[3]
        ],
        [
            'numero' => 'AFF-2023-010',
            'nature' => 'civile',
            'objet' => 'Litige avec un client',
            'parties' => 'Société ABC vs Client GHI',
            'juridiction' => 'tribunal',
            'engagement' => 'societe',
            'date_engagement' => '2023-05-05',
            'date_jugement' => null,
            'statut' => 'en-cours',
            'dispositif' => null,
            'montant' => 3200000.00,
            'maitre_id' => $avocatIds[0]
        ],
        [
            'numero' => 'AFF-2022-022',
            'nature' => 'penal',
            'objet' => 'Plainte pour fraude',
            'parties' => 'Société ABC vs Ancien employé',
            'juridiction' => 'tribunal',
            'engagement' => 'societe',
            'date_engagement' => '2022-11-15',
            'date_jugement' => '2023-07-20',
            'statut' => 'jugee-faveur',
            'dispositif' => 'Condamnation de l\'ancien employé',
            'montant' => 4800000.00,
            'maitre_id' => $avocatIds[4]
        ]
    ];

    $stmt = $pdo->prepare("INSERT INTO affaires (numero, nature, objet, parties, juridiction, engagement,
                          date_engagement, date_jugement, statut, dispositif, montant, maitre_id)
                          VALUES (:numero, :nature, :objet, :parties, :juridiction, :engagement,
                          :date_engagement, :date_jugement, :statut, :dispositif, :montant, :maitre_id)");

    foreach ($affaires as $affaire) {
        $stmt->execute($affaire);
    }

    echo "<p>" . count($affaires) . " exemples d'affaires insérés avec succès</p>";
}

/**
 * Crée un utilisateur administrateur par défaut
 */
function createDefaultAdmin($pdo) {
    // Vérifier si des utilisateurs existent déjà
    $stmt = $pdo->query("SELECT COUNT(*) FROM users");
    $count = $stmt->fetchColumn();

    if ($count > 0) {
        echo "<p>Des utilisateurs existent déjà dans la base de données</p>";
        return;
    }

    // Créer un utilisateur administrateur par défaut
    $username = 'admin';
    $password = password_hash('admin123', PASSWORD_DEFAULT);
    $email = '<EMAIL>';
    $role = 'admin';

    $stmt = $pdo->prepare("INSERT INTO users (username, password, email, role) VALUES (:username, :password, :email, :role)");
    $stmt->execute([
        'username' => $username,
        'password' => $password,
        'email' => $email,
        'role' => $role
    ]);

    echo "<p>Utilisateur administrateur créé avec succès</p>";
    echo "<p>Identifiants par défaut : username = 'admin', password = 'admin123'</p>";
}


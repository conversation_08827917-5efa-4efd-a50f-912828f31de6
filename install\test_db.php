<?php
// Script de test de la connexion à la base de données MySQL
require_once 'config.php';

try {
    // Tenter de se connecter à la base de données
    $pdo = getDbConnection();
    
    // Vérifier si la table users existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    $tableExists = $stmt->rowCount() > 0;
    
    if ($tableExists) {
        // Compter le nombre d'utilisateurs
        $stmt = $pdo->query("SELECT COUNT(*) FROM users");
        $userCount = $stmt->fetchColumn();
        
        // Récupérer la liste des utilisateurs
        $stmt = $pdo->query("SELECT id, username, email, role, last_login FROM users");
        $users = $stmt->fetchAll();
    }
    
    // Vérifier si la table avocats existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'avocats'");
    $avocatsTableExists = $stmt->rowCount() > 0;
    
    if ($avocatsTableExists) {
        // Compter le nombre d'avocats
        $stmt = $pdo->query("SELECT COUNT(*) FROM avocats");
        $avocatsCount = $stmt->fetchColumn();
    }
    
    // Vérifier si la table affaires existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'affaires'");
    $affairesTableExists = $stmt->rowCount() > 0;
    
    if ($affairesTableExists) {
        // Compter le nombre d'affaires
        $stmt = $pdo->query("SELECT COUNT(*) FROM affaires");
        $affairesCount = $stmt->fetchColumn();
    }
    
    // Vérifier si la table activity_logs existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'activity_logs'");
    $logsTableExists = $stmt->rowCount() > 0;
    
    if ($logsTableExists) {
        // Compter le nombre d'entrées de journal
        $stmt = $pdo->query("SELECT COUNT(*) FROM activity_logs");
        $logsCount = $stmt->fetchColumn();
    }
    
} catch (PDOException $e) {
    $error = "Erreur de connexion à la base de données: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test de la base de données</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #0d6efd;
            margin-bottom: 20px;
        }
        .table {
            margin-top: 20px;
        }
        .alert {
            margin-top: 20px;
        }
        .btn {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test de la base de données</h1>
        
        <?php if (isset($error)): ?>
            <div class="alert alert-danger">
                <?php echo $error; ?>
            </div>
            <p>
                Assurez-vous que:
                <ul>
                    <li>MySQL est installé et en cours d'exécution</li>
                    <li>Les informations de connexion dans config.php sont correctes</li>
                    <li>La base de données a été créée avec le script init_db.php</li>
                </ul>
            </p>
            <a href="init_db.php" class="btn btn-primary">Initialiser la base de données</a>
        <?php else: ?>
            <div class="alert alert-success">
                Connexion à la base de données réussie!
            </div>
            
            <h2>État de la base de données</h2>
            <ul class="list-group">
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    Table 'users'
                    <?php if ($tableExists): ?>
                        <span class="badge bg-success rounded-pill"><?php echo $userCount; ?> utilisateurs</span>
                    <?php else: ?>
                        <span class="badge bg-danger rounded-pill">Non trouvée</span>
                    <?php endif; ?>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    Table 'avocats'
                    <?php if ($avocatsTableExists): ?>
                        <span class="badge bg-success rounded-pill"><?php echo $avocatsCount; ?> avocats</span>
                    <?php else: ?>
                        <span class="badge bg-danger rounded-pill">Non trouvée</span>
                    <?php endif; ?>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    Table 'affaires'
                    <?php if ($affairesTableExists): ?>
                        <span class="badge bg-success rounded-pill"><?php echo $affairesCount; ?> affaires</span>
                    <?php else: ?>
                        <span class="badge bg-danger rounded-pill">Non trouvée</span>
                    <?php endif; ?>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    Table 'activity_logs'
                    <?php if ($logsTableExists): ?>
                        <span class="badge bg-success rounded-pill"><?php echo $logsCount; ?> entrées</span>
                    <?php else: ?>
                        <span class="badge bg-danger rounded-pill">Non trouvée</span>
                    <?php endif; ?>
                </li>
            </ul>
            
            <?php if ($tableExists && $userCount > 0): ?>
                <h2 class="mt-4">Utilisateurs</h2>
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Nom d'utilisateur</th>
                            <th>Email</th>
                            <th>Rôle</th>
                            <th>Dernière connexion</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                            <tr>
                                <td><?php echo $user['id']; ?></td>
                                <td><?php echo $user['username']; ?></td>
                                <td><?php echo $user['email']; ?></td>
                                <td>
                                    <?php if ($user['role'] === 'admin'): ?>
                                        <span class="badge bg-danger">Administrateur</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Utilisateur</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo $user['last_login'] ? $user['last_login'] : 'Jamais'; ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
            
            <div class="d-flex justify-content-between mt-4">
                <a href="init_db.php" class="btn btn-warning">Réinitialiser la base de données</a>
                <a href="login.html" class="btn btn-primary">Aller à la page de connexion</a>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>

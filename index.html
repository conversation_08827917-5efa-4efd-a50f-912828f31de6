<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Suivi des Affaires Juridiques</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script>
        // Vérifier si l'utilisateur est connecté
        window.onload = function() {
            const currentUser = localStorage.getItem('currentUser');
            if (currentUser) {
                // Rediriger vers l'application principale
                window.location.href = 'app.html';
            } else {
                // Rediriger vers la page de connexion
                window.location.href = 'login.html';
            }
        };
    </script>
</head>
<body>
    <div class="container text-center mt-5">
        <h2>Redirection en cours...</h2>
        <div class="spinner-border text-primary mt-3" role="status">
            <span class="visually-hidden">Chargement...</span>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

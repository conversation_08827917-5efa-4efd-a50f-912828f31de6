// Script pour la gestion des utilisateurs (admin_users.js)

// Variables globales
let users = [];
let editingUserId = null;

// Vérifier si l'utilisateur est connecté et est administrateur
document.addEventListener('DOMContentLoaded', async () => {
    // Vérifier si l'utilisateur est connecté
    const currentUser = localStorage.getItem('currentUser');
    if (!currentUser) {
        // Rediriger vers la page de connexion si l'utilisateur n'est pas connecté
        window.location.href = 'login.html';
        return;
    }

    // Vérifier si l'utilisateur est administrateur
    const userData = JSON.parse(currentUser);
    if (userData.role !== 'admin') {
        // Rediriger vers la page principale si l'utilisateur n'est pas administrateur
        alert('Accès refusé. Seuls les administrateurs peuvent accéder à cette page.');
        window.location.href = 'app.html';
        return;
    }

    // Afficher les informations de l'utilisateur connecté
    const userInfoElement = document.getElementById('user-info');
    if (userInfoElement) {
        userInfoElement.textContent = `Connecté en tant que: ${userData.username}`;
    }

    // Gérer le bouton de déconnexion
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', () => {
            logout()
                .then(() => {
                    window.location.href = 'login.html';
                })
                .catch(error => {
                    console.error('Erreur lors de la déconnexion:', error);
                    window.location.href = 'login.html';
                });
        });
    }

    // Initialiser les composants Bootstrap
    const userModal = new bootstrap.Modal(document.getElementById('userModal'));
    const alertToast = new bootstrap.Toast(document.getElementById('alert-toast'));

    // Charger la liste des utilisateurs
    await loadUsers();

    // Gérer le bouton pour ajouter un nouvel utilisateur
    const btnNouvelUtilisateur = document.getElementById('btn-nouvel-utilisateur');
    btnNouvelUtilisateur.addEventListener('click', () => {
        // Réinitialiser le formulaire
        document.getElementById('user-form').reset();
        document.getElementById('userModalTitle').textContent = 'Nouvel Utilisateur';
        document.getElementById('password').required = true;
        document.getElementById('password-help').style.display = 'none';
        document.getElementById('user-id').value = '';
        editingUserId = null;
        
        // Afficher la modal
        userModal.show();
    });

    // Gérer le bouton d'enregistrement de l'utilisateur
    const saveUserBtn = document.getElementById('save-user');
    saveUserBtn.addEventListener('click', async () => {
        // Valider le formulaire
        const form = document.getElementById('user-form');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        // Récupérer les données du formulaire
        const username = document.getElementById('username').value;
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        const role = document.getElementById('role').value;
        const userId = document.getElementById('user-id').value;

        try {
            if (editingUserId) {
                // Mettre à jour un utilisateur existant
                await updateUser(editingUserId, username, email, password, role);
                showAlert('Utilisateur mis à jour avec succès', 'success');
            } else {
                // Créer un nouvel utilisateur
                await createUser(username, email, password, role);
                showAlert('Utilisateur créé avec succès', 'success');
            }

            // Fermer la modal et recharger la liste des utilisateurs
            userModal.hide();
            await loadUsers();
        } catch (error) {
            console.error('Erreur lors de l\'enregistrement de l\'utilisateur:', error);
            showAlert(error.message || 'Erreur lors de l\'enregistrement de l\'utilisateur', 'danger');
        }
    });
});

// Fonction pour charger la liste des utilisateurs
async function loadUsers() {
    try {
        const response = await fetch('api/users_api.php?action=list', {
            method: 'GET',
            credentials: 'include'
        });

        const data = await response.json();

        if (data.success) {
            users = data.users;
            displayUsers(users);
        } else {
            showAlert(data.message || 'Erreur lors du chargement des utilisateurs', 'danger');
        }
    } catch (error) {
        console.error('Erreur lors du chargement des utilisateurs:', error);
        showAlert('Erreur de connexion au serveur', 'danger');
    }
}

// Fonction pour afficher les utilisateurs dans le tableau
function displayUsers(usersList) {
    const usersListe = document.getElementById('users-liste');
    usersListe.innerHTML = '';

    if (usersList.length === 0) {
        const tr = document.createElement('tr');
        tr.innerHTML = '<td colspan="6" class="text-center">Aucun utilisateur trouvé</td>';
        usersListe.appendChild(tr);
        return;
    }

    usersList.forEach(user => {
        const tr = document.createElement('tr');
        
        // Formater la date de dernière connexion
        const lastLogin = user.last_login 
            ? new Date(user.last_login).toLocaleString('fr-FR') 
            : 'Jamais';

        // Créer le badge pour le rôle
        const roleBadge = user.role === 'admin' 
            ? '<span class="badge bg-danger">Administrateur</span>' 
            : '<span class="badge bg-secondary">Utilisateur</span>';

        tr.innerHTML = `
            <td>${user.id}</td>
            <td>${user.username}</td>
            <td>${user.email}</td>
            <td>${roleBadge}</td>
            <td>${lastLogin}</td>
            <td>
                <button class="btn btn-sm btn-warning edit-user" data-id="${user.id}">
                    <i class="bi bi-pencil"></i> Modifier
                </button>
                <button class="btn btn-sm btn-danger delete-user" data-id="${user.id}">
                    <i class="bi bi-trash"></i> Supprimer
                </button>
            </td>
        `;

        usersListe.appendChild(tr);
    });

    // Ajouter les écouteurs d'événements pour les boutons d'action
    document.querySelectorAll('.edit-user').forEach(btn => {
        btn.addEventListener('click', () => {
            const id = btn.getAttribute('data-id');
            editUser(id);
        });
    });

    document.querySelectorAll('.delete-user').forEach(btn => {
        btn.addEventListener('click', () => {
            const id = btn.getAttribute('data-id');
            deleteUser(id);
        });
    });
}

// Fonction pour éditer un utilisateur
function editUser(id) {
    const user = users.find(u => u.id == id);
    if (!user) {
        showAlert('Utilisateur non trouvé', 'danger');
        return;
    }

    // Remplir le formulaire avec les données de l'utilisateur
    document.getElementById('username').value = user.username;
    document.getElementById('email').value = user.email;
    document.getElementById('password').value = '';
    document.getElementById('password').required = false;
    document.getElementById('password-help').style.display = 'block';
    document.getElementById('role').value = user.role;
    document.getElementById('user-id').value = user.id;
    document.getElementById('userModalTitle').textContent = 'Modifier l\'Utilisateur';
    
    editingUserId = user.id;
    
    // Afficher la modal
    const userModal = new bootstrap.Modal(document.getElementById('userModal'));
    userModal.show();
}

// Fonction pour supprimer un utilisateur
async function deleteUser(id) {
    // Vérifier si l'utilisateur est l'utilisateur actuel
    const currentUser = JSON.parse(localStorage.getItem('currentUser'));
    if (currentUser.id == id) {
        showAlert('Vous ne pouvez pas supprimer votre propre compte', 'danger');
        return;
    }

    if (confirm('Êtes-vous sûr de vouloir supprimer cet utilisateur ?')) {
        try {
            const response = await fetch('api/users_api.php?action=delete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ user_id: id }),
                credentials: 'include'
            });

            const data = await response.json();

            if (data.success) {
                showAlert('Utilisateur supprimé avec succès', 'success');
                await loadUsers();
            } else {
                showAlert(data.message || 'Erreur lors de la suppression de l\'utilisateur', 'danger');
            }
        } catch (error) {
            console.error('Erreur lors de la suppression de l\'utilisateur:', error);
            showAlert('Erreur de connexion au serveur', 'danger');
        }
    }
}

// Fonction pour créer un nouvel utilisateur
async function createUser(username, email, password, role) {
    const response = await fetch('api/users_api.php?action=create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ username, email, password, role }),
        credentials: 'include'
    });

    const data = await response.json();

    if (!data.success) {
        throw new Error(data.message || 'Erreur lors de la création de l\'utilisateur');
    }

    return data;
}

// Fonction pour mettre à jour un utilisateur existant
async function updateUser(id, username, email, password, role) {
    const userData = {
        user_id: id,
        username,
        email,
        role
    };

    // Ajouter le mot de passe uniquement s'il est fourni
    if (password) {
        userData.password = password;
    }

    const response = await fetch('api/users_api.php?action=update', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(userData),
        credentials: 'include'
    });

    const data = await response.json();

    if (!data.success) {
        throw new Error(data.message || 'Erreur lors de la mise à jour de l\'utilisateur');
    }

    return data;
}

// Fonction pour afficher une alerte
function showAlert(message, type) {
    const alertToast = document.getElementById('alert-toast');
    const alertMessage = document.getElementById('alert-message');
    
    // Définir le message
    alertMessage.textContent = message;
    
    // Définir le type d'alerte
    alertToast.className = alertToast.className.replace(/bg-\w+/, `bg-${type}`);
    
    // Afficher l'alerte
    const toast = new bootstrap.Toast(alertToast);
    toast.show();
}

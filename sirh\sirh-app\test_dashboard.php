<?php

/**
 * Test simple du tableau de bord
 */

require_once __DIR__ . '/vendor/autoload.php';

// Charger l'application Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== TEST DU TABLEAU DE BORD ===\n\n";

try {
    // Test du composant Dashboard
    $dashboard = new App\Livewire\Dashboard();
    $dashboard->mount();
    
    echo "✅ Composant Dashboard chargé avec succès !\n";
    echo "Total travailleurs: {$dashboard->totalWorkers}\n";
    echo "Travailleurs actifs: {$dashboard->activeWorkers}\n";
    echo "Heures supplémentaires: {$dashboard->totalOvertimeHours}h\n";
    echo "Montant heures sup.: {$dashboard->totalOvertimePay}€\n";
    
    echo "\n=== DÉPARTEMENTS ===\n";
    foreach ($dashboard->departmentStats as $dept) {
        echo "- {$dept['department']}: {$dept['worker_count']} employés (taux moyen: {$dept['avg_rate']}€/h)\n";
    }
    
    echo "\n=== HEURES SUPPLÉMENTAIRES RÉCENTES ===\n";
    foreach ($dashboard->recentOvertimes as $overtime) {
        $worker = $overtime['worker'];
        echo "- {$worker['first_name']} {$worker['last_name']}: {$overtime['hours']}h ({$overtime['total_pay']}€) - {$overtime['status']}\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}

echo "\n🎉 Test terminé !\n";

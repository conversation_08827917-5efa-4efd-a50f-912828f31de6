<?php
// Script pour initialiser la base de données et créer un utilisateur administrateur par défaut

// Inclure les fichiers nécessaires
require_once 'config.php';

// Fonction pour exécuter un fichier SQL
function executeSqlFile($filePath) {
    global $DB_HOST, $DB_PORT, $DB_USER, $DB_PASS;

    // Récupérer les constantes de configuration
    $DB_HOST = defined('DB_HOST') ? DB_HOST : 'localhost';
    $DB_PORT = defined('DB_PORT') ? DB_PORT : '3306';
    $DB_USER = defined('DB_USER') ? DB_USER : 'root';
    $DB_PASS = defined('DB_PASS') ? DB_PASS : '';
    $DB_NAME = defined('DB_NAME') ? DB_NAME : 'suivi_affaires_juridiques';

    try {
        // Lire le contenu du fichier SQL
        $sql = file_get_contents($filePath);
        if (!$sql) {
            throw new Exception("Impossible de lire le fichier SQL: $filePath");
        }

        // Connexion à MySQL avec la base de données spécifiée
        $pdo = new PDO(
            "mysql:host=" . $DB_HOST . ";port=" . $DB_PORT . ";dbname=" . $DB_NAME . ";charset=utf8",
            $DB_USER,
            $DB_PASS
        );
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Exécuter les requêtes SQL
        $pdo->exec($sql);

        echo "Le fichier SQL $filePath a été exécuté avec succès.<br>";
        return true;
    } catch (PDOException $e) {
        echo "Erreur lors de l'exécution du fichier SQL $filePath: " . $e->getMessage() . "<br>";
        return false;
    } catch (Exception $e) {
        echo "Erreur: " . $e->getMessage() . "<br>";
        return false;
    }
}

// Fonction pour créer un utilisateur administrateur par défaut
function createAdminUser() {
    global $DB_HOST, $DB_PORT, $DB_USER, $DB_PASS;

    // Récupérer les constantes de configuration
    $DB_HOST = defined('DB_HOST') ? DB_HOST : 'localhost';
    $DB_PORT = defined('DB_PORT') ? DB_PORT : '3306';
    $DB_USER = defined('DB_USER') ? DB_USER : 'root';
    $DB_PASS = defined('DB_PASS') ? DB_PASS : '';
    $DB_NAME = defined('DB_NAME') ? DB_NAME : 'suivi_affaires_juridiques';

    try {
        // Connexion à la base de données
        $pdo = new PDO(
            "mysql:host=" . $DB_HOST . ";port=" . $DB_PORT . ";dbname=" . $DB_NAME . ";charset=utf8",
            $DB_USER,
            $DB_PASS
        );
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Vérifier si un utilisateur admin existe déjà
        $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE role = 'admin'");
        $adminCount = $stmt->fetchColumn();

        if ($adminCount > 0) {
            echo "Un utilisateur administrateur existe déjà.<br>";
            return true;
        }

        // Créer un utilisateur administrateur par défaut
        $username = 'admin';
        $password = 'password';
        $email = '<EMAIL>';
        $role = 'admin';

        // Hasher le mot de passe
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

        // Insérer l'utilisateur
        $stmt = $pdo->prepare("INSERT INTO users (username, password_hash, email, role) VALUES (?, ?, ?, ?)");
        $stmt->execute([$username, $hashedPassword, $email, $role]);

        echo "Utilisateur administrateur créé avec succès.<br>";
        echo "Nom d'utilisateur: $username<br>";
        echo "Mot de passe: $password<br>";

        return true;
    } catch (PDOException $e) {
        echo "Erreur lors de la création de l'utilisateur administrateur: " . $e->getMessage() . "<br>";
        return false;
    }
}

// Fonction pour créer la base de données si elle n'existe pas
function createDatabaseIfNotExists() {
    global $DB_HOST, $DB_PORT, $DB_USER, $DB_PASS;

    // Récupérer les constantes de configuration
    $DB_HOST = defined('DB_HOST') ? DB_HOST : 'localhost';
    $DB_PORT = defined('DB_PORT') ? DB_PORT : '3306';
    $DB_USER = defined('DB_USER') ? DB_USER : 'root';
    $DB_PASS = defined('DB_PASS') ? DB_PASS : '';
    $DB_NAME = defined('DB_NAME') ? DB_NAME : 'suivi_affaires_juridiques';

    try {
        // Connexion à MySQL sans sélectionner de base de données
        $pdo = new PDO(
            "mysql:host=" . $DB_HOST . ";port=" . $DB_PORT . ";charset=utf8",
            $DB_USER,
            $DB_PASS
        );
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Créer la base de données si elle n'existe pas
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$DB_NAME` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");

        echo "Base de données '$DB_NAME' créée ou déjà existante.<br>";
        return true;
    } catch (PDOException $e) {
        echo "Erreur lors de la création de la base de données: " . $e->getMessage() . "<br>";
        return false;
    }
}

// Exécuter le script d'initialisation
echo "<h1>Initialisation de la base de données</h1>";

// Créer la base de données si elle n'existe pas
if (createDatabaseIfNotExists()) {
    // Exécuter le fichier schema.sql
    if (executeSqlFile('database/schema.sql')) {
        // Créer un utilisateur administrateur par défaut
        createAdminUser();

        echo "<h2>Initialisation terminée avec succès!</h2>";
        echo "<p>Vous pouvez maintenant <a href='login.html'>vous connecter</a> à l'application.</p>";
    } else {
        echo "<h2>Erreur lors de l'initialisation du schéma de la base de données.</h2>";
        echo "<p>Veuillez vérifier les messages d'erreur ci-dessus et réessayer.</p>";
    }
} else {
    echo "<h2>Erreur lors de la création de la base de données.</h2>";
    echo "<p>Veuillez vérifier les messages d'erreur ci-dessus et réessayer.</p>";
}
?>

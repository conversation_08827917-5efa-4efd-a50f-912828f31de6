# Activer le module de réécriture d'URL
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /

    # Rediriger vers HTTPS si disponible
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

    # Empêcher l'accès direct aux fichiers .php dans le répertoire api/
    RewriteCond %{REQUEST_URI} ^/api/.*\.php$
    RewriteCond %{REQUEST_METHOD} !POST
    RewriteRule ^ - [F]

    # Empêcher l'accès direct aux fichiers .sql
    RewriteRule \.sql$ - [F]

    # Rediriger toutes les requêtes vers index.php sauf pour les fichiers existants
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)$ index.php?page=$1 [QSA,L]
</IfModule>

# Définir le fuseau horaire par défaut
<IfModule mod_php7.c>
    php_value date.timezone "Europe/Paris"
</IfModule>

# Compression Gzip
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/x-javascript application/json
</IfModule>

# Mise en cache des fichiers statiques
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# Protection des fichiers sensibles
<FilesMatch "^\.(htaccess|htpasswd|ini|log|sh|inc|bak)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>
<?php
/**
 * Script de test pour la connexion à la base de données
 */

// Activer l'affichage des erreurs
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Définir le chemin de base de l'application
define('BASE_PATH', dirname(__DIR__));

// Afficher l'en-tête HTML
echo "<!DOCTYPE html>
<html>
<head>
    <title>Test de connexion à la base de données</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        h1 {
            color: #333;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class='container'>
        <h1>Test de connexion à la base de données</h1>";

try {
    // Paramètres de connexion à la base de données
    $host = 'localhost';
    $dbname = 'suivi_affaires_juridiques';
    $username = 'root';
    $password = '';
    
    echo "<h2>Paramètres de connexion</h2>";
    echo "<pre>";
    echo "Host: {$host}\n";
    echo "Database: {$dbname}\n";
    echo "Username: {$username}\n";
    echo "Password: " . str_repeat('*', strlen($password)) . "\n";
    echo "</pre>";
    
    // Tenter de se connecter à la base de données
    echo "<h2>Tentative de connexion</h2>";
    
    $pdo = new PDO("mysql:host={$host};dbname={$dbname};charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p class='success'>Connexion à la base de données réussie!</p>";
    
    // Vérifier la structure de la table users
    echo "<h2>Structure de la table users</h2>";
    
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<pre>";
    print_r($columns);
    echo "</pre>";
    
    // Vérifier si la table users contient des données
    echo "<h2>Données de la table users</h2>";
    
    $stmt = $pdo->query("SELECT * FROM users LIMIT 5");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($users) > 0) {
        echo "<pre>";
        // Masquer les mots de passe
        foreach ($users as &$user) {
            if (isset($user['password'])) {
                $user['password'] = str_repeat('*', strlen($user['password']));
            }
            if (isset($user['password_hash'])) {
                $user['password_hash'] = str_repeat('*', strlen($user['password_hash']));
            }
        }
        print_r($users);
        echo "</pre>";
    } else {
        echo "<p class='error'>Aucun utilisateur trouvé dans la base de données.</p>";
    }
    
    // Vérifier les autres tables
    $tables = ['affaires', 'avocats', 'activity_logs', 'login_attempts'];
    
    foreach ($tables as $table) {
        echo "<h2>Table {$table}</h2>";
        
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM {$table}");
            $count = $stmt->fetchColumn();
            
            echo "<p>Nombre d'enregistrements: {$count}</p>";
        } catch (PDOException $e) {
            echo "<p class='error'>Erreur lors de la vérification de la table {$table}: " . $e->getMessage() . "</p>";
        }
    }
} catch (PDOException $e) {
    echo "<p class='error'>Erreur de connexion à la base de données: " . $e->getMessage() . "</p>";
}

// Afficher le pied de page HTML
echo "    </div>
</body>
</html>";

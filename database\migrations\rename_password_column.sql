-- Script de migration pour renommer la colonne password en password_hash
-- Exécuter ce script pour mettre à jour la structure de la base de données

-- Vérifier si la colonne password existe
SET @exists := (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = 'suivi_affaires_juridiques' 
                AND TABLE_NAME = 'users' 
                AND COLUMN_NAME = 'password');

-- Si la colonne password existe, la renommer en password_hash
SET @query = IF(@exists > 0, 
                'ALTER TABLE users CHANGE COLUMN password password_hash VARCHAR(255) NOT NULL',
                'SELECT "La colonne password n\'existe pas, aucune action nécessaire" AS message');

-- Exécuter la requête
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

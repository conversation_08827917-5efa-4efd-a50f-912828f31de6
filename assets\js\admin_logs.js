// Script pour la gestion du journal d'activité (admin_logs.js)

// Variables globales
let logs = [];
let users = [];
let currentPage = 1;
let logsPerPage = 20;
let totalPages = 1;
let filters = {
    username: '',
    action: '',
    entity_type: '',
    date: ''
};

// Vérifier si l'utilisateur est connecté et est administrateur
document.addEventListener('DOMContentLoaded', async () => {
    try {
        // Masquer le conteneur d'erreur au chargement
        document.getElementById('error-container').style.display = 'none';

        // Vérifier si l'utilisateur est connecté
        const currentUser = localStorage.getItem('currentUser');
        if (!currentUser) {
            // Rediriger vers la page de connexion si l'utilisateur n'est pas connecté
            window.location.href = '../../views/auth/login.html';
            return;
        }

        // Vérifier si l'utilisateur est administrateur
        const userData = JSON.parse(currentUser);
        if (userData.role !== 'admin') {
            // Rediriger vers la page principale si l'utilisateur n'est pas administrateur
            alert('Accès refusé. Seuls les administrateurs peuvent accéder à cette page.');
            window.location.href = '../../views/app/app.html';
            return;
        }

        // Ajouter les gestionnaires d'événements pour les boutons d'erreur
        document.getElementById('btn-reinit-db').addEventListener('click', async () => {
            if (confirm('Êtes-vous sûr de vouloir réinitialiser la base de données ? Cette action est irréversible.')) {
                try {
                    // Appeler l'API pour réinitialiser la base de données
                    const response = await fetch('../../app/api/db_api.php?action=reset', {
                        method: 'POST',
                        credentials: 'include'
                    });

                    const data = await response.json();

                    if (data.success) {
                        showAlert('Base de données réinitialisée avec succès', 'success');
                        // Recharger la page après 2 secondes
                        setTimeout(() => {
                            window.location.reload();
                        }, 2000);
                    } else {
                        showAlert(data.message || 'Erreur lors de la réinitialisation de la base de données', 'danger');
                    }
                } catch (error) {
                    console.error('Erreur lors de la réinitialisation de la base de données:', error);
                    showAlert('Erreur de connexion au serveur', 'danger');
                }
            }
        });

        document.getElementById('btn-refresh-page').addEventListener('click', () => {
            window.location.reload();
        });

    // Afficher les informations de l'utilisateur connecté
    const userInfoElement = document.getElementById('user-info');
    if (userInfoElement) {
        userInfoElement.textContent = `Connecté en tant que: ${userData.username}`;
    }

    // Gérer le bouton de déconnexion
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', () => {
            logout()
                .then(() => {
                    window.location.href = '../../views/auth/login.html';
                })
                .catch(error => {
                    console.error('Erreur lors de la déconnexion:', error);
                    window.location.href = '../../views/auth/login.html';
                });
        });
    }

    // Charger la liste des utilisateurs pour le filtre
    await loadUsers();

    // Charger les logs
    await loadLogs();

    // Gérer le bouton d'actualisation des logs
    const btnRefreshLogs = document.getElementById('btn-refresh-logs');
    btnRefreshLogs.addEventListener('click', async () => {
        await loadLogs();
        showAlert('Journal d\'activité actualisé', 'success');
    });

    // Gérer le bouton de vidage du journal
    const btnClearLogs = document.getElementById('btn-clear-logs');
    btnClearLogs.addEventListener('click', async () => {
        if (confirm('Êtes-vous sûr de vouloir vider le journal d\'activité ? Cette action est irréversible.')) {
            await clearLogs();
        }
    });

    // Gérer le bouton d'application des filtres
    const btnApplyFilters = document.getElementById('btn-apply-filters');
    btnApplyFilters.addEventListener('click', async () => {
        // Récupérer les valeurs des filtres
        filters.username = document.getElementById('filter-user').value;
        filters.action = document.getElementById('filter-action').value;
        filters.entity_type = document.getElementById('filter-entity').value;
        filters.date = document.getElementById('filter-date').value;

        // Réinitialiser la pagination
        currentPage = 1;

        // Recharger les logs avec les filtres
        await loadLogs();
    });

    // Gérer le bouton de réinitialisation des filtres
    const btnResetFilters = document.getElementById('btn-reset-filters');
    btnResetFilters.addEventListener('click', async () => {
        // Réinitialiser les filtres
        document.getElementById('filter-user').value = '';
        document.getElementById('filter-action').value = '';
        document.getElementById('filter-entity').value = '';
        document.getElementById('filter-date').value = '';

        // Réinitialiser les variables de filtres
        filters = {
            username: '',
            action: '',
            entity_type: '',
            date: ''
        };

        // Réinitialiser la pagination
        currentPage = 1;

        // Recharger les logs sans filtres
        await loadLogs();
    });
});

// Fonction pour charger la liste des utilisateurs
async function loadUsers() {
    try {
        const usersUrl = '../../simple_users_api.php?action=list';
        console.log('URL pour charger les utilisateurs:', usersUrl);

        let data;
        try {
            const response = await fetch(usersUrl, {
                method: 'GET',
                credentials: 'include',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            });

            // Vérifier si la réponse est OK
            if (!response.ok) {
                throw new Error(`Erreur HTTP: ${response.status} ${response.statusText}`);
            }

            // Vérifier le type de contenu
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                // Afficher la réponse brute pour le débogage
                const responseText = await response.text();
                console.error('Réponse non-JSON reçue:', responseText);
                throw new Error('Réponse non-JSON reçue de l\'API');
            }

            // Parser la réponse JSON
            data = await response.json();
            console.log('Données JSON parsées (utilisateurs):', data);
        } catch (e) {
            console.error('Erreur lors de la requête API utilisateurs:', e);
            throw e;
        }

        if (data.success) {
            users = data.users;
            populateUserFilter(users);
        } else {
            showAlert(data.message || 'Erreur lors du chargement des utilisateurs', 'danger');
        }
    } catch (error) {
        console.error('Erreur lors du chargement des utilisateurs:', error);
        showAlert('Erreur de connexion au serveur', 'danger');
    }
}

// Fonction pour remplir le filtre des utilisateurs
function populateUserFilter(usersList) {
    const filterUser = document.getElementById('filter-user');
    filterUser.innerHTML = '<option value="">Tous</option>';

    usersList.forEach(user => {
        const option = document.createElement('option');
        option.value = user.username;
        option.textContent = user.username;
        filterUser.appendChild(option);
    });
}

// Fonction pour charger les logs
async function loadLogs() {
    try {
        // Masquer le conteneur d'erreur
        document.getElementById('error-container').style.display = 'none';

        // Construire l'URL avec les paramètres de filtrage et de pagination
        // Utiliser l'API simplifiée
        let url = `../../simple_logs_api.php?action=list&page=${currentPage}&limit=${logsPerPage}`;
        console.log('URL de l\'API:', url);

        // Ajouter les filtres à l'URL
        if (filters.username) url += `&username=${encodeURIComponent(filters.username)}`;
        if (filters.action) url += `&action=${encodeURIComponent(filters.action)}`;
        if (filters.entity_type) url += `&entity_type=${encodeURIComponent(filters.entity_type)}`;
        if (filters.date) url += `&date=${encodeURIComponent(filters.date)}`;

        let data;
        try {
            const response = await fetch(url, {
                method: 'GET',
                credentials: 'include',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            });

            // Vérifier si la réponse est OK
            if (!response.ok) {
                throw new Error(`Erreur HTTP: ${response.status} ${response.statusText}`);
            }

            // Vérifier le type de contenu
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                // Afficher la réponse brute pour le débogage
                const responseText = await response.text();
                console.error('Réponse non-JSON reçue:', responseText);
                throw new Error('Réponse non-JSON reçue de l\'API');
            }

            // Parser la réponse JSON
            data = await response.json();
            console.log('Données JSON parsées:', data);
        } catch (e) {
            console.error('Erreur lors de la requête API:', e);
            throw e;
        }

        if (data && data.success) {
            logs = data.logs;
            totalPages = Math.ceil(data.total / logsPerPage);
            displayLogs(logs);
            displayPagination();
            updateLogsCount(data.total);
        } else {
            // Afficher le message d'erreur dans le conteneur d'erreur
            document.getElementById('error-message').textContent = data.message || 'Erreur lors du chargement des logs';
            document.getElementById('error-container').style.display = 'block';

            // Afficher un message dans le tableau
            const logsListe = document.getElementById('logs-liste');
            logsListe.innerHTML = '<tr><td colspan="8" class="text-center text-danger">Impossible de charger les journaux d\'activité</td></tr>';

            // Mettre à jour le compteur
            updateLogsCount(0);
        }
    } catch (error) {
        console.error('Erreur lors du chargement des logs:', error);

        // Afficher le message d'erreur dans le conteneur d'erreur
        document.getElementById('error-message').textContent = 'Erreur de connexion au serveur. Veuillez réessayer.';
        document.getElementById('error-container').style.display = 'block';

        // Afficher un message dans le tableau
        const logsListe = document.getElementById('logs-liste');
        logsListe.innerHTML = '<tr><td colspan="8" class="text-center text-danger">Impossible de charger les journaux d\'activité</td></tr>';

        // Mettre à jour le compteur
        updateLogsCount(0);
    }
}

// Fonction pour afficher les logs dans le tableau
function displayLogs(logsList) {
    console.log('Affichage des logs:', logsList);

    const logsListe = document.getElementById('logs-liste');
    logsListe.innerHTML = '';

    // Vérifier si logsList est un tableau
    if (!Array.isArray(logsList)) {
        console.error('logsList n\'est pas un tableau:', logsList);
        const tr = document.createElement('tr');
        tr.innerHTML = '<td colspan="8" class="text-center text-danger">Erreur: Format de données incorrect</td>';
        logsListe.appendChild(tr);
        return;
    }

    if (logsList.length === 0) {
        const tr = document.createElement('tr');
        tr.innerHTML = '<td colspan="8" class="text-center">Aucune activité trouvée</td>';
        logsListe.appendChild(tr);
        return;
    }

    logsList.forEach(log => {
        console.log('Traitement du log:', log);
        const tr = document.createElement('tr');

        // Vérifier si le log a toutes les propriétés nécessaires
        if (!log || !log.timestamp) {
            console.error('Log invalide:', log);
            return;
        }

        // Formater la date et l'heure
        let timestamp;
        try {
            timestamp = new Date(log.timestamp).toLocaleString('fr-FR');
        } catch (e) {
            console.error('Erreur lors du formatage de la date:', e);
            timestamp = 'Date invalide';
        }

        // Formater l'action
        let actionBadge = '';
        switch (log.action) {
            case 'create':
                actionBadge = '<span class="badge bg-success">Création</span>';
                break;
            case 'update':
                actionBadge = '<span class="badge bg-warning text-dark">Modification</span>';
                break;
            case 'delete':
                actionBadge = '<span class="badge bg-danger">Suppression</span>';
                break;
            case 'login':
                actionBadge = '<span class="badge bg-info">Connexion</span>';
                break;
            case 'logout':
                actionBadge = '<span class="badge bg-secondary">Déconnexion</span>';
                break;
            case 'reset':
                actionBadge = '<span class="badge bg-dark">Réinitialisation</span>';
                break;
            default:
                actionBadge = `<span class="badge bg-light text-dark">${log.action}</span>`;
        }

        // Formater l'entité
        let entityBadge = '';
        switch (log.entity_type) {
            case 'user':
                entityBadge = '<span class="badge bg-primary">Utilisateur</span>';
                break;
            case 'avocat':
                entityBadge = '<span class="badge bg-success">Avocat</span>';
                break;
            case 'affaire':
                entityBadge = '<span class="badge bg-warning text-dark">Affaire</span>';
                break;
            case 'database':
                entityBadge = '<span class="badge bg-danger">Base de données</span>';
                break;
            default:
                entityBadge = `<span class="badge bg-light text-dark">${log.entity_type}</span>`;
        }

        tr.innerHTML = `
            <td>${log.id}</td>
            <td>${timestamp}</td>
            <td>${log.username}</td>
            <td>${actionBadge}</td>
            <td>${entityBadge}</td>
            <td>${log.entity_id || '-'}</td>
            <td>${log.details || '-'}</td>
            <td>${log.ip_address}</td>
        `;

        logsListe.appendChild(tr);
    });
}

// Fonction pour afficher la pagination
function displayPagination() {
    const pagination = document.getElementById('pagination');
    pagination.innerHTML = '';

    // Bouton précédent
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" data-page="${currentPage - 1}">Précédent</a>`;
    pagination.appendChild(prevLi);

    // Pages
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, startPage + 4);

    for (let i = startPage; i <= endPage; i++) {
        const pageLi = document.createElement('li');
        pageLi.className = `page-item ${i === currentPage ? 'active' : ''}`;
        pageLi.innerHTML = `<a class="page-link" href="#" data-page="${i}">${i}</a>`;
        pagination.appendChild(pageLi);
    }

    // Bouton suivant
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" data-page="${currentPage + 1}">Suivant</a>`;
    pagination.appendChild(nextLi);

    // Ajouter les écouteurs d'événements pour les liens de pagination
    document.querySelectorAll('.page-link').forEach(link => {
        link.addEventListener('click', async (e) => {
            e.preventDefault();
            if (link.parentElement.classList.contains('disabled')) return;

            const page = parseInt(link.getAttribute('data-page'));
            if (page !== currentPage) {
                currentPage = page;
                await loadLogs();
            }
        });
    });
}

// Fonction pour mettre à jour le compteur de logs
function updateLogsCount(total) {
    const logsCount = document.getElementById('logs-count');
    logsCount.textContent = `${total} activité${total > 1 ? 's' : ''} trouvée${total > 1 ? 's' : ''}`;
}

// Fonction pour vider le journal d'activité
async function clearLogs() {
    try {
        const clearUrl = '../../api/logs_api.php?action=clear';
        console.log('URL pour vider les logs:', clearUrl);

        let data;
        try {
            const response = await fetch(clearUrl, {
                method: 'POST',
                credentials: 'include',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            });

            // Vérifier si la réponse est OK
            if (!response.ok) {
                throw new Error(`Erreur HTTP: ${response.status} ${response.statusText}`);
            }

            // Vérifier le type de contenu
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                // Afficher la réponse brute pour le débogage
                const responseText = await response.text();
                console.error('Réponse non-JSON reçue:', responseText);
                throw new Error('Réponse non-JSON reçue de l\'API');
            }

            // Parser la réponse JSON
            data = await response.json();
            console.log('Données JSON parsées (clear logs):', data);
        } catch (e) {
            console.error('Erreur lors de la requête API pour vider les logs:', e);
            throw e;
        }

        if (data.success) {
            showAlert('Journal d\'activité vidé avec succès', 'success');
            await loadLogs();
        } else {
            showAlert(data.message || 'Erreur lors du vidage du journal', 'danger');
        }
    } catch (error) {
        console.error('Erreur lors du vidage du journal:', error);
        showAlert('Erreur de connexion au serveur', 'danger');
    }
}

// Fonction pour afficher une alerte
function showAlert(message, type) {
    const alertToast = document.getElementById('alert-toast');
    const alertMessage = document.getElementById('alert-message');

    // Définir le message
    alertMessage.textContent = message;

    // Définir le type d'alerte
    alertToast.className = alertToast.className.replace(/bg-\w+/, `bg-${type}`);

    // Afficher l'alerte
    const toast = new bootstrap.Toast(alertToast);
    toast.show();
}
